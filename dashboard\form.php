<?php
require_once '../db_connect.php';
require_once 'auth.php';
require_once '../inc/SecureIdHandler.php';

// === معالجة طلب البحث عن الرخصة عبر AJAX ===
if (isset($_GET['action']) && $_GET['action'] === 'lookup_license' && isset($_GET['license_number'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    $license_number = trim($_GET['license_number']);
    if (empty($license_number)) {
        echo json_encode(['found' => false]);
        exit;
    }

    try {
        $stmt = $pdo->prepare("SELECT amanah, baladiah, place_name, place_number, imgLogo FROM certificates WHERE license_number = ?");
        $stmt->execute([$license_number]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            $logoBase64 = '';
            if (!empty($result['imgLogo'])) {
                $logoBase64 = $result['imgLogo'];
            }
            echo json_encode([
                'found' => true,
                'amanah' => $result['amanah'],
                'baladiah' => $result['baladiah'],
                'placeName' => $result['place_name'],
                'placeNumber' => $result['place_number'],
                'logo' => $logoBase64
            ]);
        } else {
            echo json_encode(['found' => false]);
        }
    } catch (Exception $e) {
        echo json_encode(['found' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// التحقق من وجود معرف التعديل
$encrypted_edit_id = isset($_GET['id']) ? $_GET['id'] : '';
$certificate = null;

if (!empty($encrypted_edit_id)) {
    try {
        $edit_id = SecureIdHandler::decryptId($encrypted_edit_id);
        if ($edit_id === false) {
            $error = "معرف الشهادة غير صالح";
        } else {
            $stmt = $pdo->prepare("SELECT * FROM certificates WHERE id_number = ?");
            $stmt->execute([$edit_id]);
            $certificate = $stmt->fetch();
        }
    } catch(PDOException $e) {
        $error = "خطأ في جلب البيانات: " . $e->getMessage();
    }
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $data = [
            'id_number' => $_POST['idNumber'],
            'person_name' => $_POST['personName'],
            'nationality' => $_POST['nationality'],
            'job' => $_POST['job'],
            'gender' => $_POST['gender'],
            'amanah' => $_POST['amanah'],
            'baladiah' => $_POST['baladiah'],
            'license_number' => $_POST['licenseNumber'],
            'place_name' => $_POST['placeName'],
            'place_number' => $_POST['placeNumber'],
            'cert_number' => $_POST['certNumber'],
            'issue_date_hijri' => $_POST['issueDateHijri'],
            'issue_date' => $_POST['issueDate'],
            'expire_date_hijri' => $_POST['expireDateHijri'],
            'expire_date' => $_POST['expireDate'],
            'program_type' => $_POST['programType'],
            'program_end' => $_POST['programEnd'],
            'first_data' => $_POST['first_Data'],
            'data_done' => $_POST['Data_done'],
            'linkWeb' => $_POST['linkWeb'] ?? ''
        ];

        // معالجة الملفات
        $photo_path = null;
        $qr_path = null;
        $logo_path = null;

        if (isset($_FILES['photoFile']) && $_FILES['photoFile']['error'] === UPLOAD_ERR_OK) {
            $photo_path = 'data:image/jpeg;base64,' . base64_encode(file_get_contents($_FILES['photoFile']['tmp_name']));
        }

        if (isset($_FILES['qrFile']) && $_FILES['qrFile']['error'] === UPLOAD_ERR_OK) {
            $qr_path = 'data:image/png;base64,' . base64_encode(file_get_contents($_FILES['qrFile']['tmp_name']));
        }

        // === منطق حفظ الشعار: أولًا الملف المرفوع، ثانيًا الشعار المجلوب ===
        if (isset($_FILES['logoFile']) && $_FILES['logoFile']['error'] === UPLOAD_ERR_OK) {
            // رفع يدوي جديد
            $logo_path = 'data:image/png;base64,' . base64_encode(file_get_contents($_FILES['logoFile']['tmp_name']));
        } elseif (!empty($_POST['fetchedLogo'])) {
            // شعار مسترجع من رخصة موجودة
            $logo_path = $_POST['fetchedLogo'];
        }
        // إذا لم يُرفع جديد ولم يُجلب → $logo_path يبقى null

        // التحقق من تكرار رقم الهوية
        $check_stmt = $pdo->prepare("SELECT id FROM certificates WHERE id_number = ? AND id != ?");
        $check_stmt->execute([$data['id_number'], $certificate['id'] ?? 0]);
        if ($check_stmt->fetch()) {
            throw new Exception("خطأ: رقم الهوية هذا موجود بالفعل. يرجى إدخال رقم هوية فريد.");
        }

        if ($certificate) {
            // === التحديث ===
            $sql = "UPDATE certificates SET
                person_name = :person_name,
                nationality = :nationality,
                job = :job,
                gender = :gender,
                amanah = :amanah,
                baladiah = :baladiah,
                license_number = :license_number,
                place_name = :place_name,
                place_number = :place_number,
                cert_number = :cert_number,
                issue_date_hijri = :issue_date_hijri,
                issue_date = :issue_date,
                expire_date_hijri = :expire_date_hijri,
                expire_date = :expire_date,
                program_type = :program_type,
                program_end = :program_end,
                first_data = :first_data,
                data_done = :data_done,
                linkWeb = :linkWeb";

            if ($photo_path !== null) $sql .= ", photo = :photo";
            if ($qr_path !== null) $sql .= ", qr = :qr";
            if ($logo_path !== null) $sql .= ", imgLogo = :imgLogo";

            $sql .= " WHERE id_number = :id_number";
            $stmt = $pdo->prepare($sql);

            foreach ($data as $key => $value) {
                $stmt->bindValue(":$key", $value);
            }
            if ($photo_path !== null) $stmt->bindValue(':photo', $photo_path, PDO::PARAM_STR);
            if ($qr_path !== null) $stmt->bindValue(':qr', $qr_path, PDO::PARAM_STR);
            if ($logo_path !== null) $stmt->bindValue(':imgLogo', $logo_path, PDO::PARAM_STR);

            $stmt->execute();
            $message = "تم تحديث الشهادة بنجاح!";

        } else {
            // === الإدخال الجديد ===
            $sql = "INSERT INTO certificates (
                id_number, person_name, nationality, job, gender, amanah, baladiah,
                license_number, place_name, place_number, cert_number, issue_date_hijri,
                issue_date, expire_date_hijri, expire_date, program_type, program_end,
                first_data, data_done, photo, qr, imgLogo, linkWeb
            ) VALUES (
                :id_number, :person_name, :nationality, :job, :gender, :amanah, :baladiah,
                :license_number, :place_name, :place_number, :cert_number, :issue_date_hijri,
                :issue_date, :expire_date_hijri, :expire_date, :program_type, :program_end,
                :first_data, :data_done, :photo, :qr, :imgLogo, :linkWeb
            )";

            $stmt = $pdo->prepare($sql);

            foreach ($data as $key => $value) {
                $stmt->bindValue(":$key", $value);
            }
            $stmt->bindValue(':photo', $photo_path, PDO::PARAM_STR);
            $stmt->bindValue(':qr', $qr_path, PDO::PARAM_STR);
            $stmt->bindValue(':imgLogo', $logo_path, PDO::PARAM_STR); // يقبل null

            $stmt->execute();
            $message = "تم حفظ الشهادة بنجاح!";
        }

        header('Location: list.php');
        exit;

    } catch(Exception $e) {
        $error = "خطأ في الحفظ: " . $e->getMessage();
    }
}

function generateCertNumber() {
    return "973" . rand(10000000, 99999999);
}
?>










<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <title>نموذج تعبئة/تعديل الشهادة الصحية</title>
  <style>
    body {
      font-family: 'Tajawal', sans-serif;
      background: #f5f5f7;
      padding: 20px;
      direction: rtl;
      color: #333;
    }
    h2 {
      color: #007c79;
      margin-bottom: 20px;
    }
    form {
      max-width: 600px;
      margin: auto;
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .row {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }
    .field {
      flex: 1 1 45%;
      margin-top: 15px;
    }
    .field.full {
      flex: 1 1 100%;
    }
    label {
      display: block;
      font-weight: bold;
      margin-bottom: 4px;
    }
    input, select {
      width: 100%;
      padding: 6px 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 14px;
      background: #fafafa;
    }
    input[readonly] {
      background: #eee;
      color: #555;
    }
    button {
      margin-top: 20px;
      padding: 10px 16px;
      background: #007c79;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 15px;
    }
    button:hover {
      background: #005f5c;
    }
    .full {
      flex: 1 1 100%;
    }
    .fullh1 {
      font-weight: bold;
      margin-bottom: 4px;
      font-size: 30px;
      text-align: center;
      color: #000;
    }
    #photoFile {
      padding: 3px;
      width: 100%;
    }
    .message {
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .back-link {
      display: inline-block;
      margin-top: 15px;
      padding: 8px 16px;
      background: #444;
      color: white;
      text-decoration: none;
      border-radius: 5px;
    }
    .back-link:hover {
      background: #222;
    }
  </style>
</head>
<body>

  <h2>نموذج تعبئة/تعديل الشهادة الصحية</h2>

  <?php if (isset($message)): ?>
    <div class="message success"><?php echo $message; ?></div>
  <?php endif; ?>

  <?php if (isset($error)): ?>
    <div class="message error"><?php echo $error; ?></div>
  <?php endif; ?>

  <form id="certificateForm" method="post" enctype="multipart/form-data">
    <div class="row">

      <!-- الحقلان اللذان تود الاحتفاظ بهما -->
      <div class="field">
        <label>تاريخ إصدار الشهادة الصحية</label>
        <input id="first_Data" name="first_Data" class="info-value" value="<?php echo $certificate['first_data'] ?? '1447/04/8'; ?>" >
      </div>
      <div class="field">
        <label>تاريخ نهاية الشهادة الصحية</label>
        <input id="Data_done" name="Data_done" class="info-value" value="<?php echo $certificate['data_done'] ?? '1448/04/8'; ?>" >
      </div>

      <!-- باقي الحقول الثابتة -->
      <div class="field">
        <label>تاريخ إصدار الشهادة الصحية (هجري)</label>
        <input type="text" id="issueDateHijri" name="issueDateHijri" value="<?php echo $certificate['issue_date_hijri'] ?? '1447/04/8'; ?>" >
      </div>
      <div class="field">
        <label>تاريخ إصدار الشهادة الصحية (ميلادي)</label>
        <input type="date" id="issueDate" name="issueDate" value="<?php echo $certificate['issue_date'] ?? '2025-08-30'; ?>"  readonly>
      </div>
      <div class="field">
        <label>تاريخ نهاية الشهادة الصحية (هجري)</label>
        <input type="text" id="expireDateHijri" name="expireDateHijri" value="<?php echo $certificate['expire_date_hijri'] ?? '1448/04/8'; ?>" >
      </div>
      <div class="field">
        <label>تاريخ نهاية الشهادة الصحية (ميلادي)</label>
        <input type="date" id="expireDate" name="expireDate" value="<?php echo $certificate['expire_date'] ?? '2028-08-30'; ?>" readonly>
      </div>
      <div class="field full">
        <label>رقم الشهادة الصحية</label>
        <input type="text" id="certNumber" name="certNumber" value="<?php echo $certificate['cert_number'] ?? generateCertNumber(); ?>" >

      <div class="field">
        <label>تاريخ انتهاء البرنامج التثقيفي</label>
        <input type="text" id="programEnd" name="programEnd" value="<?php echo $certificate['program_end'] ?? '1450/04/8'; ?>" >
      </div>
    </div>

    <hr style="margin:20px 0; border-color:#ddd;">

    <div class="row">
      <!-- الحقول القابلة للتعديل -->
      <div class="field full">
        <label>الاسم الكامل</label>
        <input type="text" id="personName" name="personName" value="<?php echo $certificate['person_name'] ?? ''; ?>" required>
      </div>
      <div class="field">
        <label>رقم الهوية</label>
        <input type="text" id="idNumber" name="idNumber" value="<?php echo $certificate['id_number'] ?? ''; ?>" required>
      </div>
      <div class="field">
        <label>الجنسية</label>
        <input type="text" id="nationality" name="nationality" value="<?php echo $certificate['nationality'] ?? ''; ?>" required>
      </div>
      <div class="field">
        <label>المهنة</label>
        <input type="text" id="job" name="job" value="<?php echo $certificate['job'] ?? ''; ?>" >
      </div>
      <div class="field">
        <label>الجنس</label>
        <select id="gender" name="gender" required>
          <option value="ذكر" <?php echo (isset($certificate['gender']) && $certificate['gender'] == 'ذكر') ? 'selected' : ''; ?>>ذكر</option>
          <option value="أنثى" <?php echo (isset($certificate['gender']) && $certificate['gender'] == 'أنثى') ? 'selected' : ''; ?>>أنثى</option>
        </select>
      </div>
      <div class="field">
        <label>الأمانة</label>
        <input type="text" id="amanah" name="amanah" value="<?php echo $certificate['amanah'] ?? 'الرياض'; ?>" required>
      </div>
      <div class="field">
        <label>البلدية</label>
        <input type="text" id="baladiah" name="baladiah" value="<?php echo $certificate['baladiah'] ?? ''; ?>" >
      </div>
      <div class="field">
        <label>رقم الرخصة</label>
        <input type="text" id="licenseNumber" name="licenseNumber" value="<?php echo $certificate['license_number'] ?? ''; ?>" >
      </div>
      <div class="field">
        <label>اسم المنشأة</label>
        <input type="text" id="placeName" name="placeName" value="<?php echo $certificate['place_name'] ?? ''; ?>" >
      </div>
      <div class="field">
        <label>رقم المنشأة</label>
        <input type="text" id="placeNumber" name="placeNumber" value="<?php echo $certificate['place_number'] ?? ''; ?>" >
      </div>

     <div class="field">
        <label>نوع البرنامج التثقيفي</label>
        <input type="text" id="programType" name="programType" value="<?php echo $certificate['program_type'] ?? 'منشآت الغذاء'; ?>" >
      </div>

      <div class="field">
        <label>رابط الموقع</label>
        <input type="url" id="linkWeb" name="linkWeb" value="<?php echo $certificate['linkWeb'] ?? ''; ?>" placeholder="https://example.com">
      </div>

      <div class="field full">
    <label class="fullh1">شعار الموقع</label>
    <input type="file" id="logoFile" name="logoFile" accept="image/*">
    <img id="logoPreview" style="display:none; max-width:200px; margin-top:10px;" alt="شعار المنشأة">
<input type="hidden" id="fetchedLogo" name="fetchedLogo" value="">

</div>

      <div class="field full">
        <label class="fullh1">صورة الشخصية</label>
        <input type="file" id="photoFile" name="photoFile" accept="image/*">
      </div>

      <br>
      <div class="field">
        <label>رمز QR</label>
        <input type="file" id="qrFile" name="qrFile" accept="image/*">
      </div>
    </div>

    <button type="submit">💾 حفظ البيانات</button>

  </form>
 <a href="list.php" class="back-link">القائمة</a>

 <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment-hijri/2.2.0/moment-hijri.min.js"></script>
<script>
document.addEventListener("DOMContentLoaded", () => {
    // === التاريخ الميلادي اليوم ===
    const today = moment();

    // === تحويله لهجري (يتم الآن قراءة اليوم تلقائيًا) ===
    const hijriNow = moment().format('iYYYY/iMM/iDD');
    const hijriMoment = moment(hijriNow, 'iYYYY/iMM/iDD');

    // === التاريخ الهجري للسنة القادمة و بعد 3 سنوات ===
    const nextYearHijri = hijriMoment.clone().add(1, 'iYear').format('iYYYY/iMM/iDD');
    const threeYearsLaterHijri = hijriMoment.clone().add(3, 'iYear').format('iYYYY/iMM/iDD');

    // === الميلادي (نفس اليوم من الجهاز) ===
    const todayGregorian = today.format("YYYY-MM-DD");
    const nextYearGregorian = today.clone().add(1, 'year').format("YYYY-MM-DD");

    // === تجهيز البيانات للتخزين ===
    const certData = {
        issueDate: todayGregorian,           // ميلادي اليوم
        issueDateHijri: hijriNow,            // هجري (يتم قراءته تلقائيًا)
        expireDate: nextYearGregorian,       // بعد سنة ميلادي
        expireDateHijri: nextYearHijri,      // بعد سنة هجري
        programEnd: threeYearsLaterHijri     // بعد 3 سنوات هجري
    };

    // حفظ في localStorage
    localStorage.setItem("myCertDates", JSON.stringify(certData));

    // === عرض القيم في الحقول ===
    const fill = (id, value) => {
        const el = document.getElementById(id);
        if (el) el.value = value;
    };

    fill("issueDate", certData.issueDate);
    // fill("issueDateHijri", certData.issueDateHijri);
    fill("expireDate", certData.expireDate);
    // fill("expireDateHijri", certData.expireDateHijri);
    // fill("programEnd", certData.programEnd);
});
</script>



 <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment-hijri/2.2.0/moment-hijri.min.js"></script>
<script>
document.addEventListener("DOMContentLoaded", () => {
    // === التاريخ الميلادي اليوم ===
    const today = moment();

    // === تحويله لهجري (يتم الآن قراءة اليوم تلقائيًا) ===
    const hijriNow = moment().format('iYYYY/iMM/iDD');
    const hijriMoment = moment(hijriNow, 'iYYYY/iMM/iDD');

    // === التاريخ الهجري للسنة القادمة و بعد 3 سنوات ===
    const nextYearHijri = hijriMoment.clone().add(1, 'iYear').format('iYYYY/iMM/iDD');
    const threeYearsLaterHijri = hijriMoment.clone().add(3, 'iYear').format('iYYYY/iMM/iDD');

    // === الميلادي (نفس اليوم من الجهاز) ===
    const todayGregorian = today.format("YYYY-MM-DD");
    const nextYearGregorian = today.clone().add(1, 'year').format("YYYY-MM-DD");

    // === تجهيز البيانات للتخزين ===
    const certData = {
        issueDate: todayGregorian,           // ميلادي اليوم
        issueDateHijri: hijriNow,            // هجري (يتم قراءته تلقائيًا)
        expireDate: nextYearGregorian,       // بعد سنة ميلادي
        expireDateHijri: nextYearHijri,      // بعد سنة هجري
        programEnd: threeYearsLaterHijri     // بعد 3 سنوات هجري
    };

    // حفظ في localStorage
    localStorage.setItem("myCertDates", JSON.stringify(certData));

    // === عرض القيم في الحقول ===
    const fill = (id, value) => {
        const el = document.getElementById(id);
        if (el) el.value = value;
    };

    fill("issueDate", certData.issueDate);
    // fill("issueDateHijri", certData.issueDateHijri);
    fill("expireDate", certData.expireDate);
    // fill("expireDateHijri", certData.expireDateHijri);
    // fill("programEnd", certData.programEnd);
}   );
</script>





















<script>
document.addEventListener("DOMContentLoaded", function () {
    const licenseInput = document.getElementById('licenseNumber');
    const amanahInput = document.getElementById('amanah');
    const baladiahInput = document.getElementById('baladiah');
    const placeNameInput = document.getElementById('placeName');
    const placeNumberInput = document.getElementById('placeNumber');
    const logoPreview = document.getElementById('logoPreview');
    const fetchedLogoInput = document.getElementById('fetchedLogo'); // الحقل المخفي الجديد

    let debounceTimer;

    if (licenseInput) {
        licenseInput.addEventListener('input', function () {
            clearTimeout(debounceTimer);
            const licenseNumber = this.value.trim();

            if (licenseNumber.length < 3) {
                // لا نبحث إذا كان الرقم قصير جدًا
                return;
            }

            debounceTimer = setTimeout(() => {
                fetch(`?action=lookup_license&license_number=${encodeURIComponent(licenseNumber)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.found) {
                            amanahInput.value = data.amanah || '';
                            baladiahInput.value = data.baladiah || '';
                            placeNameInput.value = data.placeName || '';
                            placeNumberInput.value = data.placeNumber || '';

                            // عرض الشعار
                            if (logoPreview && data.logo) {
                                logoPreview.src = data.logo;
                                logoPreview.style.display = 'block';
                            } else if (logoPreview) {
                                logoPreview.style.display = 'none';
                            }

                            // ✅ حفظ بيانات الشعار في الحقل المخفي
                            if (fetchedLogoInput) {
                                fetchedLogoInput.value = data.logo || '';
                            }
                        } else {
                            // لا توجد رخصة → إخفاء الشعار وتفريغ الحقل المخفي
                            if (logoPreview) logoPreview.style.display = 'none';
                            if (fetchedLogoInput) fetchedLogoInput.value = '';
                        }
                    })
                    .catch(err => {
                        console.warn('خطأ في البحث عن الرخصة:', err);
                        if (logoPreview) logoPreview.style.display = 'none';
                        if (fetchedLogoInput) fetchedLogoInput.value = '';
                    });
            }, 500);
        });
    }
});
</script>
</body>
</html>