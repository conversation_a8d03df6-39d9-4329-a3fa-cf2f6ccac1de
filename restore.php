<?php
require_once 'db_connect.php';

header('Content-Type: application/json');

// الحصول على البيانات المرسلة
$json = file_get_contents('php://input');
$data = json_decode($json, true);

if (!$data || !is_array($data)) {
    echo json_encode(['success' => false, 'message' => 'البيانات المرسلة غير صالحة']);
    exit;
}

try {
    // بدء المعاملة
    $pdo->beginTransaction();

    // حذف جميع الشهادات الحالية (اختياري)
    // $pdo->exec("DELETE FROM certificates");

    // إضافة الشهادات المستعادة
    foreach ($data as $cert) {
        // تحويل base64 إلى بيانات ثنائية
        $photo = isset($cert['photo']) ? base64_decode($cert['photo']) : null;
        $qr = isset($cert['qr']) ? base64_decode($cert['qr']) : null;

        // التحقق من وجود الشهادة
        $stmt = $pdo->prepare("SELECT id FROM certificates WHERE id_number = ?");
        $stmt->execute([$cert['id_number']]);
        $existing = $stmt->fetch();

        if ($existing) {
            // تحديث الشهادة الموجودة
            $sql = "UPDATE certificates SET 
                    person_name = :person_name, 
                    nationality = :nationality, 
                    job = :job, 
                    gender = :gender, 
                    amanah = :amanah, 
                    baladiah = :baladiah, 
                    license_number = :license_number, 
                    place_name = :place_name, 
                    place_number = :place_number, 
                    cert_number = :cert_number, 
                    issue_date_hijri = :issue_date_hijri, 
                    issue_date = :issue_date, 
                    expire_date_hijri = :expire_date_hijri, 
                    expire_date = :expire_date, 
                    program_type = :program_type, 
                    program_end = :program_end, 
                    first_data = :first_data, 
                    data_done = :data_done";

            if ($photo !== null) {
                $sql .= ", photo = :photo";
            }

            if ($qr !== null) {
                $sql .= ", qr = :qr";
            }

            $sql .= " WHERE id_number = :id_number";

            $stmt = $pdo->prepare($sql);

            // ربط القيم
            $stmt->bindParam(':person_name', $cert['person_name']);
            $stmt->bindParam(':nationality', $cert['nationality']);
            $stmt->bindParam(':job', $cert['job']);
            $stmt->bindParam(':gender', $cert['gender']);
            $stmt->bindParam(':amanah', $cert['amanah']);
            $stmt->bindParam(':baladiah', $cert['baladiah']);
            $stmt->bindParam(':license_number', $cert['license_number']);
            $stmt->bindParam(':place_name', $cert['place_name']);
            $stmt->bindParam(':place_number', $cert['place_number']);
            $stmt->bindParam(':cert_number', $cert['cert_number']);
            $stmt->bindParam(':issue_date_hijri', $cert['issue_date_hijri']);
            $stmt->bindParam(':issue_date', $cert['issue_date']);
            $stmt->bindParam(':expire_date_hijri', $cert['expire_date_hijri']);
            $stmt->bindParam(':expire_date', $cert['expire_date']);
            $stmt->bindParam(':program_type', $cert['program_type']);
            $stmt->bindParam(':program_end', $cert['program_end']);
            $stmt->bindParam(':first_data', $cert['first_data']);
            $stmt->bindParam(':data_done', $cert['data_done']);
            $stmt->bindParam(':id_number', $cert['id_number']);

            if ($photo !== null) {
                $stmt->bindParam(':photo', $photo, PDO::PARAM_LOB);
            }

            if ($qr !== null) {
                $stmt->bindParam(':qr', $qr, PDO::PARAM_LOB);
            }

            $stmt->execute();
        } else {
            // إضافة شهادة جديدة
            $stmt = $pdo->prepare("INSERT INTO certificates (
                    id_number, person_name, nationality, job, gender, amanah, baladiah, 
                    license_number, place_name, place_number, cert_number, issue_date_hijri, 
                    issue_date, expire_date_hijri, expire_date, program_type, program_end, 
                    first_data, data_done, photo, qr) 
                    VALUES (
                    :id_number, :person_name, :nationality, :job, :gender, :amanah, :baladiah, 
                    :license_number, :place_name, :place_number, :cert_number, :issue_date_hijri, 
                    :issue_date, :expire_date_hijri, :expire_date, :program_type, :program_end, 
                    :first_data, :data_done, :photo, :qr)");

            // ربط القيم
            $stmt->bindParam(':id_number', $cert['id_number']);
            $stmt->bindParam(':person_name', $cert['person_name']);
            $stmt->bindParam(':nationality', $cert['nationality']);
            $stmt->bindParam(':job', $cert['job']);
            $stmt->bindParam(':gender', $cert['gender']);
            $stmt->bindParam(':amanah', $cert['amanah']);
            $stmt->bindParam(':baladiah', $cert['baladiah']);
            $stmt->bindParam(':license_number', $cert['license_number']);
            $stmt->bindParam(':place_name', $cert['place_name']);
            $stmt->bindParam(':place_number', $cert['place_number']);
            $stmt->bindParam(':cert_number', $cert['cert_number']);
            $stmt->bindParam(':issue_date_hijri', $cert['issue_date_hijri']);
            $stmt->bindParam(':issue_date', $cert['issue_date']);
            $stmt->bindParam(':expire_date_hijri', $cert['expire_date_hijri']);
            $stmt->bindParam(':expire_date', $cert['expire_date']);
            $stmt->bindParam(':program_type', $cert['program_type']);
            $stmt->bindParam(':program_end', $cert['program_end']);
            $stmt->bindParam(':first_data', $cert['first_data']);
            $stmt->bindParam(':data_done', $cert['data_done']);
            $stmt->bindParam(':photo', $photo, PDO::PARAM_LOB);
            $stmt->bindParam(':qr', $qr, PDO::PARAM_LOB);

            $stmt->execute();
        }
    }

    // إنهاء المعاملة
    $pdo->commit();

    echo json_encode(['success' => true, 'message' => 'تمت استعادة البيانات بنجاح']);
} catch(Exception $e) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    $pdo->rollBack();
    echo json_encode(['success' => false, 'message' => 'خطأ في استعادة البيانات: ' . $e->getMessage()]);
}
?>
