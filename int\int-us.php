﻿﻿<?php
require_once '../db_connect.php';
require_once '../inc/SecureIdHandler.php';

// التحقق من وجود معرف الشهادة
if (!isset($_GET['id'])) {
    // إذا لم يكن هناك معرف، قم بإعادة التوجيه إلى صفحة خطأ
    header("Location: ../error.php");
    exit;
}

$encryptedId = $_GET['id'];

// فك تشفير الـ ID
$id = SecureIdHandler::decryptId($encryptedId);

if ($id === false) {
    // إذا كان الـ ID غير صالح، قم بإعادة التوجيه إلى نفس الصفحة مع الـ ID الصحيح
    // هذا يضمن أن المستخدم سيتمكن دائمًا من رؤية بياناته حتى لو حاول تعديل الرابط
    header("Location: " . SecureIdHandler::createIntUsLink($id));
    exit;
}

// جلب بيانات الشهادة من قاعدة البيانات
try {
    $stmt = $pdo->prepare("SELECT * FROM certificates WHERE id_number = ?");
    $stmt->execute([$id]);
    $certificate = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$certificate) {
        die("الشهادة غير موجودة");
    }



if ($certificate && $certificate['is_active'] == 0) {
    die("<h2 style='color:red;text-align:center'>⚠️ هذه الشهادة معطلة مؤقتًا</h2>");
}



    // تحضير الصورة للعرض
    $photo_base64 = '';
    if (!empty($certificate['photo'])) {
        // تحويل البيانات الثنائية للصورة إلى base64
        $photo_data = $certificate['photo'];
        // التحقق مما إذا كانت البيانات بالفعل بتنسيق base64
        if (base64_decode($photo_data, true) === false) {
            // إذا لم تكن base64، قم بتحويلها
            $photo_base64 = 'data:image/jpeg;base64,' . base64_encode($photo_data);
        } else {
            // إذا كانت بالفعل base64، تأكد من إضافة البادئة الصحيحة
            if (strpos($photo_data, 'data:image') === 0) {
                $photo_base64 = $photo_data;
            } else {
                $photo_base64 = 'data:image/jpeg;base64,' . $photo_data;
            }
        }
    } else {
        // استخدام صورة افتراضية إذا لم تكن هناك صورة
        $photo_base64 = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMjQgMjQiPjxwYXRoIGZpbGw9IiNjY2MiIGQ9Ik0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0wIDNjMS42NiAwIDMgMS4zNCAzIDNzLTEuMzQgMy0zIDMtMy0xLjM0LTMtMyAxLjM0LTMgMy0zem0wIDE0LjJjLTIuNSAwLTQuNzEtMS4yOC02LjE2LTMuMTlMMTUuMyAxNGMxLjI1IDEuNjYgMiAxMy45OSAyIDEzLjk5czcuNzUtMTIuMzQgMi0xMy45OWw5LjE2IDEuMDFDMTYuNzEgMTcuOTIgMTQuNSAxOS4yIDEyIDE5LjJ6Ii8+PC9zdmc+';
    }
} catch(PDOException $e) {
    die("خطأ في جلب البيانات: " . $e->getMessage());
}
?>



<!DOCTYPE html>

<html lang="ar" dir="rtl">

<link rel="icon" type="image/x-icon" href="https://apps.balady.gov.sa/BALADYCDN/Content//images/fav.ico">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

 <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
<!-- Bootstrap CSS -->
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>

<!-- Popper.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
<!-- fontawsome -->
 <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.1/css/all.min.css">
<!-- Bootstrap JS -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
<meta name="viewport" content="width=device-width, initial-scale=1.0">.

<link rel="stylesheet" href="stylesprloder.css">
<link rel="stylesheet" href="style.css">
<!-- ! Style for sidebar -->
<link rel="stylesheet" href="CSS/design.css">

<style>


</style>
  </head>

<body class="loged-user bg-grey loaded">


    <div class="preloader">
        <div class="preloader-content">
            <img src="servicesbladay.jpeg" alt="شعار الموقع" class="logo">

        </div>
    </div>
<!-- شاشة التحميل -->
<div id="preloader" aria-hidden="true">
  <img src="imgs/شعار منصة بلدي – SVG (1).svg" alt="Loading…">
</div>


    <div class="preloader" id="preloader-logo" style="display: none;">
        <div class="loader">
            <div class="loader-logo">
                
                </div>
                <div class="loader-figure">
                    
                </div>
               </div>
    </div>
    
    <div class="preloader checking" id="preloader-logo">
        <div class="loader">
            <div class="loader-logo">
                
               </div>
            <div class="loader-figure"> </div>
        </div>
    </div>




    <script src="icons.js"></script>



    
<script src="header.js"></script>


        <!-- Modal-->
        <div class="modal search-modal fade" id="search-modal" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="container">
                            <div class="row">
                                <button class="close" type="button" data-dismiss="modal" aria-label="Close"><span class="close-icon" aria-hidden="true">×</span></button>
                                <div class="col-12">
                                    <form class="search-modal-form">
                                        <div class="input-wrap">
                                            <input class="search-input" id="search-text" type="text" name="search-text" placeholder="ابحث" required="required" value="">
                                            <p class="small-p">أضغط للبحث</p>
                                        </div>
                                        <button class="search-submit" type="submit">
                                            <svg class="icon-back" viewBox="0 0 48 48" width="20" height="20">
                                                <use xlink:href="#svg-search-ico"></use>
                                            </svg>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>





    </div>

  















<!-- البدايه   الي تهمني -->








    <div class="bg-grey">
        <div class="page-top-fixed nav-fixed">
                                    <div class="container p-lg-0">
                                        <div class="px-3 pb-2 px-md-0 d-flex flex-column flex-md-row align-items-md-center justify-content-between page-btns-top-fixed">
                                            <div class="page-title-content">
                                                <nav aria-label="breadcrumb" class="d-flex justify-content-between">

                                                </nav>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-grey">
                                    <section class="page-container">
                                        <div class="section pt-0 mt-1 mt-md-4">
                                            <div class="container p-0">
                                                <div id="content_data"><div class="card p-lg-4">
                              <div class="card-body">

                                  <div class="row">
                                      <div class="col-md-12 center">
                                          <div class="sub-heading">
                                                                        <h2 style="text-align:center">الشهادة الصحية</h2>
                                              
                                          </div>
                                      </div>
                                  </div>

                                  <div class="row">
                                      <div class="col-md-12" style="text-align: center;">
                        <img id="profileImage" src="<?php echo $photo_base64; ?>" alt="" srcset="">    

                                  </div>
                                  </div>

























                                  <div class="row">

                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="amanah" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['amanah'] ?? ''); ?>" readonly="">
                                              <span> الامانة </span>
                                          </label>
                                      </div>

                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="baladiah" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['baladiah'] ?? ''); ?>" readonly="">
                                              <span> البلدية</span>
                                          </label>
                                      </div>



                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="personName" class="form-control" type="text" value=" <?php echo htmlspecialchars($certificate['person_name'] ?? ''); ?>    " readonly="">
                                              <span>الاسم </span>
                                          </label>
                                      </div>

                                      <div class="col-md-6">
                                          <label class="form-group has-float-label mb-4">
                                              <input id="idNumber" class="form-control" type="text" placeholder="" value="<?php echo htmlspecialchars($certificate['id_number'] ?? ''); ?> " readonly="">
                                              <span> رقم الهوية </span>
                                          </label>
                                      </div>

                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['gender'] ?? ''); ?>" readonly="">
                                              <span>الجنس</span>
                                          </label>
                                      </div>

                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="nationality" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['nationality'] ?? ''); ?>" readonly="">
                                              <span> الجنسية </span>
                                          </label>
                                      </div>


                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="job" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['job'] ?? ''); ?>" readonly="">
                                              <span> المهنة </span>
                                          </label>
                                      </div>



                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="certNumber" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['cert_number'] ?? ''); ?>" readonly="">
                                              <span> رقم الشهادة الصحية </span>
                                          </label>
                                      </div>







                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="issueDateHijri" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['issue_date_hijri'] ?? ''); ?>" readonly="">
                                              <span> تاريخ إصدار الشهادة الصحية هجري</span>





                                          </label>
                                      </div>

                                                        <div class="col-md-6">
                                              <label class="form-group has-float-label ">
                                                  <input id="issueDate" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['issue_date'] ?? ''); ?>" readonly="">
                                                  <span> تاريخ إصدار الشهادة الصحية ميلادي</span>
                                              </label>
                                          </div>
                                      
                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="expireDateHijri" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['expire_date_hijri'] ?? ''); ?>" readonly="">
                                              <span> تاريخ نهاية الشهادة الصحية هجري</span>

                                          </label>
                                      </div>



                                                        <div class="col-md-6">
                                              <label class="form-group has-float-label ">
                                                  <input id="expireDate" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['expire_date'] ?? ''); ?>" readonly="">
                                                  <span> تاريخ نهاية الشهادة الصحية ميلادي</span>
                                              </label>
                                          </div>
                                      


                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="programType" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['program_type'] ?? ''); ?>" readonly="">
                                              <span> نوع البرنامج التثقيفى</span>
                                          </label>
                                      </div>

                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['program_end'] ?? ''); ?>" readonly="">
                                              <span> تاريخ انتهاء البرنامج التثقيفى</span>
                                          </label>
                                      </div>



                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="licenseNumber" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['license_number'] ?? ''); ?>" readonly="">

                                              <span> رقم الرخصة</span>
                                          </label>
                                      </div>
                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="placeName" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['place_name'] ?? ''); ?>" readonly="">
                                              <span> اسم المنشآة </span>
                                          </label>
                                      </div>
                                      <div class="col-md-6">
                                          <label class="form-group has-float-label ">
                                              <input id="placeNumber" class="form-control" type="text" value="<?php echo htmlspecialchars($certificate['place_number'] ?? ''); ?>" readonly="">
                                              <span> رقم المنشأة</span>
                                          </label>
                                      </div>
                                      <div class="col-md-6">

                                      </div>

                                      <div class="col-md-12 mt-4">

                                        

                                      </div>

                        </div>
























































































































                        

                    </div>
                </div>
              </div>
                    </div>
                </div>
            </section>
        </div>
    </div>




















<!-- النهايه  الي تهمني  -->










<!-- <div class="muneer-trigger-button-box bottom-right entrance-fade hover-none" data-nosnippet=""><button id="muneer-trigger-button" class="icon-position-before" aria-label="فتح أداة سهولة الوصول" data-muneer-trigger=""><span class="muneer-trigger-button-icon"><svg height="32" viewBox="0 0 512 512" width="32" xmlns="http://www.w3.org/2000/svg">
                <path d="m256 112c30.9 0 56-25.1 56-56s-25.1-56-56-56-56 25.1-56 56 25.1 56 56 56z"></path>
                <path d="m432 112.8-.5.1-.4.1c-1 .3-2 .6-3 .9-18.6 5.5-108.9 30.9-172.6 30.9-59.1 0-141.3-22-167.6-29.5-2.6-1-5.3-1.9-8-2.6-19-5-32 14.3-32 31.9 0 17.5 15.7 25.8 31.5 31.8v.3l95.2 29.7c9.7 3.7 12.3 7.5 13.6 10.8 4.1 10.6.8 31.6-.3 38.9l-5.8 45-32.1 176.3c-.1.5-.2 1-.3 1.5l-.2 1.3c-2.3 16.1 9.5 31.8 32 31.8 19.6 0 28.3-13.5 32-31.9 0 0 28-157.6 42-157.6s42.8 157.6 42.8 157.6c3.8 18.4 12.4 31.9 32 31.9 22.5 0 34.4-15.7 32-31.9-.2-1.4-.5-2.7-.8-4.1l-32.5-174.7-5.8-45c-4.2-26.2-.8-34.9.3-36.9 0 0 .1-.1.1-.2 1.1-2 6-6.5 17.5-10.8l89.3-31.2c.5-.1 1.1-.3 1.6-.5 16-6 32-14.3 32-31.9s-13-37-32-32z">
                </path>
            </svg></span></button></div>





<div id="muneer-keyboard-box" style="display: none;">
    <div class="simple-keyboard"></div>
</div> -->






<div class="smart-assistant muneer-trigger-button-box smart-assistant bottom-right entrance-fade hover-none" data-nosnippet="">
    <button id="muneer-trigger-button" class="icon-position-before" aria-label="فتح أداة سهولة الوصول" data-muneer-trigger="">
        <span class="muneer-trigger-button-icon"><svg height="32" viewBox="0 0 512 512" width="32" xmlns="http://www.w3.org/2000/svg">
                    <path d="m256 112c30.9 0 56-25.1 56-56s-25.1-56-56-56-56 25.1-56 56 25.1 56 56 56z"></path>
                    <path d="m432 112.8-.5.1-.4.1c-1 .3-2 .6-3 .9-18.6 5.5-108.9 30.9-172.6 30.9-59.1 0-141.3-22-167.6-29.5-2.6-1-5.3-1.9-8-2.6-19-5-32 14.3-32 31.9 0 17.5 15.7 25.8 31.5 31.8v.3l95.2 29.7c9.7 3.7 12.3 7.5 13.6 10.8 4.1 10.6.8 31.6-.3 38.9l-5.8 45-32.1 176.3c-.1.5-.2 1-.3 1.5l-.2 1.3c-2.3 16.1 9.5 31.8 32 31.8 19.6 0 28.3-13.5 32-31.9 0 0 28-157.6 42-157.6s42.8 157.6 42.8 157.6c3.8 18.4 12.4 31.9 32 31.9 22.5 0 34.4-15.7 32-31.9-.2-1.4-.5-2.7-.8-4.1l-32.5-174.7-5.8-45c-4.2-26.2-.8-34.9.3-36.9 0 0 .1-.1.1-.2 1.1-2 6-6.5 17.5-10.8l89.3-31.2c.5-.1 1.1-.3 1.6-.5 16-6 32-14.3 32-31.9s-13-37-32-32z">
                    </path>


</svg></span></button></div>

<!-- ! Start Sidebar -->
 <!-- <div class="smart-assistant"><i class="fa-solid fa-universal-access"></i></div> -->
 <div class="side-bar">
        <!-- Start Header  -->
        <div class="header">
            <div class="header-top">
                <div class="language-selector">
                    <div class="selected-language" id="selectedLanguage">
                        <div class="flag first"><img src="Img/1654561b39b14fd4858e7e76a0eaed42.jpg" alt=""></div>
                        <span class="language-text" id="main-language">Arabic</span>
                    </div>
                
                <div class="language-options" id="languageOptions">
                    <div class="language-option" data-lang="arabic">
                        <div class="flag last"><img src="Img/1654561b39b14fd4858e7e76a0eaed42.jpg" alt=""></div>
                        <div class="language-text-last">Arabic</div>
                    </div>
                    <div class="language-option" data-lang="english">
                        <div class="flag last"><img src="Img/england-flag-icon-logo-vector-design-template_827767-2523.jpg" alt=""></div>
                        <div class="language-text-last">English</div>
                    </div>
                </div>
            </div>

                <div class="setting">
                    <div class="icons">
                        <div class="active"><i class="fa-solid fa-moon"></i></div>
                        <div><i class="fa-solid fa-sun"></i></div>
                        <div><i class="fa-solid fa-computer"></i></div>
                    </div>
                    <div class="close">x</div>
                </div>
            </div>

            <div class="header-title">
                <h3>أداة سهولة الوصول</h3>
                <div class="toggle-container">
                    <label class="switch">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>
        <!-- End Header  -->
        <!-- Start Biggest Buttons  -->
        <div class="biggest-btns">
            <h3>تكبير أزرار الأداة</h3>
            <div class="toggle-container">
                <label class="switch">
                    <input type="checkbox" id="toggleSwitch">
                    <span class="slider"></span>
                </label>
            </div>
        </div>
        <!-- Start Help Patterns  -->
        <div class="help-pattern">
            <div class="heading">
                <h3>أنماط المساعدة</h3>
                <div class="arrow"><i class="fa-solid fa-arrow-down"></i></div>
            </div>
            <ul class="help-pattern-items">
                <li>
                    <a href="#">
                    </a>
                    <div class="first">
                        <div class="top-txt">
                            <i class="fa-solid fa-brain"></i>
                            <h4>الوضع الآمن للصرع</h4>
                        </div>
                        <p>تقليل الألوان وإزالة الوميض</p>
                    </div>
                    <div class="toggle-container">
                        <label class="switch">
                            <input type="checkbox" id="toggleSwitch">
                            <span class="slider"></span>
                        </label>
                    </div>
                </li>
                <li>
                    <a href="#">
                    </a>
                    <div class="first">
                        <div class="top-txt">
                            <i class="fa-solid fa-brain"></i>
                            <h4>ضعف الرؤية</h4>
                        </div>
                        <p>تحسين مظهر المواقع</p>
                    </div>
                    <div class="toggle-container">
                    <label class="switch">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    </div>
                </li>
                <li>
                    <a href="#">
                    </a>
                    <div class="first">
                        <div class="top-txt">
                            <i class="fa-solid fa-brain"></i>
                            <h4>الإعاقة العقلية</h4>
                        </div>
                        <p>يساعد على التركيز على المحتوى المعين</p>
                    </div>
                    <div class="toggle-container">
                    <label class="switch">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    </div>
                </li>
                <li>
                    <a href="#">
                    </a>
                    <div class="first">
                        <div class="top-txt">
                            <i class="fa-solid fa-brain"></i>
                            <h4>الإعاقة الحركية</h4>
                        </div>
                        <p>يسهل تصفح الموقع باستخدام لوحة المفاتيح</p>
                    </div>
                    <div class="toggle-container">
                    <label class="switch">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    </div>
                </li>
                <li>
                    <a href="#">
                    </a>
                    <div class="first">
                        <div class="top-txt">
                            <i class="fa-solid fa-brain"></i>
                            <h4>وضع عمى الألوان</h4>
                        </div>
                        <p>تغيير وتخصيص الألوان لتلائم المصابين بعمى الألوان</p>
                    </div>
                    <div class="toggle-container">
                    <label class="switch">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    </div>
                </li>
                <li>
                    <a href="#">
                    </a>
                    <div class="first">
                        <div class="top-txt">
                            <i class="fa-solid fa-brain"></i>
                            <h4>صعوبات القراءة (ديسليكسيا)</h4>
                        </div>
                        <p>خطوط مخصصة لمن يعانون من عسر القراءة</p>
                    </div>
                    <div class="toggle-container">
                    <label class="switch">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    </div>
                </li>
                <li>
                    <a href="#">
                    </a>
                    <div class="first">
                        <div class="top-txt">
                            <i class="fa-solid fa-brain"></i>
                            <h4>ADHD اضطراب نقص الانتباه وفرط النشاط</h4>
                        </div>
                        <p>تقليل التشتت وتحسين التركيز</p>
                    </div>
                    <div class="toggle-container">
                    <label class="switch">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    </div>
                </li>
                <li>
                    <a href="#">
                    </a>
                    <div class="first">
                        <div class="top-txt">
                            <i class="fa-solid fa-brain"></i>
                            <h4>وضع العمى</h4>
                        </div>
                        <p>يسمح باستخدام الموقع مع قارئ الشاشة</p>
                    </div>
                    <div class="toggle-container">
                    <label class="switch">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    </div>
                </li>
            </ul>
        </div>
        <!-- End Help Patterns -->
        <!-- Start Reading Comfortable  -->
        <div class="comfort-read">
            <h3>تجربة قراءة مريحة</h3>
            <div class="container">
                <div class="card">
                    <i class="fa-solid fa-maximize"></i>
                    <p>تغيير حجم المحتوى</p>
                </div>
                <div class="card">
                    <i class="fas fa-search-plus"></i>
                    <p>مكبر النص</p>
                </div>
                <div class="card">
                    <div class="read-easy">Aa</div>
                    <p>خط لقراءة أسهل</p>
                </div>
                <div class="card">
                    <div class="read-easy">Aa</div>
                    <p>خط الديسلكسيا</p>
                </div>
                <div class="card">
                    <i class="fas fa-heading"></i>
                    <p>تمييز العناوين</p>
                </div>
                <div class="card">
                    <i class="fas fa-link"></i>
                    <p>تمييز الروابط</p>
                </div>
                <div class="card">
                    <i class="fas fa-font"></i>
                    <p>تغيير حجم الخط</p>
                </div>
                <div class="card">
                    <i class="fas fa-align-left"></i>
                    <p>محاذاة النص</p>
                </div>
                <div class="card">
                    <i class="fas fa-align-justify"></i>
                    <p>تباعد الأسطر</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-text-width"></i>
                    <p>تباعد الحروف</p>
                </div>
                <div class="card">
                    <i class="fas fa-sort-alpha-down"></i>
                    <p>تباعد الكلمات</p>
                </div>
                <div class="card">
                    <i class="fas fa-volume-up"></i>
                    <p>الأوامر الصوتية</p>
                </div>
                <div class="card coming-soon">
                    <i class="fas fa-bell"></i>
                    <p>النص الى صوت</p>
                </div>
            </div>
        </div>
        <!-- End Reading Comfortable  -->
        <!-- Start Visualize Comfortable  -->
        <div class="visual-comfort">
            <h3>تجربة بصرية ممتعة</h3>
            <div class="container">
                <div class="card">
                    <i class="fas fa-volume-up"></i>
                    <p>تباين ذكي</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-droplet-slash"></i>
                    <p>أحادي اللون</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-circle-half-stroke"></i>
                    <p>تباين عالي</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-droplet"></i>
                    <p>تشبع عالي</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-droplet-slash"></i>
                    <p>تشبع منخفض</p>
                </div>
            </div>
        </div>
        <!-- End Visualize Comfortable  -->
        <!-- Start Setting Colors For Text  -->
        <div class="color-settings">
            <div class="setting-item" id="text-colors-toggle">
                <span class="color-indicator"></span>
                <p>ضبط ألوان النص</p>
            </div>
            <div class="color-palette hidden" id="text-colors-palette">
                <div class="color-circle" style="background-color: #00ff00;"></div>
                <div class="color-circle selected" style="background-color: #ff00ff;"></div>
                <div class="color-circle" style="background-color: #ff00ff;"></div>
                <div class="color-circle" style="background-color: #00ff00;"></div>
                <div class="color-circle" style="background-color: #ff00ff;"></div>
                <div class="color-circle" style="background-color: #ffff00;"></div>
                <div class="color-circle" style="background-color: #ffff00;"></div>
                <div class="color-circle" style="background-color: #ff6600;"></div>
                <div class="color-circle" style="background-color: #ff0000;"></div>
                <div class="color-circle" style="background-color: #0000ff;"></div>
                <div class="color-circle" style="background-color: #0000ff;"></div>
                <div class="color-circle" style="background-color: #00ffff;"></div>
                <div class="color-circle" style="background-color: #00ffff;"></div>
            </div>
        </div>
        <!-- End Setting Colors For Text -->
        <!-- Start Setting Colors For Heading-->
        <div class="color-settings">
            <div class="setting-item" id="text-colors-toggle">
                <span class="color-indicator"></span>
                <p>ضبط ألوان العناوين</p>
            </div>
            <div class="color-palette hidden" id="text-colors-palette">
                <div class="color-circle" style="background-color: #00ff00;"></div>
                <div class="color-circle selected" style="background-color: #ff00ff;"></div>
                <div class="color-circle" style="background-color: #ff00ff;"></div>
                <div class="color-circle" style="background-color: #00ff00;"></div>
                <div class="color-circle" style="background-color: #ff00ff;"></div>
                <div class="color-circle" style="background-color: #ffff00;"></div>
                <div class="color-circle" style="background-color: #ffff00;"></div>
                <div class="color-circle" style="background-color: #ff6600;"></div>
                <div class="color-circle" style="background-color: #ff0000;"></div>
                <div class="color-circle" style="background-color: #0000ff;"></div>
                <div class="color-circle" style="background-color: #0000ff;"></div>
                <div class="color-circle" style="background-color: #00ffff;"></div>
                <div class="color-circle" style="background-color: #00ffff;"></div>
            </div>
        </div>
        <!-- End Setting Colors For Heading-->
        <!-- Start Setting Colors For Background-->
        <div class="color-settings">
            <div class="setting-item" id="text-colors-toggle">
                <span class="color-indicator"></span>
                <p>ضبط ألوان الخلفية</p>
            </div>
            <div class="color-palette hidden" id="text-colors-palette">
                <div class="color-circle" style="background-color: #00ff00;"></div>
                <div class="color-circle selected" style="background-color: #ff00ff;"></div>
                <div class="color-circle" style="background-color: #ff00ff;"></div>
                <div class="color-circle" style="background-color: #00ff00;"></div>
                <div class="color-circle" style="background-color: #ff00ff;"></div>
                <div class="color-circle" style="background-color: #ffff00;"></div>
                <div class="color-circle" style="background-color: #ffff00;"></div>
                <div class="color-circle" style="background-color: #ff6600;"></div>
                <div class="color-circle" style="background-color: #ff0000;"></div>
                <div class="color-circle" style="background-color: #0000ff;"></div>
                <div class="color-circle" style="background-color: #0000ff;"></div>
                <div class="color-circle" style="background-color: #00ffff;"></div>
                <div class="color-circle" style="background-color: #00ffff;"></div>
            </div>
        </div>
        <!-- End Setting Colors For Background-->
        <!-- Start Flexability  -->
        <div class="flexability">
            <h3>قابلية الانتقال والتوجيه</h3>
            <div class="container">
                <div class="card">
                    <i class="fa-solid fa-volume-xmark"></i>
                    <p>كتم الصوت</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-eye-slash"></i>
                    <p>اخفاء الصور</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-image"></i>
                    <p>تلميحات الصور</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-keyboard"></i>
                    <p>لوحة المفاتيح الافتراضية</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-book-open-reader"></i>
                    <p>ديلل القراءة</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-book-open-reader"></i>
                    <p>قناع القراءة</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-eye-slash"></i>
                    <p>ايقاف المؤثرات البصرية</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-droplet-slash"></i>
                    <p>تمييز مرور المؤشر</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-droplet-slash"></i>
                    <p>تمييز التركيز</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-arrow-pointer"></i>
                    <p>مؤشر كبير داكن</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-arrow-pointer"></i>
                    <p>مؤشر كبير فاتح</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-droplet-slash"></i>
                    <p>تشبع منخفض</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-droplet-slash"></i>
                    <p>تشبع منخفض</p>
                </div>
                <div class="card">
                    <i class="fa-solid fa-arrows-up-down-left-right"></i>
                    <p>مفاتيح التنقل</p>
                </div>
            </div>
        </div>
        <!-- End Flexability  -->
        <!-- Start Translate The Page  -->
        <div  class="select-container">
            <h3>ترجمة هذه الصفحة</h3>
            <div class="language-select-container">
                <p class="chooseLangParag" style="margin-bottom: 0;">اختر اللغة</p>
            <select id="language-select">
                <option value="ar">Arabic - العربية</option>
                <option value="en">English</option>
                <option value="fr">French - Français</option>
                <option value="es">Spanish - Español</option>
                <option value="de">German - Deutsch</option>
                <option value="zh">Chinese - 中文</option>
                <option value="ja">Japanese - 日本語</option>
                <option value="ru">Russian - Русский</option>
                <option value="pt">Portuguese - Português</option>
                <option value="hi">Hindi - हिन्दी</option>
                <option value="bn">Bengali - বাংলা</option>
                <option value="pa">Punjabi - ਪੰਜਾਬੀ</option>
                <option value="ur">Urdu - اردو</option>
                <option value="id">Indonesian - Bahasa Indonesia</option>
                <option value="fa">Persian - فارسی</option>
                <option value="tr">Turkish - Türkçe</option>
                <option value="it">Italian - Italiano</option>
                <option value="ko">Korean - 한국어</option>
                <option value="nl">Dutch - Nederlands</option>
                <option value="pl">Polish - Polski</option>
                <option value="vi">Vietnamese - Tiếng Việt</option>
                <option value="th">Thai - ไทย</option>
                <option value="sv">Swedish - Svenska</option>
                <option value="no">Norwegian - Norsk</option>
                <option value="da">Danish - Dansk</option>
                <option value="fi">Finnish - Suomi</option>
                <option value="el">Greek - Ελληνικά</option>
                <option value="he">Hebrew - עברית</option>
                <option value="uk">Ukrainian - Українська</option>
                <option value="ro">Romanian - Română</option>
                <option value="hu">Hungarian - Magyar</option>
                <option value="cs">Czech - Čeština</option>
                <option value="sk">Slovak - Slovenčina</option>
                <option value="hr">Croatian - Hrvatski</option>
                <option value="sr">Serbian - Српски</option>
                <option value="bg">Bulgarian - Български</option>
                <option value="sl">Slovenian - Slovenščina</option>
                <option value="et">Estonian - Eesti</option>
                <option value="lv">Latvian - Latviešu</option>
                <option value="lt">Lithuanian - Lietuvių</option>
            </select>
            </div>
        <!-- End Translate The Page  -->
        <!--Start Explorer The Links  -->
        <h3>استكشاف الروابط</h3>
        <div class="language-select-container">
            <p style="margin-bottom: 0;">اختر من القائمة</p>
            <select id="language-select">
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
                <option value="link">Link</option>
            </select>
        </div>

        </div>
        <!-- End Explorer The Links   -->
        <!-- Start Footer  -->
        <div class="myFooter">
            <div class="box">
                <i class="fa-solid fa-rotate-left"></i>
                <p class="text">اعادة ضبط <br>الاعدادات</p>
            </div>
            <div class="box">
                <i class="fa-solid fa-eye-slash"></i>
                <p>لا تظهر أدوات</p>
            </div>
        </div>
    </div>
<!-- ! End Sidebar -->






















<!-- ! Start FOtter -->
    <footer class="footer footer-inner">
        <div class="container-fluid">
            <div class="footer-cont">
                <div class="side-one">
                    <a class="footer-logo" href="https://www.vision2030.gov.sa/ar/v2030/vrps/qol/" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 198.425 132.744" style="version:1; height:40px;">
                            <switch>
                                <foreignObject width="1" height="1" x="0" y="0" requiredExtensions="http://ns.adobe.com/AdobeIllustrator/10.0/"></foreignObject>
                                <g>
                                    <path fill="#ADA9AE" d="M141.468 75.372v0.145c0 9.575-6.977 16.611-18.315 16.611 -9.157 0-15.48-3.627-19.84-8.777l7.704-7.327c3.488 3.918 7.122 6.093 12.282 6.093 4.215 0 7.195-2.393 7.195-6.165 -0.056-0.1 0.822-6.6-9.957-6.6h-4.651l-1.745-7.11 16.32-16.385 -6.54 4.2h-17.628v-9.574h34.375v8.414l-12.863 12.258C134.709 62.316 141.468 65.942 141.468 75.372M109.563 17.265c0-4.597 3.302-8.297 7.936-8.297h5.222l-1.367 0.681c0.062 12.6-0.25 14.187 0.556 16.029l-4.353-0.001c-4.634 0-7.994-3.758-7.994-8.354V17.265zM117.499 27.788h4.637v-1.667c0.338 0.586 0.793 1.041 1.464 1.359 1.475 0.7 0.403 0.26 42.703 0.39V6.858h-2.231v18.785c0 0-38.678 0.149-39.413-0.181 -0.638-0.287-0.87-0.943-0.976-1.642 -0.169-1.135-0.066-1.156-0.097-16.962h-6.029c-6.169 0-10.369 4.885-10.369 10.465v0.058C107.188 22.96 111.33 27.788 117.499 27.788M182.746 4.63h-4.888c0.106-1.616-0.642-4.63 2.651-4.63h1.574v1.552c-1.524 0.113-2.401-0.371-2.401 0.846v0.515h3.064V4.63zM188.396 28.097c-0.595 0-1.2-0.083-1.813-0.251l0.586-2.143c1.009 0.287 2.299 0.304 2.831-0.598 0.621-1.068 0.399-1.444 0.432-18.247h2.231c-0.064 15.685 0.154 16.123-0.209 17.941C192.026 26.926 190.736 28.097 188.396 28.097M123.586 4.63h-2.232V2.403h2.232V4.63zM172.692 12.314c1.047-2.039 3.245-3.174 5.492-3.198V9.112h3.605l-1.274 0.635v10c-1.756-0.052-2.99 0.167-4.616-0.349 -2.22-0.698-3.724-2.406-3.724-4.885C172.175 13.716 172.347 12.982 172.692 12.314M172.692 20.206c2.905 2.07 6.866 1.728 7.073 1.767l0.75-1.571c-0.121 1.523 0.419 3.651-1.004 4.717 -1.203 0.899-3.348 0.87-4.672 0.556 -0.548-0.129-1.176-0.352-1.883-0.667l-0.697 2.143c1.414 0.521 2.766 0.946 4.267 0.946 2.176 0 3.756-0.515 4.742-1.544 0.985-1.031 1.478-2.418 1.478-4.162V6.858c-2.746 0.086-4.829-0.277-7.335 0.585C168.696 9.752 168.562 17.266 172.692 20.206M119.123 4.63h-2.232V2.403h2.232V4.63zM96.028 6.834h2.304v20.911h-1.886L81.607 8.936l1.312 3.048v15.761h-2.305V6.834h2.215l14.17 17.99 -0.971-2.566V6.834zM9.009 27.894L0 6.834h2.634c0.134 0.324 7.314 17.679 8.042 19.439l-0.228-2.298 7.121-17.141h2.543l-9.008 21.06H9.009zM48.932 6.834h2.364v20.91h-2.364V6.834zM74.29 17.349c0 4.75-3.411 8.573-8.2 8.573s-8.261-3.883-8.261-8.632v-0.061c0-4.749 3.413-8.572 8.201-8.572 4.789 0 8.26 3.883 8.26 8.633V17.349zM66.09 6.476c-6.375 0-10.715 5.048-10.715 10.814v0.059c0 5.765 4.28 10.754 10.655 10.754s10.715-5.048 10.715-10.813v-0.061C76.745 11.465 72.465 6.476 66.09 6.476M45.031 22.069v0.059c0 3.584-2.993 5.915-7.153 5.915 -3.322 0-6.045-1.105-8.559-3.346l1.466-1.732c2.185 1.971 4.28 2.956 7.184 2.956 2.813 0 4.668-1.492 4.668-3.554v-0.059c0-5.836-12.48-1.986-12.48-10.008v-0.059c0-3.286 2.903-5.706 6.884-5.706 3.052 0 5.237 0.867 7.362 2.57l-1.376 1.821c-4.75-3.864-10.506-2.384-10.506 1.106v0.06c0 1.971 1.077 3.076 5.687 4.062C42.877 17.17 45.031 18.873 45.031 22.069M25.688 27.745h-2.364V6.835h2.364V27.745zM163.293 30.096h2.232v2.227h-2.232V30.096zM158.83 30.096h2.232v2.227h-2.232V30.096zM18.71 81.609h19.398v9.649H0.461v-8.85c18.811-15.964 25.728-19.084 25.728-26.04 0-4.28-2.835-6.601-6.832-6.601 -3.925 0-6.614 2.175-10.393 6.817l-7.849-6.309C6.13 43.456 11 39.756 20.084 39.756c10.538 0 17.515 6.166 17.515 15.669v0.144c0 11.916-9.021 15.757-24.49 28.223l-2.16 1.664L18.71 81.609zM186.725 66.015c0 8.704-6.25 15.813-15.263 15.813 -9.012 0-15.407-7.254-15.407-15.959v-0.145c0-8.704 6.251-15.812 15.262-15.812 9.011 0 15.408 7.254 15.408 15.957V66.015zM171.462 39.612c-15.697 0-27.108 11.823-27.108 26.257v0.146c0 14.435 11.264 26.113 26.963 26.113s27.108-11.824 27.108-26.259v-0.145C198.425 51.29 187.16 39.612 171.462 39.612">
                                    </path>
                                    <path fill="#29367B" d="M68.518 36.272L71.095 38.74 68.622 41.311 66.046 38.843">
                                    </path>
                                    <path fill="#B1AEB3" d="M74.415 38.843L76.99 41.311 79.463 38.74 76.887 36.272">
                                    </path>
                                    <path fill="#D0DA33" d="M70.787 42.084L72.794 44.005 74.718 42.003 72.713 40.082">
                                    </path>
                                    <path fill="#71BA44" d="M78.142 43.183L80.148 45.105 82.072 43.103 80.066 41.181">
                                    </path>
                                    <path fill="#D0DA33" d="M67.507 45.145L69.513 47.067 71.437 45.065 69.433 43.143">
                                    </path>
                                    <path fill="#27B8BE" d="M74.238 45.378L76.243 47.3 78.168 45.298 76.162 43.376">
                                    </path>
                                    <path fill="#71BA44" d="M71.605 47.681L73.016 49.033 74.37 47.624 72.959 46.272">
                                    </path>
                                    <path fill="#58B75E" d="M76.928 48.742L78.339 50.094 79.694 48.685 78.283 47.334">
                                    </path>
                                    <path fill="#3088C8" d="M88.918 37.593H93.36800000000001V42.035000000000004H88.918z" transform="rotate(-10.39 91.135 39.818)"></path>
                                    <path fill="#1E3871" d="M83.133 39.508H86.70299999999999V43.072H83.133z" transform="rotate(-10.376 84.927 41.294)"></path>
                                    <path fill="#27B8BE" d="M89.92 44.395H93.49V47.959H89.92z" transform="rotate(-10.39 91.7 46.17)"></path>
                                    <path fill="#D0DA33" d="M85.014 44.984H87.794V47.758H85.014z" transform="rotate(-10.382 86.413 46.375)"></path>
                                    <path fill="#B1AEB3" d="M90.334 50.169H93.114V52.943999999999996H90.334z" transform="rotate(-10.382 91.733 51.56)"></path>
                                    <path fill="#27B8BE" d="M80.917 46.094H83.69800000000001V48.869H80.917z" transform="rotate(-10.382 82.315 47.485)"></path>
                                    <path fill="#71BA44" d="M85.88 49.669H88.66V52.443999999999996H85.88z" transform="rotate(-10.382 87.28 51.06)"></path>
                                    <path fill="#B1AEB3" d="M82.329 50.08H84.285V52.032H82.329z" transform="rotate(-10.39 83.314 51.066)"></path>
                                    <path fill="#58B75E" d="M86.024 54.05H87.979V56.001H86.024z" transform="rotate(-10.36 87.01 55.033)"></path>
                                    <path fill="#29367B" d="M101.572 52.342L99.659 56.354 103.679 58.262 105.59 54.251">
                                    </path>
                                    <path fill="#27B8BE" d="M95.867 50.489L94.333 53.707 97.557 55.238 99.091 52.02">
                                    </path>
                                    <path fill="#71BA44" d="M98.505 58.416L96.971 61.635 100.196 63.165 101.73 59.947">
                                    </path>
                                    <path fill="#D0DA33" d="M94.28 56.002L93.086 58.508 95.597 59.7 96.792 57.194">
                                    </path>
                                    <path fill="#D0DA33" d="M95.555 63.315L94.36 65.821 96.87 67.013 98.066 64.507">
                                    </path>
                                    <path fill="#3088C8" d="M90.306 54.51L89.111 57.016 91.622 58.208 92.817 55.702">
                                    </path>
                                    <path fill="#B1AEB3" d="M92.237 60.308L91.042 62.814 93.552 64.006 94.748 61.5">
                                    </path>
                                    <path fill="#71BA44" d="M89.217 58.541L88.376 60.304 90.142 61.142 90.983 59.38">
                                    </path>
                                    <path fill="#27B8BE" d="M89.372 64.241H91.324V66.196H89.372z" transform="rotate(-64.552 90.342 65.217)"></path>
                                    <path fill="#3088C8" d="M103.737 71.551L99.835 73.687 101.976 77.582 105.878 75.446"></path>
                                    <path fill="#27B8BE" d="M100.198 66.717L97.068 68.431 98.785 71.555 101.915 69.841">
                                    </path>
                                    <path fill="#27B8BE" d="M97.691 74.686L94.56 76.4 96.278 79.525 99.408 77.811">
                                    </path>
                                    <path fill="#29367B" d="M95.679 70.261L93.242 71.596 94.578 74.028 97.017 72.694">
                                    </path>
                                    <path fill="#58B75E" d="M92.426 76.936L89.988 78.271 91.325 80.703 93.763 79.369">
                                    </path>
                                    <path fill="#1E3871" d="M93.331 66.731L90.893 68.065 92.23 70.499 94.669 69.164">
                                    </path>
                                    <path fill="#D0DA33" d="M91.498 72.56L89.06 73.895 90.397 76.327 92.835 74.993">
                                    </path>
                                    <path fill="#B1AEB3" d="M90.084 69.364L88.369 70.303 89.31 72.014 91.024 71.076">
                                    </path>
                                    <path fill="#27B8BE" d="M87.475 74.114L85.76 75.053 86.7 76.764 88.415 75.826">
                                    </path>
                                    <path fill="#71BA44" d="M89.529 88.1H93.972V92.55099999999999H89.529z" transform="rotate(-82.958 91.74 90.32)"></path>
                                    <path fill="#1E3871" d="M90.424 82.17H93.988V85.74H90.424z" transform="rotate(-82.935 92.21 83.955)"></path>
                                    <path fill="#27B8BE" d="M83.719 87.169H87.28299999999999V90.74H83.719z" transform="rotate(-82.935 85.504 88.955)"></path>
                                    <path fill="#71BA44" d="M85.516 82.459H88.29100000000001V85.239H85.516z" transform="rotate(-82.952 86.912 83.852)"></path>
                                    <path fill="#B1AEB3" d="M78.966 85.97H81.741V88.75H78.966z" transform="rotate(-82.952 80.36 87.365)"></path>
                                    <path fill="#B1AEB3" d="M85.681 78.224H88.457V81.00500000000001H85.681z" transform="rotate(-82.935 87.072 79.615)"></path>
                                    <path fill="#71BA44" d="M80.778 81.88H83.55300000000001V84.661H80.778z" transform="rotate(-82.935 82.168 83.27)"></path>
                                    <path fill="#B1AEB3" d="M82.377 78.518H84.329V80.473H82.377z" transform="rotate(-82.946 83.36 79.498)"></path>
                                    <path fill="#B1AEB3" d="M77.476 80.846H79.428V82.802H77.476z" transform="rotate(-82.952 78.455 81.827)"></path>
                                    <path fill="#D0DA33" d="M76.652 96.502L73.39 93.479 70.362 96.735 73.624 99.757">
                                    </path>
                                    <path fill="#71BA44" d="M76.051 34.279L72.789 31.256 69.761 34.512 73.023 37.534">
                                    </path>
                                    <path fill="#27B8BE" d="M80.133 91.625L77.516 89.2 75.087 91.812 77.704 94.236">
                                    </path>
                                    <path fill="#71BA44" d="M71.765 91.764L69.147 89.339 66.718 91.951 69.335 94.375">
                                    </path>
                                    <path fill="#3088C8" d="M75.337 88.463L73.299 86.575 71.408 88.608 73.445 90.496">
                                    </path>
                                    <path fill="#D0DA33" d="M67.965 87.486L65.928 85.598 64.037 87.631 66.075 89.52">
                                    </path>
                                    <path fill="#D0DA33" d="M77.954 85.126L75.916 83.238 74.025 85.271 76.062 87.159">
                                    </path>
                                    <path fill="#B1AEB3" d="M71.833 85.227L69.795 83.339 67.904 85.372 69.941 87.26">
                                    </path>
                                    <path fill="#1E3871" d="M74.426 82.881L72.993 81.552 71.663 82.983 73.096 84.311">
                                    </path>
                                    <path fill="#B1AEB3" d="M69.087 81.907L67.654 80.579 66.323 82.009 67.756 83.337">
                                    </path>
                                    <path fill="#3088C8" d="M52.797 88.848H57.248V93.28999999999999H52.797z" transform="rotate(-11.333 55.022 91.072)"></path>
                                    <path fill="#27B8BE" d="M59.437 87.709H63.007V91.273H59.437z" transform="rotate(-11.333 61.207 89.493)"></path>
                                    <path fill="#71BA44" d="M52.569 82.934H56.139V86.49799999999999H52.569z" transform="rotate(-11.344 54.34 84.698)"></path>
                                    <path fill="#D0DA33" d="M58.262 83.048H61.042V85.82300000000001H58.262z" transform="rotate(-11.333 59.64 84.44)"></path>
                                    <path fill="#29367B" d="M62.34 81.869H65.12V84.645H62.34z" transform="rotate(-11.333 63.72 83.263)"></path>
                                    <path fill="#3088C8" d="M57.317 78.377H60.097V81.152H57.317z" transform="rotate(-11.333 58.696 79.77)"></path>
                                    <path fill="#71BA44" d="M61.693 78.724H63.649V80.676H61.693z" transform="rotate(-11.314 62.682 79.71)"></path>
                                    <path fill="#B1AEB3" d="M57.934 74.815H59.888999999999996V76.767H57.934z" transform="rotate(-11.344 58.916 75.795)"></path>
                                    <path fill="#27B8BE" d="M44.388 78.715L46.233 74.673 42.182 72.831 40.337 76.873">
                                    </path>
                                    <path fill="#D0DA33" d="M50.122 80.474L51.602 77.231 48.353 75.754 46.872 78.997">
                                    </path>
                                    <path fill="#3088C8" d="M47.351 72.592L48.832 69.349 45.582 67.871 44.102 71.115">
                                    </path>
                                    <path fill="#27B8BE" d="M49.54 71.708H52.316V74.487H49.54z" transform="rotate(-65.485 50.93 73.102)"></path>
                                    <path fill="#B1AEB3" d="M55.002 81.249L56.155 78.724 53.625 77.574 52.472 80.099">
                                    </path>
                                    <path fill="#29367B" d="M48.145 64.418H50.92100000000001V67.197H48.145z" transform="rotate(-65.474 49.535 65.806)"></path>
                                    <path fill="#58B75E" d="M53.538 73.134H56.31399999999999V75.913H53.538z" transform="rotate(-65.474 54.928 74.522)"></path>
                                    <path fill="#D0DA33" d="M51.512 67.369H54.288V70.148H51.512z" transform="rotate(-65.455 52.905 68.762)"></path>
                                    <path fill="#1E3871" d="M56.637 72.312L57.448 70.536 55.668 69.726 54.857 71.503">
                                    </path>
                                    <path fill="#71BA44" d="M55.879 66.948L56.69 65.171 54.91 64.362 54.099 66.139">
                                    </path>
                                    <path fill="#27B8BE" d="M41.902 59.544L45.769 57.344 43.563 53.485 39.697 55.685">
                                    </path>
                                    <path fill="#71BA44" d="M45.522 64.32L48.624 62.555 46.854 59.459 43.753 61.225">
                                    </path>
                                    <path fill="#1E3871" d="M47.897 56.31L50.999 54.545 49.229 51.448 46.128 53.214">
                                    </path>
                                    <path fill="#3088C8" d="M49.982 60.701L52.396 59.326 51.019 56.916 48.604 58.29">
                                    </path>
                                    <path fill="#71BA44" d="M53.124 53.974L55.538 52.599 54.161 50.189 51.746 51.563">
                                    </path>
                                    <path fill="#27B8BE" d="M52.387 64.192L54.803 62.818 53.426 60.407 51.01 61.782">
                                    </path>
                                    <path fill="#D0DA33" d="M54.124 58.334L56.539 56.96 55.162 54.549 52.747 55.924">
                                    </path>
                                    <path fill="#B1AEB3" d="M55.591 61.506L57.29 60.539 56.321 58.844 54.622 59.81">
                                    </path>
                                    <path fill="#71BA44" d="M58.12 56.713L59.82 55.746 58.851 54.051 57.151 55.018">
                                    </path>
                                    <path fill="#71BA44" d="M51.354 38.35H55.796V42.800000000000004H51.354z" transform="rotate(-83.898 53.578 40.576)"></path>
                                    <path fill="#B1AEB3" d="M51.445 45.166H55.008V48.736999999999995H51.445z" transform="rotate(-83.892 53.227 46.953)"></path>
                                    <path fill="#27B8BE" d="M58.066 40.057H61.63V43.628H58.066z" transform="rotate(-83.892 59.848 41.844)"></path>
                                    <path fill="#D0DA33" d="M57.142 45.581H59.917V48.361000000000004H57.142z" transform="rotate(-83.886 58.533 46.972)"></path>
                                    <path fill="#3088C8" d="M63.634 41.961H66.409V44.741H63.634z" transform="rotate(-83.91 65.016 43.35)"></path>
                                    <path fill="#27B8BE" d="M57.047 49.818H59.821999999999996V52.598H57.047z" transform="rotate(-83.886 58.435 51.21)"></path>
                                    <path fill="#71BA44" d="M61.889 46.08H64.664V48.86H61.889z" transform="rotate(-83.886 63.278 47.47)"></path>
                                    <path fill="#1E3871" d="M61.176 50.287H63.128V52.243H61.176z" transform="rotate(-83.892 62.153 51.266)"></path>
                                    <path fill="#B1AEB3" d="M66.039 47.877H67.991V49.834H66.039z" transform="rotate(-83.927 67.016 48.854)"></path>
                                    <path fill="#727A82" d="M86.941 68.235c-2.33-1.342-5.572-1.732-8.931-0.502 -1.468 0.555-2.938 1.524-4.178 2.473 -0.781-4.735-0.4-9.13-0.218-11.289 0.001-0.012 0.015-0.018 0.026-0.011 1.677 1.055 1.207 4.003 0.855 4.846 -0.007 0.017 0.018 0.028 0.03 0.014 2.09-2.468 1.069-5.193 0.64-5.883 -0.078-0.125 0.395 0.254 0.783 0.751 1.461 1.86 0.786 3.949 0.949 3.9 0.004-0.001 0.009 0 0.011-0.005 0.971-1.905 0.659-4.321-1.338-6.066 -0.091-0.077 0.265 0.039 0.378 0.079 1.703 0.591 2.591 2.345 2.579 3.909 0 0.344 1.15-2.938-1.63-4.606 -0.52-0.311-1.054-0.482-1.403-0.524 -0.272-0.029 1.489-0.294 2.726 0.544 0.743 0.501 0.938 1.107 0.919 0.937 -0.216-2.197-2.806-3.065-4.376-2.406 -0.166 0.083 0.618-1.079 2.134-0.989 0.292 0.017 0.562 0.08 0.778 0.183 0.015 0.007 0.029-0.013 0.018-0.026 -1.145-1.309-3.029-1.336-4.029 0.178 -0.007 0.01-0.022 0.01-0.028 0 -0.379-0.679-0.696-1.548-0.833-2.368 -0.033-0.21-0.103 1.015-0.863 2.368 -0.006 0.01-0.02 0.01-0.027 0 -1.003-1.518-2.885-1.488-4.029-0.178 -0.012 0.013 0.002 0.033 0.018 0.026 0.764-0.363 2.207-0.256 2.93 0.781 0.009 0.014-0.004 0.032-0.02 0.025 -1.618-0.68-4.16 0.243-4.375 2.406 -0.001 0.018 0.023 0.028 0.032 0.012 0.072-0.135 0.25-0.423 0.583-0.715 1.294-1.14 3.329-0.81 3.03-0.778 -0.795 0.098-2.427 0.806-3.03 2.424 -0.493 1.322-0.018 3.063 0 2.632 0.016-1.471 0.859-3.441 2.94-3.943 0.016-0.004 0.028 0.018 0.015 0.029 -3.234 2.843-1.278 6.334-1.308 6.058 -0.1-1.127-0.183-3.139 1.686-4.658 0.014-0.011 0.036 0.004 0.026 0.02 -0.429 0.69-1.449 3.415 0.64 5.883 0.012 0.014 0.037 0.003 0.03-0.014 -0.351-0.843-0.821-3.791 0.855-4.846 0.011-0.007 0.025-0.001 0.026 0.011 0.183 2.159 0.564 6.554-0.218 11.289 -2.656-2.03-5.271-3.361-8.639-3.213 -1.694 0.074-3.301 0.567-4.47 1.242 -0.042 0.024-0.011 0.085 0.034 0.071 4.899-1.566 9.139 0.334 13.081 3.614 -1.991 1.735-3.798 3.551-4.848 4.099 -0.245-0.335-0.67-0.528-1.135-0.427 -0.876 0.193-1.085 1.29-0.61 1.762 1.721 1.717 4.412-1.69 7.631-4.538 3.098 2.738 5.913 6.254 7.631 4.538 0.137-0.137 0.296-0.589 0.214-0.948 -0.207-0.896-1.398-1.155-1.959-0.387 -1.01-0.509-2.783-2.301-4.848-4.099 3.97-3.309 8.164-5.156 13.08-3.614C86.952 68.32 86.983 68.259 86.941 68.235">
                                    </path>
                                    <path fill="#ADA9AE" d="M71.265 115.108h1.277v-11.436h-1.277V115.108zM37.804 113.833l-1.995-4.891c2.064-1.747 4.625-1.426 6.273 0l-1.995 4.891H37.804zM30.764 113.833c-1.004-0.03-1.71 0.095-2.642-0.198 -3.451-1.09-2.513-5.888 1.349-5.888h1.293V113.833zM66.268 113.833c-2.057-0.063-3.91 0.363-4.853-1.274 0.186-0.818 0.084-1.552 0.112-5.306H60.25v4.318c0 3.169-3.824 2.963-4.726 1.06 -0.362-0.765-0.141-1.085-0.207-5.378h-1.278v4.414c0 1.44-0.901 2.166-2.155 2.166H49.41v-6.58h-1.277v6.58h-6.705l2.235-5.337c-1.268-1.108-3.033-2.151-4.725-2.151 -1.719 0-3.45 0.998-4.726 2.151l2.251 5.337h-4.422v-7.376h-2.043c-2.888 0-5.285 1.66-5.285 4.381 0 4.843 5.941 4.246 6.051 4.27 -0.02 0.157 0.159 1.251-0.575 1.801 -0.686 0.514-1.911 0.499-2.674 0.319 -0.314-0.075-0.673-0.202-1.077-0.383l-0.399 1.227c0.81 0.298 1.583 0.542 2.442 0.542 2.434 0 3.56-1.214 3.56-3.267v-0.239c20.842-0.131 20.226 0.3 21.496-0.398 0.484-0.265 0.875-0.621 1.173-1.068 0.309 0.51 0.729 0.909 1.261 1.196 1.117 0.599 2.821 0.543 3.856 0.031 0.537-0.265 0.95-0.674 1.237-1.227 0.493 0.842 1.258 1.383 2.794 1.451v0.015h3.687v-11.436h-1.277V113.833zM180.858 113.268c-1.13 2.641-5.02 2.717-6.146-0.04 -0.331-0.809-0.331-1.768 0-2.589 1.363-3.378 6.41-2.408 6.41 1.299C181.122 112.415 181.034 112.859 180.858 113.268M168.47 113.268c-0.977 2.27-3.921 2.606-5.46 1.02 -1.263-1.306-1.226-3.433-0.007-4.716 0.993-1.055 2.524-1.225 3.695-0.742C168.544 109.6 169.159 111.667 168.47 113.268M137.412 113.833c-0.977-0.03-1.685 0.098-2.611-0.206 -3.363-1.105-2.561-5.896 1.349-5.896h1.262V113.833zM186.389 113.833H182c1.37-3.007-0.758-6.532-4.263-6.532 -3.228 0-5.486 3.308-4.167 6.532h-3.959c1.335-2.919-0.688-6.532-4.262-6.532 -3.23 0-5.486 3.309-4.167 6.532h-4.055v-10.161h-1.277v10.161h-4.07c-0.07-1.309 0.297-3.246-0.799-4.469 -0.743-0.838-1.57-1.099-2.698-1.099h-5.651l2.404-4.593h-1.389l-2.452 4.577c0.079 0.508 0.233 0.905 0.575 1.29 6.387 0.054 6.804-0.134 7.559 0.184 0.592 0.245 0.953 0.692 1.078 1.235 0.152 0.654 0.075 1.146 0.096 2.875h-11.814v-7.376h-2.012c-3.384 0-5.268 2.013-5.268 4.333 0 2.907 2.338 4.318 5.268 4.318h25.335c1.879 2.024 5.042 1.852 6.769 0h5.619c1.873 2.018 5.033 1.859 6.77 0h6.497v-11.436h-1.278V113.833zM191.386 115.108h1.277v-11.436h-1.277V115.108zM97.288 117.914H98.6v-1.309h-1.312V117.914zM109.774 113.833l-1.996-4.891c2.048-1.734 4.605-1.441 6.274 0l-1.995 4.891H109.774zM118.825 113.833h-5.427l2.234-5.337c-1.271-1.114-3.042-2.151-4.725-2.151 -1.725 0-3.451 1.003-4.726 2.151l2.251 5.337h-4.756v-7.376h-1.278v8.922c0 1.127-0.082 1.96-1.213 1.96 -0.263 0-0.402-0.023-0.654-0.096l-0.335 1.228c1.067 0.292 2.327 0.194 2.953-0.726 0.566-0.833 0.499-1.983 0.527-2.637h16.427v-11.436h-1.278V113.833zM21.872 107.572c-1.42-1.55-3.064-0.988-3.624-1.115v1.274c0.932 0.036 1.363-0.103 1.995 0.199 0.532 0.256 0.908 0.669 1.062 1.251 0.173 0.669 0.076 1.191 0.104 4.652h-5.987v1.275h7.264c-0.053-4.83 0.132-5.342-0.2-6.421C22.353 108.252 22.148 107.88 21.872 107.572M92.619 117.929h1.312v-1.309h-1.312V117.929zM84.854 113.833c-0.975-0.03-1.684 0.098-2.611-0.206 -3.367-1.106-2.556-5.896 1.349-5.896h1.262V113.833zM98.583 106.457h-1.277v7.376h-4.821v-7.376h-1.277v7.376h-5.077v-7.376c-1.606 0.05-4.002-0.325-5.843 1.234 -2.61 2.211-1.937 7.417 3.832 7.417h14.463V106.457zM90.382 117.929h1.312v-1.309h-1.312V117.929zM5.037 103.672H3.725v1.309h1.312V103.672zM136.446 103.672h-1.312v1.309h1.312V103.672zM86.125 103.672h-1.312v1.309h1.312V103.672zM12.571 117.929h1.312v-1.309h-1.312V117.929zM83.888 103.672h-1.312v1.309h1.312V103.672zM7.274 103.672H5.962v1.309h1.312V103.672zM10.334 117.929h1.312v-1.309h-1.312V117.929zM6.003 113.833c-0.973-0.03-1.686 0.098-2.61-0.206 -3.361-1.102-2.567-5.896 1.349-5.896h1.261V113.833zM13.634 106.457h-1.277v7.376H7.28v-7.376c-1.531 0.047-2.748-0.153-4.183 0.326 -1.203 0.404-2.186 1.147-2.722 2.287 -0.558 1.186-0.457 2.696 0.057 3.664 0.925 1.746 2.86 2.374 4.836 2.374h8.366V106.457zM123.822 115.108h1.277v-11.436h-1.277V115.108zM138.682 103.672h-1.312v1.309h1.312V103.672z">
                                    </path>
                                    <path fill="#ADA9AE" d="M1.251 123.484L2.854 123.484 2.854 128.062 7.193 123.484 9.161 123.484 5.369 127.399 9.33 132.588 7.389 132.588 4.275 128.491 2.854 129.948 2.854 132.588 1.251 132.588">
                                    </path>
                                    <path fill="#ADA9AE" d="M10.792 123.484H12.395V132.588H10.792z"></path>
                                    <path fill="#ADA9AE" d="M14.863 123.484L16.349 123.484 21.236 129.779 21.236 123.484 22.812 123.484 22.812 132.588 21.47 132.588 16.44 126.111 16.44 132.588 14.863 132.588">
                                    </path>
                                    <path fill="#ADA9AE" d="M24.797 128.062v-0.026c0-2.549 1.955-4.709 4.704-4.709 1.59 0 2.568 0.443 3.506 1.236l-1.016 1.21c-2.47-2.098-5.512-0.587-5.512 2.237v0.026c0 1.873 1.237 3.251 3.1 3.251 0.861 0 1.643-0.273 2.203-0.689v-1.704h-2.333v-1.391h3.884v3.823C30.071 134.113 24.797 132.681 24.797 128.062">
                                    </path>
                                    <path fill="#ADA9AE" d="M38.948 131.131c4.254 0 4.203-6.191 0-6.191H37.15v6.191H38.948zM35.547 123.484h3.401c6.508 0 6.42 9.104 0 9.104h-3.401V123.484z">
                                    </path>
                                    <path fill="#ADA9AE" d="M53.155 128.062v-0.026c0-1.769-1.29-3.238-3.102-3.238 -1.811 0-3.075 1.443-3.075 3.212v0.026c0 1.769 1.29 3.239 3.101 3.239C51.891 131.275 53.155 129.831 53.155 128.062M45.297 128.062v-0.026c0-2.562 1.981-4.709 4.782-4.709 2.802 0 4.757 2.121 4.757 4.683v0.026c0 2.562-1.98 4.708-4.783 4.708C47.252 132.744 45.297 130.624 45.297 128.062">
                                    </path>
                                    <path fill="#ADA9AE" d="M56.827 123.484L58.534 123.484 61.31 127.789 64.085 123.484 65.792 123.484 65.792 132.588 64.189 132.588 64.189 126.059 61.31 130.351 61.258 130.351 58.404 126.085 58.404 132.588 56.827 132.588">
                                    </path>
                                    <path fill="#ADA9AE" d="M79.506 128.062v-0.026c0-1.769-1.29-3.238-3.102-3.238 -1.811 0-3.075 1.443-3.075 3.212v0.026c0 1.769 1.29 3.239 3.101 3.239C78.242 131.275 79.506 129.831 79.506 128.062M71.648 128.062v-0.026c0-2.562 1.981-4.709 4.782-4.709 2.802 0 4.757 2.121 4.757 4.683v0.026c0 2.562-1.981 4.708-4.783 4.708C73.603 132.744 71.648 130.624 71.648 128.062">
                                    </path>
                                    <path fill="#ADA9AE" d="M83.179 123.484L89.968 123.484 89.968 124.941 84.782 124.941 84.782 127.425 89.382 127.425 89.382 128.881 84.782 128.881 84.782 132.588 83.179 132.588">
                                    </path>
                                    <path fill="#ADA9AE" d="M95.027 131.262l0.964-1.145c0.873 0.755 1.746 1.184 2.88 1.184 0.99 0 1.616-0.455 1.616-1.145v-0.025c0-2.034-5.096-0.646-5.096-4.111v-0.026c0-2.794 3.911-3.507 6.425-1.495l-0.86 1.209c-2.098-1.561-3.961-0.947-3.961 0.144v0.026c0 2.005 5.095 0.737 5.095 4.096V130C102.09 133.192 97.564 133.521 95.027 131.262">
                                    </path>
                                    <path fill="#ADA9AE" d="M109.296 128.973l-1.577-3.642 -1.564 3.642H109.296zM107.002 123.419h1.485l4.014 9.169h-1.694l-0.925-2.198h-4.314l-0.937 2.198h-1.643L107.002 123.419z">
                                    </path>
                                    <path fill="#ADA9AE" d="M113.444 128.726v-5.242h1.603v5.177c0 1.691 0.873 2.601 2.306 2.601 1.421 0 2.294-0.858 2.294-2.536v-5.242h1.603v5.164c0 2.718-1.538 4.084-3.923 4.084C114.955 132.732 113.444 131.366 113.444 128.726">
                                    </path>
                                    <path fill="#ADA9AE" d="M126.997 131.131c4.254 0 4.204-6.191 0-6.191h-1.798v6.191H126.997zM123.596 123.484h3.401c6.508 0 6.42 9.104 0 9.104h-3.401V123.484z">
                                    </path>
                                    <path fill="#ADA9AE" d="M133.875 123.484H135.478V132.588H133.875z"></path>
                                    <path fill="#ADA9AE" d="M147.352 128.973l-1.577-3.642 -1.564 3.642H147.352zM145.058 123.419h1.485l4.014 9.169h-1.694l-0.925-2.198h-4.314l-0.937 2.198h-1.643L145.058 123.419z">
                                    </path>
                                    <path fill="#ADA9AE" d="M156.02 127.997c1.147 0 1.877-0.598 1.877-1.522v-0.026c0-0.975-0.704-1.509-1.89-1.509h-2.332v3.057H156.02zM152.072 123.484h4.065c1.928 0 3.389 0.969 3.389 2.874 -0.037 0.1 0.19 2.108-2.177 2.784l2.463 3.446h-1.89l-2.241-3.174c-0.175 0-2.109 0-2.006 0v3.174h-1.603V123.484z">
                                    </path>
                                    <path fill="#ADA9AE" d="M167.299 128.973l-1.577-3.642 -1.564 3.642H167.299zM165.005 123.419h1.486l4.013 9.169h-1.694l-0.925-2.198h-4.314l-0.937 2.198h-1.643L165.005 123.419z">
                                    </path>
                                    <path fill="#ADA9AE" d="M176.253 131.171c1.095 0 1.759-0.43 1.759-1.249v-0.026c0-0.767-0.612-1.223-1.876-1.223h-2.541v2.498H176.253zM175.784 127.321c1.029 0 1.72-0.403 1.72-1.236v-0.026c0-0.715-0.573-1.157-1.603-1.157h-2.306v2.419H175.784zM172.018 123.484h4.092c1.604 0 2.997 0.75 2.997 2.315 -0.037 0.1 0.164 1.294-1.303 2.055 1.069 0.364 1.811 0.976 1.811 2.211v0.026c0 1.626-1.342 2.497-3.375 2.497h-4.222V123.484z">
                                    </path>
                                    <path fill="#ADA9AE" d="M181.6 123.484H183.203V132.588H181.6z"></path>
                                    <path fill="#ADA9AE" d="M191.041 128.973l-1.577-3.642 -1.564 3.642H191.041zM188.747 123.419h1.485l4.014 9.169h-1.694l-0.925-2.198h-4.314l-0.937 2.198h-1.643L188.747 123.419z">
                                    </path>
                                </g>
                            </switch>
                        </svg></a>
                    <div class="copyright">
                        © 2023 وزارة الشؤون البلدية والقروية والإسكان
                    </div>
                </div>
                <div class="side-two">
                    <ul class="list-unstyled">
                        <li><a href="https://balady.gov.sa/form/contact-us">اتصل بنا </a></li>
                        <li><a href="https://balady.gov.sa/node/11026" target="_blank">شروط الاستخدام</a></li>
                        <li><a href="https://balady.gov.sa/node/11293" target="_blank">خريطة الموقع</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>
   <div><div class="gf-app"></div></div>
  



<!-- ! Start Scripte -->
<script>
(function () {
  // مدة الظهور الدنيا (بالمللي ثانية) – عدلها لو تبي
  var MIN_DURATION = 900; 
  var start = Date.now();
  var pre = document.getElementById('preloader');

  // تأكد أنه ظاهر حتى لو فيه CSS ثانية تغيّر display
  document.addEventListener('DOMContentLoaded', function () {
    if (pre) pre.style.display = 'flex';
  });

  function hidePreloader() {
    // يضمن ظهور الحد الأدنى
    var elapsed = Date.now() - start;
    var wait = Math.max(0, MIN_DURATION - elapsed);
    setTimeout(function () {
      document.body.classList.add('loaded');
      // إزالة العنصر نهائيًا بعد انتهاء الأنيميشن
      setTimeout(function () {
        if (pre && pre.parentNode) pre.parentNode.removeChild(pre);
      }, 700);
    }, wait);
  }

  // إخفاء عند اكتمال تحميل الصفحة
  window.addEventListener('load', hidePreloader);

  // خطة طوارئ: لو حدثت مشكلة وما وصل حدث "load"
  setTimeout(hidePreloader, 7000);
})();
</script>










<script>
    document.addEventListener('DOMContentLoaded', () => {
        const params = new URLSearchParams(window.location.search);
        const id = params.get('id');

        if (!id) return;

        const all = JSON.parse(localStorage.getItem('mySystem_certificates') || '{}');
        const d = all[id];
        if (!d) return;

        // تعيين الصورة
        const imgEl = document.getElementById('profileImage');
        if (d.photo) {
            imgEl.src = d.photo;
        }

        // تعبئة الحقول باستخدام الـ ID لكل عنصر
        const fieldsToFill = {
            personName: d.personName,
            idNumber: d.idNumber,
            certNumber: d.certNumber,
            issueDate: d.issueDate,
            issueDateHijri: d.issueDateHijri,
            expireDate: d.expireDate,
            expireDateHijri: d.expireDateHijri,
            programType: d.programType,
            programEnd: d.programEnd,
            nationality: d.nationality,
            job: d.job,
            gender: d.gender,
            amanah: d.amanah,
            baladiah: d.baladiah,
            licenseNumber: d.licenseNumber,
            placeName: d.placeName,
            placeNumber: d.placeNumber
        };

        for (const key in fieldsToFill) {
            const el = document.getElementById(key);
            if (el) el.textContent = fieldsToFill[key] || '';
        }

        // **************** ملاحظة هامة جداً ****************
        // إذا كان لديك JS يعتمد على DOM الذي ينشئه navbar_html.js،
        // مثل navbar.js، فيجب أن تتأكد من أن هذا الـ JS يعمل بعد أن
        // يتم حقن محتوى navbar_html.js.
        // أفضل طريقة هي التأكد من أن DOMContentLoaded يستدعي كل الوظائف.
        // وإذا كانت وظائف navbar.js تحتاج إلى DOM موجود فورًا،
        // فقد تحتاج إلى استدعائها هنا بعد تعبئة الـ navbar-placeholder.
        // مثال: إذا كان navbar.js يحتوي على وظيفة تهيئة، استدعها هنا:
        // initializeNavbarFunctions();
        // أو تأكد أن logic navbar.js يتم تشغيله بعد حقن الـ HTML.
        // ************************************************
    });

    function saveAsImage() {
        const target = document.getElementById('certificateContent');
        html2canvas(target, { scale: 2 }).then(canvas => {
            const link = document.createElement('a');
            link.download = 'شهادة_صحية.png';
            link.href = canvas.toDataURL();
            link.click();
        });
    }

    function downloadCurrentPageWithStyles() {
        // احصل على اسم الشخص من العنصر الذي يحمل ID "personName"
        const personNameElement = document.getElementById('personName');
        let personName = personNameElement ? personNameElement.textContent.trim() : 'مجهول';

        // قم بتنظيف الاسم ليكون مناسبًا لاسم الملف (إزالة المسافات الزائدة، الأحرف غير الصالحة، إلخ)
        personName = personName.replace(/[^a-z0-9\u0600-\u06FF-]/gi, '_').replace(/_+/g, '_');

        // قم بعمل نسخة من مستند HTML الحالي
        const clone = document.documentElement.cloneNode(true);

        // إزالة علامات <link> CSS الخارجية من النسخة المنسوخة (باستثناء Google Fonts و favicon)
        clone.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
            if (!link.href.includes('fonts.googleapis.com') && !link.href.includes('fav.ico')) {
                link.remove();
            }
        });

        // إزالة علامات <script> JS الخارجية من النسخة المنسوخة (باستثناء html2canvas)
        // هذا السطر مهم جداً: تأكد من أنك تزيل جميع السكربتات التي قمت بتضمينها
        // يدويًا في ملف HTML الذي ستقوم بتنزيله، باستثناء html2canvas.
        // إذا كان هناك سكربت معين تريد الاحتفاظ به في الملف المنزّل،
        // فتأكد من إضافته كاستثناء هنا.
        clone.querySelectorAll('script[src]').forEach(script => {
            if (!script.src.includes('html2canvas.min.js')) {
                script.remove();
            }
        });

        // إزالة زر التحميل نفسه لتجنب تكراره في الملف المحمل
        const downloadButton = clone.querySelector('button[onclick="downloadCurrentPageWithStyles()"]');
        if (downloadButton) downloadButton.remove();

        // الحصول على محتوى الـ HTML النهائي للنسخة المنسوخة
        const htmlString = '<!DOCTYPE html>\n' + clone.outerHTML;
        const blob = new Blob([htmlString], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');

        // استخدام اسم الشخص في اسم الملف
        a.href = url;
        a.download = `شهادة_صحية_${personName}.html`;
        a.click();
        URL.revokeObjectURL(url);
    }
</script>











<script>
(function () {
  // مدة الظهور الدنيا (بالمللي ثانية) – عدلها لو تبي
  var MIN_DURATION = 900; 
  var start = Date.now();
  var pre = document.getElementById('preloader');

  // تأكد أنه ظاهر حتى لو فيه CSS ثانية تغيّر display
  document.addEventListener('DOMContentLoaded', function () {
    if (pre) pre.style.display = 'flex';
  });

  function hidePreloader() {
    // يضمن ظهور الحد الأدنى
    var elapsed = Date.now() - start;
    var wait = Math.max(0, MIN_DURATION - elapsed);
    setTimeout(function () {
      document.body.classList.add('loaded');
      // إزالة العنصر نهائيًا بعد انتهاء الأنيميشن
      setTimeout(function () {
        if (pre && pre.parentNode) pre.parentNode.removeChild(pre);
      }, 700);
    }, wait);
  }

  // إخفاء عند اكتمال تحميل الصفحة
  window.addEventListener('load', hidePreloader);

  // خطة طوارئ: لو حدثت مشكلة وما وصل حدث "load"
  setTimeout(hidePreloader, 7000);
})();
</script>










<script>
    document.addEventListener('DOMContentLoaded', () => {
        const params = new URLSearchParams(window.location.search);
        const id = params.get('id');

        if (!id) return;

        const all = JSON.parse(localStorage.getItem('mySystem_certificates') || '{}');
        const d = all[id];
        if (!d) return;

        // تعيين الصورة
        const imgEl = document.getElementById('profileImage');
        if (d.photo) {
            imgEl.src = d.photo;
        }

        // تعبئة الحقول باستخدام الـ ID لكل عنصر
        const fieldsToFill = {
            personName: d.personName,
            idNumber: d.idNumber,
            certNumber: d.certNumber,
            issueDate: d.issueDate,
            issueDateHijri: d.issueDateHijri,
            expireDate: d.expireDate,
            expireDateHijri: d.expireDateHijri,
            programType: d.programType,
            programEnd: d.programEnd,
            nationality: d.nationality,
            job: d.job,
            gender: d.gender,
            amanah: d.amanah,
            baladiah: d.baladiah,
            licenseNumber: d.licenseNumber,
            placeName: d.placeName,
            placeNumber: d.placeNumber
        };

        for (const key in fieldsToFill) {
            const el = document.getElementById(key);
            if (el) el.textContent = fieldsToFill[key] || '';
        }

        // **************** ملاحظة هامة جداً ****************
        // إذا كان لديك JS يعتمد على DOM الذي ينشئه navbar_html.js،
        // مثل navbar.js، فيجب أن تتأكد من أن هذا الـ JS يعمل بعد أن
        // يتم حقن محتوى navbar_html.js.
        // أفضل طريقة هي التأكد من أن DOMContentLoaded يستدعي كل الوظائف.
        // وإذا كانت وظائف navbar.js تحتاج إلى DOM موجود فورًا،
        // فقد تحتاج إلى استدعائها هنا بعد تعبئة الـ navbar-placeholder.
        // مثال: إذا كان navbar.js يحتوي على وظيفة تهيئة، استدعها هنا:
        // initializeNavbarFunctions();
        // أو تأكد أن logic navbar.js يتم تشغيله بعد حقن الـ HTML.
        // ************************************************
    });

    function saveAsImage() {
        const target = document.getElementById('certificateContent');
        html2canvas(target, { scale: 2 }).then(canvas => {
            const link = document.createElement('a');
            link.download = 'شهادة_صحية.png';
            link.href = canvas.toDataURL();
            link.click();
        });
    }

    function downloadCurrentPageWithStyles() {
        // احصل على اسم الشخص من العنصر الذي يحمل ID "personName"
        const personNameElement = document.getElementById('personName');
        let personName = personNameElement ? personNameElement.textContent.trim() : 'مجهول';

        // قم بتنظيف الاسم ليكون مناسبًا لاسم الملف (إزالة المسافات الزائدة، الأحرف غير الصالحة، إلخ)
        personName = personName.replace(/[^a-z0-9\u0600-\u06FF-]/gi, '_').replace(/_+/g, '_');

        // قم بعمل نسخة من مستند HTML الحالي
        const clone = document.documentElement.cloneNode(true);

        // إزالة علامات <link> CSS الخارجية من النسخة المنسوخة (باستثناء Google Fonts و favicon)
        clone.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
            if (!link.href.includes('fonts.googleapis.com') && !link.href.includes('fav.ico')) {
                link.remove();
            }
        });

        // إزالة علامات <script> JS الخارجية من النسخة المنسوخة (باستثناء html2canvas)
        // هذا السطر مهم جداً: تأكد من أنك تزيل جميع السكربتات التي قمت بتضمينها
        // يدويًا في ملف HTML الذي ستقوم بتنزيله، باستثناء html2canvas.
        // إذا كان هناك سكربت معين تريد الاحتفاظ به في الملف المنزّل،
        // فتأكد من إضافته كاستثناء هنا.
        clone.querySelectorAll('script[src]').forEach(script => {
            if (!script.src.includes('html2canvas.min.js')) {
                script.remove();
            }
        });

        // إزالة زر التحميل نفسه لتجنب تكراره في الملف المحمل
        const downloadButton = clone.querySelector('button[onclick="downloadCurrentPageWithStyles()"]');
        if (downloadButton) downloadButton.remove();

        // الحصول على محتوى الـ HTML النهائي للنسخة المنسوخة
        const htmlString = '<!DOCTYPE html>\n' + clone.outerHTML;
        const blob = new Blob([htmlString], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');

        // استخدام اسم الشخص في اسم الملف
        a.href = url;
        a.download = `شهادة_صحية_${personName}.html`;
        a.click();
        URL.revokeObjectURL(url);
    }
</script>

















 <script>
        function copyToClipboard() {
            // إنشاء عنصر مؤقت لنسخ المحتوى
            const tempElement = document.createElement('textarea');
            tempElement.value = document.querySelector('.container').innerText;
            document.body.appendChild(tempElement);
            tempElement.select();
            document.execCommand('copy');
            document.body.removeChild(tempElement);

            // إظهار رسالة تأكيد
            const notification = document.createElement('div');
            notification.textContent = 'تم نسخ المحتوى بنجاح!';
            notification.style.position = 'fixed';
            notification.style.bottom = '20px';
            notification.style.right = '20px';
            notification.style.backgroundColor = '#2ecc71';
            notification.style.color = 'white';
            notification.style.padding = '10px 20px';
            notification.style.borderRadius = '4px';
            notification.style.zIndex = '1000';
            document.body.appendChild(notification);

            // إخفاء الرسالة بعد 3 ثوانٍ
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }
    </script>





<script src="scriptprloder.js"></script>
<script src="main.js"></script>


  </body>
  
  </html>