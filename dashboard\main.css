:root {
  --main-color: #0d6f6d;
  --second-color: #83bd48;
  --text-color: #787676;
  --text-footer: #115a68;
  --light-border: #78767621;
  --details-text-color: #333333;

  --font-weight-value: 600;
  --font-size-value: 0.813rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

@font-face {
  font-family: 'NotoKufiArabic';
  src: url('fonts/NotoKufiArabic-Regular.woff2') format('woff2'),
       url('fonts/NotoKufiArabic-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap; /* يضمن ظهور النص حتى لو لم يتم تحميل الخط بعد */
}
@font-face {
  font-family: 'Ishraq-Light';
  src: url('fonts/alexandria-bold.ttf') format('truetype'); /* تم تعديل هذه القيمة */
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'stv';
  src: url('fonts/stv.ttf') format('truetype'); /* تم تعديل هذه القيمة */
  font-weight: 500;
  font-style: normal;
}


@font-face {
  font-family: 'tanseekAnatherpag';
  src: url('fonts/tanseekarabic.ttf') format('truetype'); /* تم تعديل هذه القيمة */
  font-weight: 500;
  font-style: normal;
}


/* @font-face {
  font-family: 'Ishraq-Light';
  src: url('alfont_com_Somar-ExtraBoldItalic.otf') format('opentype'); 
  font-weight: 500;
  font-style: normal;
}

.header4 {
  width: 400px;
  height: 100px;
  background-color: #417d7f;
}

.header4 h2 {
  font-family: 'Ishraq-Light', sans-serif;
  color: #fff;
  font-size: xx-large;
  transform: scaleY(1.5); 
  transform-origin: center; 
} */


  /*
  alfont_com_Israr-Syria.otf
  alexandria-black.ttf
  alexandria-bold.ttf
  src: url('helvetica-world.ttf') format('truetype'); تم التغيير هنا */



@font-face {
  font-family: 'Ishraq-Light';
  src: url('alexandria-bold.ttf') format('truetype'); 
  font-weight: 500;
  font-style: normal;
}

body {
 
  background-color: #282828;
  font-size: 0.875rem;
    line-height: 1.5;
   
}

h6 {
  font-size: 11px;
  font-weight: 600;
}

.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.h-72 {
  height: 72%;
}
.grid {
  display: grid;
}
.bg-gray {
  background-color: #e0e3e2;
}
.space-between {
  justify-content: space-between;
}
.space-around {
  justify-content: space-around;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}


.max-w-full {
  max-width: 100%;
}
.max-h-full {
  max-height: 100%;
}
.w-285 {
  width: 295px;
}
.w-245 {
  width: 200px;
}

.text-center {
  text-align: center;
}
.text-30 {
  font-size: 25px;
}
.p-8 {
  padding: 8px;
}
.p-10 {
  padding: 10px;
}

.p-6 {
  padding: 6px;
}
.p-4 {
  padding: 4px;
}
.pb-60 {
  padding-bottom: 60px;
}
.px-8 {
  padding-right: 8px;
  padding-left: 8px;
}
.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}
.rounded-6 {
  border-radius: 6px;
}
.rounded-8 {
  border-radius: 8px;
}
.text-decoration {
  text-decoration: none;
}
.bg-white {
  background-color: white;
}

.text-red {
  color: red !important;
}

.text-white {
  color: white !important;
}

.text-right {
  text-align: right !important;
}
.dir-rtl {
  direction: rtl !important;
}
.grid-re-1fr {
  grid-template-columns: repeat(2, 1fr);
}

.flex-1 {
  flex: 1;
}

.mt-30 {
  margin-top: 30px;
}
.mt-40 {
  margin-top: 40px;
}
.mb-30 {
  margin-bottom: 30px;
}
.mb-60 {
  margin-bottom: 60px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mb-10 {
  margin-bottom: 10px;
}
.mt-10 {
  margin-top: 10px;
}
.-mt-10 {
  margin-top: -10px;
}
.mb-30 {
  margin-bottom: 30px;
}
.mr-30 {
  margin-right: 30px;
}
.mr-15 {
  margin-right: 15px;
}
.mr-12 {
  margin-right: 12px;
}
.ml-15 {
  margin-left: 15px;
}
.ml-17 {
  margin-left: 17px;
}
.gap-30 {
  gap: 30px;
}
.gap-26 {
  gap: 26px;
}
.gap-35 {
  gap: 35px;
}
.gap-20 {
  gap: 20px;
}
.gap-15 {
  gap: 15px;
}
.gap-10 {
  gap: 10px;
}
.gap-8 {
  gap: 8px;
}
.gap-5 {
  gap: 5px;
}
.gap-2 {
  gap: 2px;
}
.gap-3 {
  gap: 3px;
}
.text-details-color {
  color: var(--details-text-color);
}
.flex-column {
  flex-direction: column;
}

.btn-div {
  text-align: center;
}

.download-btn {
  display: inline-block;
  margin: 0 0 1rem 0;
  padding: 0.5rem 1rem;
  background: #0e7270;
  color: #fff;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 600;
}
.download-btn:hover {
  opacity: 0.92;
}

/* .app {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  padding: 10px 0 10px 0;
} */

.container {
  position: relative;
  max-width: 51.125rem;
  margin: 0 auto;
  height: auto;
  background-color: #f0f0f0;
  overflow: hidden;
  z-index: 2;
      /* margin-bottom: 26px; */
}


/* .container.top::before {
  content: "";
  position: absolute;
  inset: 0% 0% 0% 50%;
  pointer-events: none;
  z-index: 0;
  background: repeating-radial-gradient(
    circle at -62% 46%,
    rgb(255 255 255 / 25%) 0 3px,
   transparent 5px 25px
 
  );
  transform: rotate(52deg);
  width: 100%;
  height: 123%;
} */




/* .background-shape.top::after {
  content: "";
  position: absolute;
  top: -25px;
  right: -49.5%;
  bottom: 0;
  width: 100%;
  background: url(./Imgs/nakhla.png) no-repeat center center;
  opacity: 0.1;
  background-size: auto 100%;
  pointer-events: none;
  height: 146%;
} */





.container.bottom::before {
  content: "";
  position: absolute;
  top: -34px;
  left: -49.5%;
  bottom: 0;
  width: 100%;
  background: url(imgs/nakhla.png) no-repeat center center;
  opacity: 0.2;
  background-size: auto 100%;
  pointer-events: none;
  /* height: 146%; */
  height: auto;
}

header {
  gap: 140px;
  
}
header .div-tilte {
font-family: 'NotoKufiArabic';
    background-color: #0e7270;
    padding: 22px 46px;
     padding: 6px 35px;
    font-size: xx-large;
    text-transform: uppercase;
    /* margin-top: -20px; */
  transform: scaleY(2.0);
    
}
.div-tilte h1 {
  font-size: 29px;
  font-weight: 600;
}
.div-img {
  width: 80px;
  position: relative;
}
.div-img img {
  z-index: 100;
}
.middel-imgs {
  border-right: 1px solid #80808059;
  border-left: 1px solid #80808059;
  margin: 0 5px;
}

.details {
  padding: 0 17px;
}
.details h1 {
  color: #0e7270;
}
.text-55 {
  font-size: 42px;
  font-weight: 500;
  width: 360px;
  text-align: right;
  line-height: 1.6em;
}
.border-green {
  border: 1px solid var(--second-color);
}
.text-contact {
  color: #6e6e6e;
}

.left-photos {
  width: 166px;
}
.left-photos div {
  height: 176px;
}
.shapeInput {
  padding: 4px 5px;



}
.shapeInput h4 {
   font-family: 'stv', sans-serif;
  color: var(--details-text-color);
 font-size: 18px;
    font-weight: 400;
}
.shapeLabel {
   font-family: 'stv', sans-serif;
  margin-right: 4px;
  margin-bottom: 4px;
 font-size: 18px;
    font-weight: 400;
}

.contact-row {
  position: relative;
  flex-wrap: nowrap;
  border-right: 1.5px solid var(--main-color);
  border-bottom: 1.5px solid var(--main-color);
  border-radius: 0 0 30px;
  margin: 10px 16px 0;
  padding-bottom: 5px;
  margin-bottom: 2.5px;
  gap: 45px;
}

/* أيقونة الهاتف (زي ما هي) */
.contact-row .div-has-iphone {
  width: 33px;
  height: 33px;
  border: 2px solid var(--second-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.contact-row .div-has-iphone i {
  font-size: 18px;
  color: var(--second-color);
}

.care-span {
  font-size: 10px;
  font-weight: 700;
}

/* باقي الأيقونات */
.parent-icon {
  width: 23px;
  height: 23px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--second-color);
  border-radius: 50%;
}
.parent-icon i {
  font-size: 11.5px;
  color: var(--second-color);
}

/* النصوص بجانب الأيقونات */
.text-footer {
  color: var(--text-footer);
    font-family: 'NotoKufiArabic' !important;

  font-weight: 600;
  font-size: 19px;
  white-space: nowrap;
}

/* فاصل الهاتف */
.details-contact {
  padding-right: 6px;
  color: #6e6e6e;
  border-right: 1px dashed var(--text-footer);
}

.container.bottom {
  background-color: #4a8484;
  padding: 5px 18px;
}

.content-text {
  padding: 0 32px 45px 20px;
}

.last-div {
  width: 100%;
  height: 25px;
}

.footer-text {
    font-family: 'NotoKufiArabic';
  font-size: 23px;
  line-height: 1.6em;
  direction: rtl;
  font-weight: 400;
}





.text-footer,
.text-55
{
  font-family: 'NotoKufiArabic';
}









.background-shape {
    position: absolute;
    inset: -65% -12% -22% -4%;
    pointer-events: none;
    
    background: url(./Imgs/backgroundContinerAllC.png) no-repeat center;
    background-size: cover;
    transform: rotate(9deg);
    width: 107%;
    height: 149%;
    margin-top: 288px;
    z-index: -2;
}









.background-shape::after {
  content: "";
  position: absolute;
  top: -25px;
  right: -49.5%;
  bottom: 0;
  width: 100%;
  background: url(./Imgs/nakhla.png) no-repeat center center;
  opacity: 0.1;
  background-size: auto 100%;
  pointer-events: none;
  height: 112%;
  
}

/* .btnconfar {
    width: 100%; 
    text-align: center; 
} */

/* #btnconfarfBtn {
   
    display: block; 
    width: 100%; 
    padding: 20px 10px; 
    font-size: 24px;
    font-weight: bold;
    text-decoration: none;
    color: white; 
    border: 2px solid #FF3B30; 
    border-radius: 12px; 
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #FF5B50, #D90429); 

    box-shadow: 0 0 10px #FF3B30, 0 0 20px #FF3B30, 0 0 30px #FF3B30, 0 0 40px #FF3B30;
    
    animation: pulse 1.5s infinite;
} */

#downloadPdfBtn:hover {
    /* تأثير عند مرور الماوس */
    transform: scale(1.05); /* تكبير الزر قليلاً */
    box-shadow: 0 0 15px #FF3B30, 0 0 30px #FF3B30, 0 0 45px #FF3B30, 0 0 60px #FF3B30;
}

/* تعريف تأثير النبض */
@keyframes pulse {
    0% {
        box-shadow: 0 0 5px #FF3B30, 0 0 10px #FF3B30;
    }
    50% {
        box-shadow: 0 0 20px #FF3B30, 0 0 30px #FF3B30, 0 0 40px #FF3B30;
    }
    100% {
        box-shadow: 0 0 5px #FF3B30, 0 0 10px #FF3B30;
    }
}


/* stv.ttf 
din-next-lt-arabic-light.ttf
lama-sans-bold-italic.otf
*/


@font-face {
  font-family: 'arabic_light';
  src: url('fonts/din-next-lt-arabic-light.ttf') format('truetype'); /* تم تعديل هذه القيمة */
  font-weight: 500;
  font-style: normal;
}


.name {
  font-size: 23px !important;
  font-weight: 600 ;
  font-family: 'arabic_light', sans-serif !important;
  font-size: xx-large;
  color: #0e7270;
   
  text-transform: uppercase;
  transform: scaleY(1.0);
    margin-right: -23px;
}




/* #job
{
  padding: 13px;
} */

/* هنا  الرابط  فوق  الباركود  يحولني للموقع   */

.border-green {
  position: relative;
  display: inline-block;
  width: 150px;   /* 🔹 نفس عرض الباركود */
  height: 150px;  /* 🔹 نفس ارتفاع الباركود */
}

.qr {
  width: 90%;
  height: 90%;
  display: block;
}

.qr-link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}



.left-photos div
 {
    height: 164px;
}

 /* .empty-field {
    padding: 11px;
  } */

/* #programEnd,
#programType
{
  padding: 9px;
} */

/* .div-img  #logoall{
  width: 200px;
  height: ;
  position: relative;
} */


/* 
{
  width: 1020%;
  height: 105%;
  object-fit: cover;
 
} */
.content-text {
    padding: 25px 32px 45px 20px;
}


.gap-10 {
    gap: 25px;
}
