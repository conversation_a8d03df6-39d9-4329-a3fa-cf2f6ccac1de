<?php
require_once 'db_connect.php';

header('Content-Type: application/json');

try {
    // جلب جميع الشهادات من قاعدة البيانات
    $stmt = $pdo->query("SELECT * FROM certificates");
    $certificates = $stmt->fetchAll();

    // تحويل الصور إلى base64 للنسخ الاحتياطي
    foreach ($certificates as &$cert) {
        if ($cert['photo']) {
            $cert['photo'] = base64_encode($cert['photo']);
        }
        if ($cert['qr']) {
            $cert['qr'] = base64_encode($cert['qr']);
        }
    }

    echo json_encode($certificates);
} catch(PDOException $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
