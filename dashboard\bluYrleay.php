<?php
require_once '../db_connect.php';
require_once 'auth.php';
require_once '../inc/SecureIdHandler.php';

// الحصول على معرف الشهادة من الرابط
$encrypted_id = isset($_GET['id']) ? $_GET['id'] : '';
$certificate = null;

// جلب بيانات الشهادة من قاعدة البيانات
if (!empty($encrypted_id)) {
    try {
        // فك تشفير الـ ID
        $id = SecureIdHandler::decryptId($encrypted_id);
        if ($id === false) {
            $error = "معرف الشهادة غير صالح";
        } else {
            $stmt = $pdo->prepare("SELECT * FROM certificates WHERE id_number = ?");
            $stmt->execute([$id]);
            $certificate = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // التحقق من وجود البيانات
            if (!$certificate) {
                $error = "لم يتم العثور على الشهادة المطلوبة";
            }
        }
    } catch(PDOException $e) {
        $error = "خطأ في جلب البيانات: " . $e->getMessage();
    }
}

// تحويل الصور إلى base64 للعرض
$photo_base64 = '';
$qr_base64 = '';
$logo_base64 = '';

if ($certificate && !empty($certificate['photo'])) {
    // التحقق من نوع البيانات
    if (is_resource($certificate['photo'])) {
        // إذا كانت البيانات مورد (مثل Blob من قاعدة البيانات)
        $photo_data = stream_get_contents($certificate['photo']);
        $photo_base64 = 'data:image/jpeg;base64,' . base64_encode($photo_data);
    } elseif (strpos($certificate['photo'], 'data:image') === 0) {
        // إذا كانت البيانات بتنسيق data URI بالفعل
        $photo_base64 = $certificate['photo'];
    } elseif (is_string($certificate['photo']) && file_exists($certificate['photo'])) {
        // إذا كانت البيانات مسار ملف
        $photo_base64 = 'data:image/jpeg;base64,' . base64_encode(file_get_contents($certificate['photo']));
    } else {
        // إذا كانت البيانات ثنائية
        try {
            $photo_base64 = 'data:image/jpeg;base64,' . base64_encode($certificate['photo']);
        } catch (Exception $e) {
            // في حالة وجود خطأ في التحويل، استخدام الصورة الافتراضية
            $photo_base64 = '../imgs/person-placeholder.png';
        }
    }
}

if ($certificate && !empty($certificate['qr'])) {
    // التحقق من نوع البيانات
    if (is_resource($certificate['qr'])) {
        // إذا كانت البيانات مورد (مثل Blob من قاعدة البيانات)
        $qr_data = stream_get_contents($certificate['qr']);
        $qr_base64 = 'data:image/png;base64,' . base64_encode($qr_data);
    } elseif (strpos($certificate['qr'], 'data:image') === 0) {
        // إذا كانت البيانات بتنسيق data URI بالفعل
        $qr_base64 = $certificate['qr'];
    } elseif (is_string($certificate['qr']) && file_exists($certificate['qr'])) {
        // إذا كانت البيانات مسار ملف
        $qr_base64 = 'data:image/png;base64,' . base64_encode(file_get_contents($certificate['qr']));
    } else {
        // إذا كانت البيانات ثنائية
        try {
            $qr_base64 = 'data:image/png;base64,' . base64_encode($certificate['qr']);
        } catch (Exception $e) {
            // في حالة وجود خطأ في التحويل، استخدام الصورة الافتراضية
            $qr_base64 = '../imgs/qr-placeholder.png';
        }
    }
}


if ($certificate && !empty($certificate['imgLogo'])) {
    // التحقق من نوع البيانات
    if (is_resource($certificate['imgLogo'])) {
        // إذا كانت البيانات مورد (مثل Blob من قاعدة البيانات)
        $logo_data = stream_get_contents($certificate['imgLogo']);
        $logo_base64 = 'data:image/png;base64,' . base64_encode($logo_data);
    } elseif (strpos($certificate['imgLogo'], 'data:image') === 0) {
        // إذا كانت البيانات بتنسيق data URI بالفعل
        $logo_base64 = $certificate['imgLogo'];
    } elseif (is_string($certificate['imgLogo']) && file_exists($certificate['imgLogo'])) {
        // إذا كانت البيانات مسار ملف
        $logo_base64 = 'data:image/png;base64,' . base64_encode(file_get_contents($certificate['imgLogo']));
    } else {
        // إذا كانت البيانات ثنائية
        try {
            $logo_base64 = 'data:image/png;base64,' . base64_encode($certificate['imgLogo']);
        } catch (Exception $e) {
            // في حالة وجود خطأ في التحويل، استخدام الصورة الافتراضية
            $logo_base64 = '../imgs/qr-placeholder.png';
        }
    }
}




?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الشهادة الصحية</title>
    <link rel="stylesheet" href="main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" rel="stylesheet"/>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100..900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="fonts/notokufiarabic-csrk4ydqnpyadxexlff6lzvlkrodri0fflkp.woff2">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100..900&display=swap" rel="stylesheet">
<link rel="stylesheet" href="print.css" media="print">

<style>


@font-face {
  font-family: 'extralight';
  src: url('fonts/cairo-extralight.ttf') format('truetype');
  font-weight: normal;

}

@font-face {
  font-family: 'NotoKufiArabic';
  src: url('fonts/NotoKufiArabic-Regular.woff2') format('woff2'),
       url('fonts/NotoKufiArabic-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap; /* يضمن ظهور النص حتى لو لم يتم تحميل الخط بعد */
}
@font-face {
  font-family: 'Ishraq-Light';
  src: url('fonts/alexandria-bold.ttf') format('truetype'); /* تم تعديل هذه القيمة */
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'stv';
  src: url('fonts/stv.ttf') format('truetype'); /* تم تعديل هذه القيمة */
  font-weight: 500;
  font-style: normal;
}


@font-face {
  font-family: 'tanseekAnatherpag';
  src: url('fonts/tanseekarabic.ttf') format('truetype'); /* تم تعديل هذه القيمة */
  font-weight: 500;
  font-style: normal;
}


@font-face {
  font-family: 'Ishraq';
  src: url('fonts/alexandria-bold.ttf') format('truetype'); 
  font-weight: 500;
  font-style: normal;
}


@font-face {
  font-family: 'arabicght';
  src: url('fonts/arabicght.ttf') format('truetype'); 
  font-weight: 500;
  font-style: normal;
}



@font-face {
  font-family: 'neo';
  src: url('fonts/neo.ttf') format('truetype'); 
  font-weight: 500;
  font-style: normal;
}


@font-face {
  font-family: 'cairo';
  src: url('fonts/cairo-bold.ttf') format('truetype'); 
  font-style: normal;
}



@font-face {
  font-family: 'extralight';
  src: url('fonts/cairo-extralight.ttf') format('truetype');
  font-weight: normal;

}













.shapeInput h4 {
      font-family: 'extralight' !important;

  /* color: var(--details-text-color); */
  font-size: 15px;
  font-weight: 600;
  line-height: 26px;
  color: #000;
}



.shapeLabel {
      font-family: 'cairo' !important;

  margin-right: 4px;
  margin-bottom: 4px;
  font-size: 15px;
  font-weight: 600;
  /* transform: scaleY(1.2);
     transform: scaleX(1.2);
     margin-right: 36px; */

     
}
























  .qr-link {
  cursor: pointer;
}

/* مثال على تعديل ملف CSS */
/*.div-tilte h1 {*/
/*  text-align: center;*/
/*  margin-top: -16px; */
/*}*/








.border-green {
    border: 2px solid #9bde57;
}


.div-img {
    width: 86px;
    position: relative;
}


.w-245 {
    width: 288px;
}


.max-w-full {
    max-width: 116%;
}

#imgblady
{
  width: 116%;  
}

.w-245 {
    /* قم بزيادة هذه القيمة (مثلاً 400px أو 300px) */
    width: 350px; 
}



.name {
    font-size: 24px !important;
    font-weight: 600;
    font-family: 'extralight', sans-serif;
    color: #0e7270;
       padding-top: 41px;
    margin-bottom: 10px;

}



.div-img {
    /* يمكنك تحديد عرض ثابت لجميع حاويات الصور هنا */
    width: 100px; /* مثال: حدد عرضًا معقولًا */
    height: auto; /* لضمان مرونة الارتفاع */
    position: relative;
}

/* للتأكد من أن الصور داخلها تتناسب مع هذا العرض */
.div-img img {
    width: 100%; 
    height: auto;
    z-index: 100;
    /* إزالة max-w-full من الصورة إذا كنت تريد حجمًا ثابتًا */
    /* max-width: 100%; */
}

/* يمكنك تخصيص صورة الشعار (logoall) بشكل خاص إذا أردت */
.div-img #logoall {
     width: 100px; /* نفس عرض الحاوية أو أقل بقليل */
     height: auto;
}


.div-img {
    width: 117px;
    /* position: relative; */
}

.div-img {
    /* width: 100px; */
    height: 72px;
    position: relative;
    margin-left: 22px;
}


.space-between {
    /* استبدل space-between بـ flex-start أو center */
    /*justify-content: flex-start; */
    
    /* استخدم خاصية gap لتحديد المسافة بين الصور الثلاث */
    gap: 20px; /* يمكنك تغيير هذه القيمة (20px) حسب رغبتك */
}

/* 🖼️ التحكم بحجم الصورة الأولى (الشعار) */
#logoall {
    width: 120px; /* مثال: عرض كبير نسبيًا */
    height: auto;
}

/* 🖼️ التحكم بحجم الصورة الثانية (بلدي) */
#imgblady {
    width: 80px; /* مثال: عرض أصغر */
    height: auto;
}

/* 🖼️ التحكم بحجم الصورة الثالثة (AISCAN3) */
#imgthird {
    width: 100px; /* مثال: عرض متوسط */
    height: auto;
}


/* 📏 الصورة الأولى: قريبة من الثانية */
#logoall {
    /* (إذا كنت تريد تقريبها، قلل الهامش أو اعتمد على الـ gap) */
    margin-right: 15px; /* أو لا تضع شيئًا وتعتمد على الـ gap */
    width: 120px;
    height: auto;
}

/* 📏 حاوية الصورة الوسطى: للتحكم بمسافة ما قبلها وما بعدها */
.middel-imgs {
    /* المسافة بين الصورة الثانية والثالثة */
    margin-right: 40px; /* مثال: مسافة بعيدة */
    margin-left: 0;
    
    /* إلغاء الحدود (إذا لم تعد تريدها) */
    border-right: none;
    border-left: none;
}

/* 📏 الصورة الثالثة: لا تحتاج لهامش أيمن بعدها */
#imgthird {
    width: 100px;
    height: auto;
}



.middel-imgs {
    margin-right: -20px;
    margin-left: 45px;
    /* border-right: none; */
    /* border-left: none; */
}


#imgblady {
    width: 172px;
    /* height: 137px; */
    margin-top: -5px;
}

.max-w-full {
    max-width: 116%;
    padding: 1px;
}

#qr
{
   padding: 8px; 
   padding-left: 20px;
}

.middel-imgs {
    margin-right: -10px;
    margin-left: 77px;
    /* border-right: none; */
    /* border-left: none; */
}


.div-img #logoall {
    width: 100px;
   height: 77px;
    margin-left: 60px;
        margin-top: -5px;
        position: absolute;
       top: 12px;
    left: 18px;
}
.div-img #logoall {
    width: 89px;
    height: auto;
    margin-left: 66px;
}

#imgblady {
    width: 61px;
    height: 96px;
    margin-top: -1px;
    margin-left: 18px;
}

#askan{
    
   
   width: 153px;
    height: 62px;
    /* max-width: 100%; */
}
#askan img{
    
   
   
    height: 100px;
   
}

#logoalldiv
{
      position: relative;
  
}

#imgblady {
    width: 63px;
    height: 86px;
    margin-top: 0px;
    margin-left: 18px;
}


.sprotor {
    height: 80px;
    border-left: 1px solid #c5c5c5;
    width: 0;
    margin: -42px 252px;
    position: absolute;
}



.sprotorgren {
    height: 86px;
    border-left: 3px solid #0e7270;
    width: 0;
    margin: -36px 256px;
    position: absolute;
    margin-right: -19px;
    margin-left: 360px;
    margin-top: -14px;
}









.space-between {
    gap: -1px;
}
@font-face {
  font-family: 'Somar';
  src: url('fonts/Somar.otf') format('opentype'); 
  /*font-weight: 500;*/
  font-style: normal;
}


@font-face {
  font-family: 'ArbFONTS';
  src: url('fonts/ArbFONTS.otf') format('opentype'); 
  /*font-weight: 500;*/
  font-style: normal;
}



@font-face {
  font-family: 'lama';
  src: url('fonts/lama.otf') format('opentype'); 
  font-weight: 500;
  font-style: normal;
}


@font-face {
  font-family: 'myTitle';
  src: url('fonts/tanseek-modern-pro-arabic-medium.ttf') format('truetype');
   font-weight: 100;
 }


@font-face {
  font-family: 'tanseekght';
  src: url('fonts/tanseekght.ttf') format('truetype');
   font-weight: 100;
 }
#tilte
{
   font-family: 'myTitle' !important;
    background-color: #0e7270;
    padding: 13px 34px;
    margin-top: 5px;
    transform: scaleY(1.2);
    transform: scaleX(1.4);
    margin-bottom: -28px;
    margin-left: 22px;
    font-weight: 300;
    
}

#tilte h1 {.
   font-family: 'myTitle' !important;
    transform: scaleY(1.5);
    font-size: 42px;
    font-weight: 300;
    margin-bottom: 9px;
    margin-left: 50px;
     font-variation-settings: "wght" 100;
}

.w-285 {
    width: 285px;
}

.w-245 {
    width: 350px;
    margin-top: 8px;
}


.left-photos {
    width: 185px;
    margin-top: 8px;
    position: relative;
    top: 10px;
    left: 7px;
    width: 207px;
    margin-top: 10px;
    height: 350px;
}


.left-photos div {
    height: 177px;
    width: 166px;

}

.border-green {
    border: 2px inset #a2d76d;
}

.footer-text {
    font-size: 23px;
    line-height: 2.1em;
    direction: rtl;
    font-weight: 400;
}

#alltext
{
    width: 188px;
    height: 35px;
    margin-top: 18px;
    margin-right: 6px;
 
    
}

#photo {
    padding: 1px;
    padding-top: 2px;
}

#qr {
       padding: 6px;
    padding-left: 15px;
}



.border-green {
    border: 2px solid #78b73885;
}

#askan {
    width: 139px;
    height: 62px;
    /* max-width: 100%; */
}


#askan img {
    height: 104px;
    margin-top: -6px;
}

#imgblady {
    width: 60px;
    height: 93px;
    margin-top: 0px;
    margin-left: 12px;
}

.div-img #logoall
 {
    width: 89px;
    height: auto;
    margin-left: 56px;
}

#botomaskan
{
    width: 103px;
    height: 88px;
}




.shapeLabel {
    font-family: 'cairo' !important;
    margin-right: 4px;
    margin-bottom: 3px;
    font-size: 15px;
    font-weight: 600;
}




.contact-row {
    position: relative;
    flex-wrap: nowrap;
    border-right: 1.5px solid var(--main-color);
    border-bottom: 1.5px solid var(--main-color);
    border-radius: 0 0 30px;
    margin: 10px 16px 0;
    padding-bottom: 5px;
    margin-bottom: 2.5px;
    gap: 45px;
    margin-left: 27px;
}

.details-contact {
    padding-right: 6px;
    color: #6e6e6e;
    border-right: 1px dashed var(--text-footer);
    height: 25px;
}



header .div-tilte {
    /*background-color: #383585;*/
        background-color: #3c3a72;

    /*background-color: rgba(65, 85, 135);*/
    /* padding: 13px 23px;
    font-size: xx-large;
    text-transform: uppercase;
    transform: scaleY(1.0);
    margin-bottom: 11px; */
}

.container.bottom {
      /*background-color: #4744a0;*/
    /*background-color: rgba(65, 85, 135);*/
        background-color: #3c3a72;

    /* padding: 5px 18px; */
}



</style>
</head>
  <body>

 

   <div class="btn-div">
        <a id="downloadPdfBtn" class="download-btn"   onclick="downloadPDF()"  type="button">Download PDF</a>
      </div>
    <!-- <div id="content-to-export" class="app"> -->

      <div id="top" class="container top">
                <div class="bg-nakhla"></div>

                 <div class="background-shape">
                  <img src="../imgs/backgroundContinerAllC.png" alt="" srcset="">
                  <img id="nakhla" src="../imgs/nakhla.png" alt="" srcset="">
                 </div>

        <header class="flex items-center space-between">
            <div id="tilte" class=" tilte text-center">
    <h1 class="text-white">
        <?php echo str_replace(' ', '&nbsp;', '   الشهادة الصحية    '); ?>
    </h1>
</div>


            <div class="flex items-center mr-12 w-245">
              <div class="div-img" id="logoalldiv" >

            <!-- الحالة: صورة Base64 -->
<img id="logoall" class="max-w-full" src="<?php echo $logo_base64; ?>" alt="شعار">     
            <!-- الحالة: مسار صورة -->
      
 
</div>

              <div class="div-img middel-imgs"><img id="imgblady" class="max-w-full" src="../imgs/bl.png" alt=""></div>
              
              <div class="sprotor"></div>
              
                            <div class="sprotorgren"></div>

              
              <div class="div-img"  id="askan"><img class="max-w-full" src="../imgs/a.png" alt=""></div>
            </div>

        </header>

        <div class="details flex items-center space-between">
            <div class="left-photos flex flex-column gap-15">
                <div class="border-green p-4 text-center">
                    <?php if (!empty($photo_base64)): ?>
                        <?php if (strpos($photo_base64, 'data:image') === 0): ?>
                            <img id="photo" class="max-w-full max-h-full w-full h-full" src="<?php echo htmlspecialchars($photo_base64); ?>" alt="صورة شخصية">
                        <?php else: ?>
                            <img id="photo" class="max-w-full max-h-full w-full h-full" src="<?php echo htmlspecialchars($photo_base64); ?>" alt="صورة شخصية" onerror="this.src='../imgs/person-placeholder.png'">
                        <?php endif; ?>
                    <?php else: ?>
                        <img id="photo" class="max-w-full max-h-full w-full h-full" src="../imgs/person-placeholder.png" alt="صورة افتراضية">
                    <?php endif; ?>
                </div>

                <div class="border-green p-4 text-center">
                    <a href="<?php echo !empty($certificate['linkWeb']) ? htmlspecialchars($certificate['linkWeb']) : ''; ?>" target="_blank" class="qr-link">
                        <?php if (!empty($qr_base64)): ?>
                            <?php if (strpos($qr_base64, 'data:image') === 0): ?>
                                <img id="qr" class="max-w-full max-h-full h-full" src="<?php echo htmlspecialchars($qr_base64); ?>" alt="QR Code">
                            <?php else: ?>
                                <img id="qr" class="max-w-full max-h-full h-full" src="<?php echo htmlspecialchars($qr_base64); ?>" alt="QR Code" onerror="this.src='../imgs/qr-placeholder.png'">
                            <?php endif; ?>
                        <?php else: ?>
                            <img id="qr" class="max-w-full max-h-full h-full" src="../imgs/qr-placeholder.png" alt="رمز QR افتراضي">
                        <?php endif; ?>
                    </a>
                </div>
            </div>

            <div>
<div>            
  
<div id="personName" class="name text-right mb-10">
    <?php
        $person_name = htmlspecialchars($certificate['person_name'] ?? '');
        // استبدال المسافات العادية بمسافات غير قابلة للكسر
        echo str_replace(' ', '&nbsp;', $person_name);
    ?>
</div>


</div>
                <div class="flex items-center space-between gap-20 mb-10">
                    <div>
                        <h4 class="text-right text-details-color shapeLabel">الجنسية</h4>
                        <div class="bg-white w-285 shapeInput">
                            <h4 id="nationality" class="text-right"><?php echo htmlspecialchars($certificate['nationality'] ?? ''); ?></h4>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-right text-details-color shapeLabel">   <?php echo str_replace(' ', '&nbsp;', 'رقم  الهوية'); ?></h4>
                       
                        <div class="bg-white w-285 shapeInput">
                            <h4 id="idNumber" class="text-right"><?php echo htmlspecialchars($certificate['id_number'] ?? ''); ?></h4>
                        </div>
                      </div>
                </div>

                  <div class="flex items-center space-between gap-20 mb-10">
    <div>
      <h4 class="text-right text-details-color shapeLabel">المهنة</h4>
      <div class="bg-white w-285 shapeInput">
        <h4 id="job" class="text-right"><?php  $job = htmlspecialchars($certificate['job'] ?? '');
          echo str_replace(' ', '&nbsp;', $job);?></h4>
      </div>
    </div>
    <div>
      <h4 class="text-right text-details-color shapeLabel">     <?php echo str_replace(' ', '&nbsp;', 'رقم  الشهادة الصحية'); ?></h4>
     
      <div class="bg-white w-285 shapeInput">
        <h4 id="certNumber" class="text-right"><?php echo htmlspecialchars($certificate['cert_number'] ?? ''); ?></h4>
      </div>
    </div>
  </div>


  <!-- الصف 3: تاريخ نهاية الشهادة / تاريخ إصدار الشهادة -->
  <div class="flex items-center space-between gap-20 mb-10">
    <div>
      <h4 class="text-right text-details-color shapeLabel">  <?php echo str_replace(' ', '&nbsp;', ' تاريخ نهاية الشهادة الصحية  '); ?>  </h4>
      
      <div class="bg-white w-285 shapeInput">
        <h4 id="expireDateHijri" class="text-right"><?php echo htmlspecialchars($certificate['expire_date_hijri'] ?? ''); ?></h4>
      </div>
    </div>
    <div>
      <h4 class="text-right text-details-color shapeLabel">   <?php echo str_replace(' ', '&nbsp;', ' تاريخ إصدار الشهادة الصحية   '); ?>  </h4>
      
      
      <div class="bg-white w-285 shapeInput">
        <h4 id="issueDateHijri" class="text-right"><?php echo htmlspecialchars($certificate['issue_date_hijri'] ?? ''); ?></h4>
      </div>
    </div>
  </div>

  <!-- الصف 4: تاريخ انتهاء البرنامج التثقيفي / نوع البرنامج التثقيفي -->
  <div class="flex items-center space-between gap-20">
    <div>
      <h4 class="text-right text-details-color shapeLabel">    <?php echo str_replace(' ', '&nbsp;', '  تاريخ انتهاء البرنامج التثقيفي   '); ?> </h4>
    
      <div class="bg-white w-285 shapeInput">
        <h4 id="programEnd" class="text-right empty-field"><?php echo htmlspecialchars($certificate['program_end'] ?? ''); ?></h4>
      </div>
    </div>
    <div>
      <h4 class="text-right text-details-color shapeLabel">   <?php echo str_replace(' ', '&nbsp;', '  نوع البرنامج التثقيفي   '); ?>   </h4>
      
      <div class="bg-white w-285 shapeInput">
        <h4 id="programType" class="text-right linkish empty-field"><?php echo htmlspecialchars($certificate['program_type'] ?? ''); ?></h4>
      </div>
    </div>
  </div>


            </div>


        </div>
        <div class="contact-row flex items-center mt-30 mb-30">
          <div class="flex items-center gap-10">
              <div class="flex items-center gap-5 br-contact">
                <div class="div-has-iphone w-30"><i class="fa-solid fa-phone"></i></div>
                <div class="flex items-center details-contact gap-5">
                    <h1 class="text-30">199040</h1>
                      <span class="care-span">مركز العناية بالعملاء</span>
                </div>
              </div>
              <div class="flex items-center gap-5">
                  <div class="parent-icon"><i class="fa-brands fa-twitter"></i></div>
                  <span class="text-footer">Balady_cs</span>
              </div>
          </div>


        <div class="socail-icons flex items-center space-between gap-35">
            <div class="flex items-center gap-5">
                <div class="parent-icon"><i class="fa-brands fa-twitter"></i></div>
                <div class="parent-icon"><i class="fa-brands fa-facebook-f"></i></div>
                <div class="parent-icon"><i class="fa-brands fa-youtube"></i></div>

                <span class="text-footer">saudimomra</span>
            </div>

            <div class="flex items-center gap-5 gapMedia">
              <div class="parent-icon"><i class="fa-solid fa-globe"></i></div>
              <span class="text-footer">www.balady.gov.sa</span>
            </div>
          </div>
        </div>

      </div>

    <div id="bottom" class="container bottom text-white">
              <div class="flex items-center gap-10">
                <h1 class="text-55">تعليمات وإرشادات</h1>

                <img src="../imgs/plamTreeWhite-removebg-preview.png" alt="">
                <img src="../imgs/image-removebg-preview.png" alt="">
                <div class="flex items-center">
                  <img id="alltext" src="../imgs/alltext.png" alt="">
                  <img id="botomaskan" src="../imgs/ministryWhite.png" alt="">
                </div>
              </div>

      <div class="mt-40 text-right flex flex-column items-end gap-10 pb-60 content-text">

                <div class="flex items-start gap-10">
                  <h2 class="footer-text">شهادة صحية تجدد سنويًا.</h2>
                  <img src="../imgs/star.png" alt="">
                </div>

                  <div class="flex items-start gap-10">
                    <h2 class="footer-text">يسمح لحامل الشهادة الصحية بالعمل في منشآت الغذاء أو الصحة العامة وفق المهنة المسموح بها نظاماً.</h2>
                    <img src="../imgs/star.png" alt="">
                  </div>

                  <div class="flex items-start gap-10">
                    <h2 class="footer-text">يلزم حامل هذه الشهادة بإجراء فحص طبي عند عودته من الخارج قبل البدء بممارسة العمل.</h2>
                    <img src="../imgs/star.png" alt="">
                  </div>

                    <div class="flex items-start gap-10">
                      <h2 class="footer-text">لا تعتبر الشهادة إثبات هوية.</h2>
                      <img src="../imgs/star.png" alt="">
                    </div>


      </div>
    </div>













<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>
  async function nodeToCanvas(node, scale = 2) {
    return await html2canvas(node, {
      scale: scale,
      useCORS: true,
      backgroundColor: null,
    });
  }

  function rectOnCanvas(el, containerEl, canvas, scale = 2) {
    const elRect = el.getBoundingClientRect();
    const cRect = containerEl.getBoundingClientRect();

    const domX = elRect.left - cRect.left;
    const domY = elRect.top - cRect.top;
    const domW = elRect.width;
    const domH = elRect.height;

    const scaleX = (canvas.width / scale) / cRect.width;
    const scaleY = (canvas.height / scale) / cRect.height;

    return {
      x: domX * scaleX,
      y: domY * scaleY,
      w: domW * scaleX,
      h: domH * scaleY,
    };
  }

  async function downloadPDF() {
    const { jsPDF } = window.jspdf;

    const page1 = document.querySelector(".container.top");
    const page2 = document.querySelector(".container.bottom");

    if (!page1 || !page2) {
      alert("تأكد أن لديك عناصر .container.top و .container.bottom");
      return;
    }

    const personNameEl = document.getElementById("personName");
    const personName = personNameEl ? personNameEl.innerText.trim() : "";
    const filename = personName
      ? personName + "-.pdf"
      : "-.pdf";

    const scale = 2;
    const canvas1 = await nodeToCanvas(page1, scale);
    const canvas2 = await nodeToCanvas(page2, scale);

    const w1 = canvas1.width,
      h1 = canvas1.height;
    const w2 = canvas2.width,
      h2 = canvas2.height;

    const pdf = new jsPDF({
      unit: "px",
      format: [w1 / scale, h1 / scale],
      orientation: w1 > h1 ? "landscape" : "portrait",
      compress: true,
    });

    const img1 = canvas1.toDataURL("image/jpeg", 0.98);
    pdf.addImage(img1, "JPEG", 0, 0, w1 / scale, h1 / scale);

    const qrImg = document.getElementById("qr");
    if (qrImg) {
      const linkEl = qrImg.closest("a");
      const href =
        linkEl && linkEl.href ? linkEl.href : "https://khamsat.com/";
      const { x, y, w, h } = rectOnCanvas(qrImg, page1, canvas1, scale);
      pdf.link(x, y, w, h, { url: href, target: "_blank" });
    }

    pdf.addPage([w2 / scale, h2 / scale], w2 > h2 ? "landscape" : "portrait");
    const img2 = canvas2.toDataURL("image/jpeg", 0.98);
    pdf.addImage(img2, "JPEG", 0, 0, w2 / scale, h2 / scale);

    pdf.save(filename);
  }
</script>











</body>
</html>