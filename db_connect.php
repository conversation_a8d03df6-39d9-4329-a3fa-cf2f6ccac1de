<?php
$host = 'localhost';
$dbname = 'certificates_db';
$username = 'root';
$password = '';

try {
    // إنشاء اتصال PDO
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);

    // تعيين وضع الخطأ
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // تعيين وضع الجلب الافتراضي
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

} catch(PDOException $e) {
    // في حالة فشل الاتصال
    die("فشل الاتصال بقاعدة البيانات: " . $e->getMessage());
}
?>
