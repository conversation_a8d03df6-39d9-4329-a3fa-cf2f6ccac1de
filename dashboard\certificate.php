<?php
require_once '../db_connect.php';
require_once 'auth.php';
require_once '../inc/SecureIdHandler.php';

// الحصول على معرف الشهادة من الرابط
$encrypted_id = isset($_GET['id']) ? $_GET['id'] : '';
$certificate = null;

// جلب بيانات الشهادة من قاعدة البيانات
if (!empty($encrypted_id)) {
    try {
        // فك تشفير الـ ID
        $id = SecureIdHandler::decryptId($encrypted_id);
        if ($id === false) {
            $error = "معرف الشهادة غير صالح";
        } else {
            $stmt = $pdo->prepare("SELECT * FROM certificates WHERE id_number = ?");
            $stmt->execute([$id]);
            $certificate = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // التحقق من وجود البيانات
            if (!$certificate) {
                $error = "لم يتم العثور على الشهادة المطلوبة";
            }
        }
    } catch(PDOException $e) {
        $error = "خطأ في جلب البيانات: " . $e->getMessage();
    }
}

// تحويل الصور إلى base64 للعرض
$photo_base64 = '';
$qr_base64 = '';
$logo_base64 = '';

if ($certificate && !empty($certificate['photo'])) {
    // التحقق من نوع البيانات
    if (is_resource($certificate['photo'])) {
        // إذا كانت البيانات مورد (مثل Blob من قاعدة البيانات)
        $photo_data = stream_get_contents($certificate['photo']);
        $photo_base64 = 'data:image/jpeg;base64,' . base64_encode($photo_data);
    } elseif (strpos($certificate['photo'], 'data:image') === 0) {
        // إذا كانت البيانات بتنسيق data URI بالفعل
        $photo_base64 = $certificate['photo'];
    } elseif (is_string($certificate['photo']) && file_exists($certificate['photo'])) {
        // إذا كانت البيانات مسار ملف
        $photo_base64 = 'data:image/jpeg;base64,' . base64_encode(file_get_contents($certificate['photo']));
    } else {
        // إذا كانت البيانات ثنائية
        try {
            $photo_base64 = 'data:image/jpeg;base64,' . base64_encode($certificate['photo']);
        } catch (Exception $e) {
            // في حالة وجود خطأ في التحويل، استخدام الصورة الافتراضية
            $photo_base64 = '../imgs/person-placeholder.png';
        }
    }
}

if ($certificate && !empty($certificate['qr'])) {
    // التحقق من نوع البيانات
    if (is_resource($certificate['qr'])) {
        // إذا كانت البيانات مورد (مثل Blob من قاعدة البيانات)
        $qr_data = stream_get_contents($certificate['qr']);
        $qr_base64 = 'data:image/png;base64,' . base64_encode($qr_data);
    } elseif (strpos($certificate['qr'], 'data:image') === 0) {
        // إذا كانت البيانات بتنسيق data URI بالفعل
        $qr_base64 = $certificate['qr'];
    } elseif (is_string($certificate['qr']) && file_exists($certificate['qr'])) {
        // إذا كانت البيانات مسار ملف
        $qr_base64 = 'data:image/png;base64,' . base64_encode(file_get_contents($certificate['qr']));
    } else {
        // إذا كانت البيانات ثنائية
        try {
            $qr_base64 = 'data:image/png;base64,' . base64_encode($certificate['qr']);
        } catch (Exception $e) {
            // في حالة وجود خطأ في التحويل، استخدام الصورة الافتراضية
            $qr_base64 = '../imgs/qr-placeholder.png';
        }
    }
}


if ($certificate && !empty($certificate['imgLogo'])) {
    // التحقق من نوع البيانات
    if (is_resource($certificate['imgLogo'])) {
        // إذا كانت البيانات مورد (مثل Blob من قاعدة البيانات)
        $logo_data = stream_get_contents($certificate['imgLogo']);
        $logo_base64 = 'data:image/png;base64,' . base64_encode($logo_data);
    } elseif (strpos($certificate['imgLogo'], 'data:image') === 0) {
        // إذا كانت البيانات بتنسيق data URI بالفعل
        $logo_base64 = $certificate['imgLogo'];
    } elseif (is_string($certificate['imgLogo']) && file_exists($certificate['imgLogo'])) {
        // إذا كانت البيانات مسار ملف
        $logo_base64 = 'data:image/png;base64,' . base64_encode(file_get_contents($certificate['imgLogo']));
    } else {
        // إذا كانت البيانات ثنائية
        try {
            $logo_base64 = 'data:image/png;base64,' . base64_encode($certificate['imgLogo']);
        } catch (Exception $e) {
            // في حالة وجود خطأ في التحويل، استخدام الصورة الافتراضية
            $logo_base64 = '../imgs/qr-placeholder.png';
        }
    }
}




?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الشهادة الصحية</title>
    <link rel="stylesheet" href="main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" rel="stylesheet"/>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100..900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="fonts/notokufiarabic-csrk4ydqnpyadxexlff6lzvlkrodri0fflkp.woff2">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100..900&display=swap" rel="stylesheet">

<style>
/*   header .div-tilte {*/
/*    background-color: #0e7270;*/
/*    padding: 9px 61px;*/
/*}*/


@font-face {
  font-family: 'extralight';
  src: url('fonts/cairo-extralight.ttf') format('truetype');
  font-weight: normal;

}

@font-face {
  font-family: 'NotoKufiArabic';
  src: url('fonts/NotoKufiArabic-Regular.woff2') format('woff2'),
       url('fonts/NotoKufiArabic-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap; /* يضمن ظهور النص حتى لو لم يتم تحميل الخط بعد */
}
@font-face {
  font-family: 'Ishraq-Light';
  src: url('fonts/alexandria-bold.ttf') format('truetype'); /* تم تعديل هذه القيمة */
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'stv';
  src: url('fonts/stv.ttf') format('truetype'); /* تم تعديل هذه القيمة */
  font-weight: 500;
  font-style: normal;
}


@font-face {
  font-family: 'tanseekAnatherpag';
  src: url('fonts/tanseekarabic.ttf') format('truetype'); /* تم تعديل هذه القيمة */
  font-weight: 500;
  font-style: normal;
}


/* @font-face {
  font-family: 'Ishraq-Light';
  src: url('alfont_com_Somar-ExtraBoldItalic.otf') format('opentype'); 
  font-weight: 500;
  font-style: normal;
}

.header4 {
  width: 400px;
  height: 100px;
  background-color: #417d7f;
}

.header4 h2 {
  font-family: 'Ishraq-Light', sans-serif;
  color: #fff;
  font-size: xx-large;
  transform: scaleY(1.5); 
  transform-origin: center; 
} */


  /*
  alfont_com_Israr-Syria.otf
  alexandria-black.ttf
  alexandria-bold.ttf
  src: url('helvetica-world.ttf') format('truetype'); تم التغيير هنا */



@font-face {
  font-family: 'Ishraq-Light';
  src: url('alexandria-bold.ttf') format('truetype'); 
  font-weight: 500;
  font-style: normal;
}


/*body {*/
/*   font-family: 'Ishraq-Light';*/
/*  font-size: 0.875rem;*/
/*    line-height: 1.5;*/
   
/*}*/



@font-face {
  font-family: 'myTitle';
  src: url('fonts/tanseek-modern-pro-arabic-medium.ttf') format('truetype');
   font-weight: 100;
 }














header .div-tilte {
 background-color: #0e7270;
    padding: 0px 23px;
    font-size: xx-large;
    text-transform: uppercase;
    transform: scaleY(1.0);
    margin-bottom: 11px;
}
.div-tilte h1 {
  

 text-align: center;
    margin-top: -5px;
    /* font-size: 58px; */
    font-weight: 300;
    /* margin-top: -9px; */
    margin-bottom: -8px;
    font-family: 'myTitle' !important;
    /* transform: scaleY(1.2); */
    font-size: 60px;
    /* font-weight: 300; */
    margin-bottom: 7px;
    margin-left: 6px;
    font-variation-settings: "wght" 100;
}

/*#job{*/
/*    padding:9px;*/
/*}*/

.left-photos div {
    height: 176px;
}

header {
    gap: 100px;
    margin-bottom: -14px;
}

.name {
    font-size: 27px !important;
    font-weight: 600;
    font-family: 'extralight', sans-serif;
    color: #0e7270;
    padding-top: 9px;
}

.w-full {
    width: 100%;
    padding: 3px;
}


.h-full {
    height: 100%;
    padding: 7px;
}




.w-285 {
    width: 291px;
}

.border-green {
    position: relative;
    display: inline-block;
     width: 170px; 
    height: 146px;
}


#photo
{
    padding: 3px;
}

/*rgba(0, 0, 0, 0.1);*/

/*header .div-tilte {*/
    /*background-color: #2e498e;*/
/*     background-color: rgba(65, 85, 135);*/
/*    padding: 13px 23px;*/
/*    font-size: xx-large;*/
/*    text-transform: uppercase;*/
/*    transform: scaleY(1.0);*/
/*    margin-bottom: 11px;*/
/*}*/

/*.container.bottom {*/
/*    background-color: #2e498e;*/
/*    padding: 5px 18px;*/
/*}*/

/*#job*/
/*{*/
/*    padding: 11px;*/
/*}*/










.left-photos div {
    height: 171px;
}





</style>

</head>
  <body>

    <!--<div class="btnconfar">-->
    <!--    <a id="btnconfarfBtn" class="download-btn" type="button"> انتبه   شعار الشهاد </a>-->
    <!--  </div>-->

   <div class="btn-div">
        <a id="downloadPdfBtn" class="download-btn"   onclick="downloadPDF()"  type="button">Download PDF</a>
      </div>
    <div id="content-to-export" class="app">

      <div class="container top">
                <div class="bg-nakhla"></div>

                 <div class="background-shape">
                  <img src="../imgs/backgroundContinerAllC.png" alt="" srcset="">
                  <img id="nakhla" src="../imgs/nakhla.png" alt="" srcset="">
                 </div>

        <header class="flex items-center space-between">
            <!--<div class="div-tilte text-center"><h1 class="text-white">الشهادة الصحية  </h1></div>-->
            
            
    <div class="div-tilte text-center"><h1 class="text-white">الشهادة الصحية &zwj; الموحدة</h1></div>



            <div class="flex items-center space-between mr-12 w-245">
              <div class="div-img">

            <!-- الحالة: صورة Base64 -->
<img id="logoall" class="max-w-full" src="<?php echo $certificate['imgLogo'] ?? ''; ?>" alt="شعار">     
            <!-- الحالة: مسار صورة -->
      
 
</div>

              <div class="div-img middel-imgs"><img class="max-w-full" src="../imgs/balady-removebg-preview.png" alt=""></div>
              <div class="div-img"><img class="max-w-full" src="../imgs/AISCAN3.png" alt=""></div>
            </div>

        </header>

        <div class="details flex items-center space-between">
            <div class="left-photos flex flex-column gap-15">
                <div class="border-green p-4 text-center">
                    <?php if (!empty($photo_base64)): ?>
                        <?php if (strpos($photo_base64, 'data:image') === 0): ?>
                            <img id="photo" class="max-w-full max-h-full w-full h-full" src="<?php echo htmlspecialchars($photo_base64); ?>" alt="صورة شخصية">
                        <?php else: ?>
                            <img id="photo" class="max-w-full max-h-full w-full h-full" src="<?php echo htmlspecialchars($photo_base64); ?>" alt="صورة شخصية" onerror="this.src='../imgs/person-placeholder.png'">
                        <?php endif; ?>
                    <?php else: ?>
                        <img id="photo" class="max-w-full max-h-full w-full h-full" src="../imgs/person-placeholder.png" alt="صورة افتراضية">
                    <?php endif; ?>
                </div>

                <div class="border-green p-4 text-center">
                    <a href="<?php echo !empty($certificate['linkWeb']) ? htmlspecialchars($certificate['linkWeb']) : ''; ?>" target="_blank" class="qr-link">
                        <?php if (!empty($qr_base64)): ?>
                            <?php if (strpos($qr_base64, 'data:image') === 0): ?>
                                <img id="qr" class="max-w-full max-h-full h-full" src="<?php echo htmlspecialchars($qr_base64); ?>" alt="QR Code">
                            <?php else: ?>
                                <img id="qr" class="max-w-full max-h-full h-full" src="<?php echo htmlspecialchars($qr_base64); ?>" alt="QR Code" onerror="this.src='../imgs/qr-placeholder.png'">
                            <?php endif; ?>
                        <?php else: ?>
                            <img id="qr" class="max-w-full max-h-full h-full" src="../imgs/qr-placeholder.png" alt="رمز QR افتراضي">
                        <?php endif; ?>
                    </a>
                </div>
            </div>

            <div>
                
                
<div>             

<div id="personName" class="name text-right mb-10">
    <?php
        $person_name = htmlspecialchars($certificate['person_name'] ?? '');
        // استبدال المسافات العادية بمسافات غير قابلة للكسر
        echo str_replace(' ', '&nbsp;', $person_name);
    ?>
</div>


</div>
                <div class="flex items-center space-between gap-20 mb-10">
                    <div>
                        <h4 class="text-right text-details-color shapeLabel">الجنسية</h4>
                        <div class="bg-white w-285 shapeInput">
                            <h4 id="nationality" class="text-right"><?php echo htmlspecialchars($certificate['nationality'] ?? ''); ?></h4>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-right text-details-color shapeLabel">رقم الهوية</h4>
                        <div class="bg-white w-285 shapeInput">
                            <h4 id="idNumber" class="text-right"><?php echo htmlspecialchars($certificate['id_number'] ?? ''); ?></h4>
                        </div>
                      </div>
                </div>

                  <div class="flex items-center space-between gap-20 mb-10">
    <div>
      <h4 class="text-right text-details-color shapeLabel">المهنة</h4>
      <div class="bg-white w-285 shapeInput">
        <h4 id="job" class="text-right"><?php echo htmlspecialchars($certificate['job'] ?? ''); ?></h4>
      </div>
    </div>
    <div>
      <h4 class="text-right text-details-color shapeLabel">رقم الشهادة الصحية</h4>
      <div class="bg-white w-285 shapeInput">
        <h4 id="certNumber" class="text-right"><?php echo htmlspecialchars($certificate['cert_number'] ?? ''); ?></h4>
      </div>
    </div>
  </div>


  <!-- الصف 3: تاريخ نهاية الشهادة / تاريخ إصدار الشهادة -->
  <div class="flex items-center space-between gap-20 mb-10">
    <div>
      <h4 class="text-right text-details-color shapeLabel">تاريخ نهاية الشهادة الصحية</h4>
      <div class="bg-white w-285 shapeInput">
        <h4 id="expireDateHijri" class="text-right"><?php echo htmlspecialchars($certificate['expire_date_hijri'] ?? ''); ?></h4>
      </div>
    </div>
    <div>
      <h4 class="text-right text-details-color shapeLabel">تاريخ إصدار الشهادة الصحية</h4>
      <div class="bg-white w-285 shapeInput">
        <h4 id="issueDateHijri" class="text-right"><?php echo htmlspecialchars($certificate['issue_date_hijri'] ?? ''); ?></h4>
      </div>
    </div>
  </div>

  <!-- الصف 4: تاريخ انتهاء البرنامج التثقيفي / نوع البرنامج التثقيفي -->
  <div class="flex items-center space-between gap-20">
    <div>
      <h4 class="text-right text-details-color shapeLabel">تاريخ انتهاء البرنامج التثقيفي</h4>
      <div class="bg-white w-285 shapeInput">
        <h4 id="programEnd" class="text-right empty-field"><?php echo htmlspecialchars($certificate['program_end'] ?? ''); ?></h4>
      </div>
    </div>
    <div>
      <h4 class="text-right text-details-color shapeLabel">نوع البرنامج التثقيفي</h4>
      <div class="bg-white w-285 shapeInput">
        <h4 id="programType" class="text-right linkish empty-field"><?php echo htmlspecialchars($certificate['program_type'] ?? ''); ?></h4>
      </div>
    </div>
  </div>


            </div>


        </div>
        <div class="contact-row flex items-center mt-30 mb-30">
          <div class="flex items-center gap-10">
              <div class="flex items-center gap-5 br-contact">
                <div class="div-has-iphone w-30"><i class="fa-solid fa-phone"></i></div>
                <div class="flex items-center details-contact gap-5">
                    <h1 class="text-30">199040</h1>
                      <span class="care-span">مركز العناية بالعملاء</span>
                </div>
              </div>
              <div class="flex items-center gap-5">
                  <div class="parent-icon"><i class="fa-brands fa-twitter"></i></div>
                  <span class="text-footer">Balady_cs</span>
              </div>
          </div>


        <div class="socail-icons flex items-center space-between gap-35">
            <div class="flex items-center gap-5">
                <div class="parent-icon"><i class="fa-brands fa-twitter"></i></div>
                <div class="parent-icon"><i class="fa-brands fa-facebook-f"></i></div>
                <div class="parent-icon"><i class="fa-brands fa-youtube"></i></div>

                <span class="text-footer">saudimomra</span>
            </div>

            <div class="flex items-center gap-5 gapMedia">
              <div class="parent-icon"><i class="fa-solid fa-globe"></i></div>
              <span class="text-footer">www.balady.gov.sa</span>
            </div>
          </div>
        </div>

      </div>

    <div class="container bottom text-white">
      <div class="flex items-center gap-10">
        <h1 class="text-55">تعليمات وإرشادات</h1>

        <img src="../imgs/plamTreeWhite-removebg-preview.png" alt="">
        <img src="../imgs/image-removebg-preview.png" alt="">
        <div class="flex items-center">
          <img src="../imgs/TextMinistry.png" alt="">
          <img src="../imgs/ministryWhite.png" alt="">
        </div>
      </div>

      <div class="mt-40 text-right flex flex-column items-end gap-10 pb-60 content-text">

        <div class="flex items-start gap-10">
          <h2 class="footer-text">شهادة صحية تجدد سنويًا.</h2>
          <img src="../imgs/star.png" alt="">
        </div>

        <div class="flex items-start gap-10">
          <h2 class="footer-text">يسمح لحامل الشهادة الصحية بالعمل في منشآت الغذاء أو الصحة العامة وفق المهنة المسموح بها نظاماً.</h2>
          <img src="../imgs/star.png" alt="">
        </div>

        <div class="flex items-start gap-10">
          <h2 class="footer-text">يلزم حامل هذه الشهادة بإجراء فحص طبي عند عودته من الخارج قبل البدء بممارسة العمل.</h2>
          <img src="../imgs/star.png" alt="">
        </div>

        <div class="flex items-start gap-10">
          <h2 class="footer-text">لا تعتبر الشهادة إثبات هوية.</h2>
          <img src="../imgs/star.png" alt="">
        </div>


      </div>
    </div>



   

  </div>
  
  
  
   <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script>
      async function nodeToCanvas(node) {
        return await html2canvas(node, {
          scale: 2,
          useCORS: true,
          backgroundColor: null,
          windowWidth: document.documentElement.clientWidth,
        });
      }

      function rectOnCanvas(el, containerEl, canvas) {
        const elRect = el.getBoundingClientRect();
        const cRect = containerEl.getBoundingClientRect();

        const domX = elRect.left - cRect.left;
        const domY = elRect.top - cRect.top;
        const domW = elRect.width;
        const domH = elRect.height;

        const scaleX = canvas.width / cRect.width;
        const scaleY = canvas.height / cRect.height;

        return {
          x: domX * scaleX,
          y: domY * scaleY,
          w: domW * scaleX,
          h: domH * scaleY,
        };
      }

      async function downloadPDF() {
        const { jsPDF } = window.jspdf;

        const page1 = document.querySelector(".container.top");
        const page2 = document.querySelector(".container.bottom");

        if (!page1 || !page2) {
          alert("تأكد أن لديك عناصر .container.top و .container.bottom");
          return;
        }

        const personNameEl = document.getElementById("personName");
        const personName = personNameEl ? personNameEl.innerText.trim() : "";
        const filename = personName
          ? personName + "-.pdf"
          : "-.pdf";

        const canvas1 = await nodeToCanvas(page1);
        const canvas2 = await nodeToCanvas(page2);

        const w1 = canvas1.width,
          h1 = canvas1.height;
        const w2 = canvas2.width,
          h2 = canvas2.height;

        const pdf = new jsPDF({
          unit: "px",
          format: [w1, h1],
          orientation: w1 > h1 ? "landscape" : "portrait",
          compress: true,
        });

        const img1 = canvas1.toDataURL("image/jpeg", 0.98);
        pdf.addImage(img1, "JPEG", 0, 0, w1, h1);

        const qrImg = document.getElementById("qr");
        if (qrImg) {
          const linkEl = qrImg.closest("a");
          const href =
            linkEl && linkEl.href ? linkEl.href : "https://khamsat.com/";
          const { x, y, w, h } = rectOnCanvas(qrImg, page1, canvas1);
          pdf.link(x, y, w, h, { url: href, target: "_blank" });
        }

        pdf.addPage([w2, h2], w2 > h2 ? "landscape" : "portrait");
        const img2 = canvas2.toDataURL("image/jpeg", 0.98);
        pdf.addImage(img2, "JPEG", 0, 0, w2, h2);

        pdf.save(filename);
      }
    </script>
  
  
  
  
  
  
  
  
  
  
  
  
   
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>-->
<!--<script>-->

<!--document.addEventListener('DOMContentLoaded', function() {-->
    // العثور على زر التحميل
<!--  const downloadBtn = document.getElementById('downloadPdfBtn');-->

<!-- downloadBtn.addEventListener('click', function() {-->
        // إخفاء الزر مؤقتًا أثناء عملية التحميل
<!--      this.style.display = 'none';-->

        // الحصول على اسم الشخص لتسمية الملف
<!--        const personName = document.getElementById('personName').innerText.trim();-->
        
        // الحصول على المحتوى المراد تصديره
<!--       const element = document.getElementById('content-to-export');-->

        // إعداد خيارات المكتبة
<!--       const opt = {-->
<!--            margin: 1,-->
<!--           filename: (personName ? personName + '-شهادة-صحية.pdf' : 'شهادة-صحية.pdf'),-->
<!--           image: { type: 'jpeg', quality: 0.98 },-->
<!--           jsPDF: {-->
<!--               unit: 'px',-->
<!--               format: [818, 1156],-->
<!--               orientation: 'portrait'-->
<!--           }-->
<!--        };-->

        // بدء عملية التحويل والحفظ
<!--        html2pdf().set(opt).from(element).save().finally(() => {-->
            // إعادة إظهار الزر بعد انتهاء العملية
<!--         this.style.display = 'block';-->
<!--     });-->
<!--   });-->
<!--  });-->
<!--</script>-->















    <!--<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>-->
    <!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>-->

    <!--<script>-->
    <!--  async function nodeToCanvas(node) {-->
    <!--    return await html2canvas(node, {-->
    <!--      scale: 2,-->
    <!--      useCORS: true,-->
    <!--      backgroundColor: null,-->
    <!--      windowWidth: document.documentElement.clientWidth,-->
    <!--    });-->
    <!--  }-->

    <!--  async function downloadPDF() {-->
    <!--    const { jsPDF } = window.jspdf;-->

    <!--    const page1 = document.querySelector(".container.top");-->
    <!--    const page2 = document.querySelector(".container.bottom");-->

    <!--    if (!page1 || !page2) {-->
    <!--      alert("تأكد أن لديك عناصر .container.top و .container.bottom");-->
    <!--      return;-->
    <!--    }-->

        // الاسم من الصفحة
    <!--    const personNameEl = document.getElementById("personName");-->
    <!--    const personName = personNameEl ? personNameEl.innerText.trim() : "";-->
    <!--    const filename = personName-->
    <!--      ? personName + "-شهادة-صحية.pdf"-->
    <!--      : "شهادة-صحية.pdf";-->

        // 1) حوِّل كل صفحة إلى Canvas
    <!--    const canvas1 = await nodeToCanvas(page1);-->
    <!--    const canvas2 = await nodeToCanvas(page2);-->

        // أبعاد كل صفحة بالبكسل (كما هي على الشاشة)
    <!--    const w1 = canvas1.width;-->
    <!--    const h1 = canvas1.height;-->
    <!--    const w2 = canvas2.width;-->
    <!--    const h2 = canvas2.height;-->

        // 2) أنشئ PDF بمقاس الصفحة الأولى (بوحدة px علشان نحط الصورة 1:1)
    <!--    const pdf = new jsPDF({-->
    <!--      unit: "px",-->
    <!--      format: [w1, h1],-->
    <!--      orientation: w1 > h1 ? "landscape" : "portrait",-->
    <!--      compress: true,-->
    <!--    });-->

        // 3) أضف الصورة الأولى بملء الصفحة
    <!--    const img1 = canvas1.toDataURL("image/jpeg", 0.98);-->
    <!--    pdf.addImage(img1, "JPEG", 0, 0, w1, h1);-->

        // 4) أضف صفحة ثانية بمقاس الصفحة الثانية
    <!--    pdf.addPage([w2, h2], w2 > h2 ? "landscape" : "portrait");-->
    <!--    const img2 = canvas2.toDataURL("image/jpeg", 0.98);-->
    <!--    pdf.addImage(img2, "JPEG", 0, 0, w2, h2);-->

        // 5) حفظ
    <!--    pdf.save(filename);-->
    <!--  }-->
    <!--</script>-->






















<script>
  document.addEventListener("DOMContentLoaded", function () {
    const programEnd = document.getElementById("programEnd");
    const programType = document.getElementById("programType");

    // دالة تساعدنا نتأكد من كون العنصر فارغ أو لا
    function isEmpty(el) {
      return !el.textContent.trim();
    }

    // تحقق وأضف/لا تضف الكلاس
    if (isEmpty(programEnd)) {
      programEnd.classList.add("empty-field");
    } else {
      programEnd.classList.remove("empty-field");
    }

    if (isEmpty(programType)) {
      programType.classList.add("empty-field");
    } else {
      programType.classList.remove("empty-field");
    }
  });
</script>

</body>
</html>