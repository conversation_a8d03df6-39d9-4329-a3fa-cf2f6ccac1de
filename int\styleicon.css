
    .fa,
    .fas,
    .far,
    .fal,
    .fad,
    .fab {
      -moz-osx-font-smoothing: grayscale;
      -webkit-font-smoothing: antialiased;
      display: inline-block;
      font-style: normal;
      font-variant: normal;
      text-rendering: auto;
      line-height: 1;
    }

    .fa-lg {
      font-size: 1.33333em;
      line-height: 0.75em;
      vertical-align: -.0667em;
    }

    .fa-xs {
      font-size: .75em;
    }

    .fa-sm {
      font-size: .875em;
    }

    .fa-1x {
      font-size: 1em;
    }

    .fa-2x {
      font-size: 2em;
    }

    .fa-3x {
      font-size: 3em;
    }

    .fa-4x {
      font-size: 4em;
    }

    .fa-5x {
      font-size: 5em;
    }

    .fa-6x {
      font-size: 6em;
    }

    .fa-7x {
      font-size: 7em;
    }

    .fa-8x {
      font-size: 8em;
    }

    .fa-9x {
      font-size: 9em;
    }

    .fa-10x {
      font-size: 10em;
    }

    .fa-fw {
      text-align: center;
      width: 1.25em;
    }

    .fa-ul {
      list-style-type: none;
      margin-left: 2.5em;
      padding-left: 0;
    }

    .fa-ul>li {
      position: relative;
    }

    .fa-li {
      left: -2em;
      position: absolute;
      text-align: center;
      width: 2em;
      line-height: inherit;
    }

    .fa-border {
      border: solid 0.08em #eee;
      border-radius: .1em;
      padding: .2em .25em .15em;
    }

    .fa-pull-left {
      float: left;
    }

    .fa-pull-right {
      float: right;
    }

    .fa.fa-pull-left,
    .fas.fa-pull-left,
    .far.fa-pull-left,
    .fal.fa-pull-left,
    .fab.fa-pull-left {
      margin-right: .3em;
    }

    .fa.fa-pull-right,
    .fas.fa-pull-right,
    .far.fa-pull-right,
    .fal.fa-pull-right,
    .fab.fa-pull-right {
      margin-left: .3em;
    }

    .fa-spin {
      -webkit-animation: fa-spin 2s infinite linear;
      animation: fa-spin 2s infinite linear;
    }

    .fa-pulse {
      -webkit-animation: fa-spin 1s infinite steps(8);
      animation: fa-spin 1s infinite steps(8);
    }

    @-webkit-keyframes fa-spin {
      0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes fa-spin {
      0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    .fa-rotate-90 {
      -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
      -webkit-transform: rotate(90deg);
      transform: rotate(90deg);
    }

    .fa-rotate-180 {
      -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
      -webkit-transform: rotate(180deg);
      transform: rotate(180deg);
    }

    .fa-rotate-270 {
      -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
      -webkit-transform: rotate(270deg);
      transform: rotate(270deg);
    }

    .fa-flip-horizontal {
      -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
      -webkit-transform: scale(-1, 1);
      transform: scale(-1, 1);
    }

    .fa-flip-vertical {
      -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
      -webkit-transform: scale(1, -1);
      transform: scale(1, -1);
    }

    .fa-flip-both,
    .fa-flip-horizontal.fa-flip-vertical {
      -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
      -webkit-transform: scale(-1, -1);
      transform: scale(-1, -1);
    }

    :root .fa-rotate-90,
    :root .fa-rotate-180,
    :root .fa-rotate-270,
    :root .fa-flip-horizontal,
    :root .fa-flip-vertical,
    :root .fa-flip-both {
      -webkit-filter: none;
      filter: none;
    }

    .fa-stack {
      display: inline-block;
      height: 2em;
      line-height: 2em;
      position: relative;
      vertical-align: middle;
      width: 2.5em;
    }

    .fa-stack-1x,
    .fa-stack-2x {
      left: 0;
      position: absolute;
      text-align: center;
      width: 100%;
    }

    .fa-stack-1x {
      line-height: inherit;
    }

    .fa-stack-2x {
      font-size: 2em;
    }

    .fa-inverse {
      color: #fff;
    }

    /* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
readers do not read off random characters that represent icons */
    .fa-500px:before {
      content: "\f26e";
    }

    .fa-accessible-icon:before {
      content: "\f368";
    }

    .fa-accusoft:before {
      content: "\f369";
    }

    .fa-acquisitions-incorporated:before {
      content: "\f6af";
    }

    .fa-ad:before {
      content: "\f641";
    }

    .fa-address-book:before {
      content: "\f2b9";
    }

    .fa-address-card:before {
      content: "\f2bb";
    }

    .fa-adjust:before {
      content: "\f042";
    }

    .fa-adn:before {
      content: "\f170";
    }

    .fa-adobe:before {
      content: "\f778";
    }

    .fa-adversal:before {
      content: "\f36a";
    }

    .fa-affiliatetheme:before {
      content: "\f36b";
    }

    .fa-air-freshener:before {
      content: "\f5d0";
    }

    .fa-airbnb:before {
      content: "\f834";
    }

    .fa-algolia:before {
      content: "\f36c";
    }

    .fa-align-center:before {
      content: "\f037";
    }

    .fa-align-justify:before {
      content: "\f039";
    }

    .fa-align-left:before {
      content: "\f036";
    }

    .fa-align-right:before {
      content: "\f038";
    }

    .fa-alipay:before {
      content: "\f642";
    }

    .fa-allergies:before {
      content: "\f461";
    }

    .fa-amazon:before {
      content: "\f270";
    }

    .fa-amazon-pay:before {
      content: "\f42c";
    }

    .fa-ambulance:before {
      content: "\f0f9";
    }

    .fa-american-sign-language-interpreting:before {
      content: "\f2a3";
    }

    .fa-amilia:before {
      content: "\f36d";
    }

    .fa-anchor:before {
      content: "\f13d";
    }

    .fa-android:before {
      content: "\f17b";
    }

    .fa-angellist:before {
      content: "\f209";
    }

    .fa-angle-double-down:before {
      content: "\f103";
    }

    .fa-angle-double-left:before {
      content: "\f100";
    }

    .fa-angle-double-right:before {
      content: "\f101";
    }

    .fa-angle-double-up:before {
      content: "\f102";
    }

    .fa-angle-down:before {
      content: "\f107";
    }

    .fa-angle-left:before {
      content: "\f104";
    }

    .fa-angle-right:before {
      content: "\f105";
    }

    .fa-angle-up:before {
      content: "\f106";
    }

    .fa-angry:before {
      content: "\f556";
    }

    .fa-angrycreative:before {
      content: "\f36e";
    }

    .fa-angular:before {
      content: "\f420";
    }

    .fa-ankh:before {
      content: "\f644";
    }

    .fa-app-store:before {
      content: "\f36f";
    }

    .fa-app-store-ios:before {
      content: "\f370";
    }

    .fa-apper:before {
      content: "\f371";
    }

    .fa-apple:before {
      content: "\f179";
    }

    .fa-apple-alt:before {
      content: "\f5d1";
    }

    .fa-apple-pay:before {
      content: "\f415";
    }

    .fa-archive:before {
      content: "\f187";
    }

    .fa-archway:before {
      content: "\f557";
    }

    .fa-arrow-alt-circle-down:before {
      content: "\f358";
    }

    .fa-arrow-alt-circle-left:before {
      content: "\f359";
    }

    .fa-arrow-alt-circle-right:before {
      content: "\f35a";
    }

    .fa-arrow-alt-circle-up:before {
      content: "\f35b";
    }

    .fa-arrow-circle-down:before {
      content: "\f0ab";
    }

    .fa-arrow-circle-left:before {
      content: "\f0a8";
    }

    .fa-arrow-circle-right:before {
      content: "\f0a9";
    }

    .fa-arrow-circle-up:before {
      content: "\f0aa";
    }

    .fa-arrow-down:before {
      content: "\f063";
    }

    .fa-arrow-left:before {
      content: "\f060";
    }

    .fa-arrow-right:before {
      content: "\f061";
    }

    .fa-arrow-up:before {
      content: "\f062";
    }

    .fa-arrows-alt:before {
      content: "\f0b2";
    }

    .fa-arrows-alt-h:before {
      content: "\f337";
    }

    .fa-arrows-alt-v:before {
      content: "\f338";
    }

    .fa-artstation:before {
      content: "\f77a";
    }

    .fa-assistive-listening-systems:before {
      content: "\f2a2";
    }

    .fa-asterisk:before {
      content: "\f069";
    }

    .fa-asymmetrik:before {
      content: "\f372";
    }

    .fa-at:before {
      content: "\f1fa";
    }

    .fa-atlas:before {
      content: "\f558";
    }

    .fa-atlassian:before {
      content: "\f77b";
    }

    .fa-atom:before {
      content: "\f5d2";
    }

    .fa-audible:before {
      content: "\f373";
    }

    .fa-audio-description:before {
      content: "\f29e";
    }

    .fa-autoprefixer:before {
      content: "\f41c";
    }

    .fa-avianex:before {
      content: "\f374";
    }

    .fa-aviato:before {
      content: "\f421";
    }

    .fa-award:before {
      content: "\f559";
    }

    .fa-aws:before {
      content: "\f375";
    }

    .fa-baby:before {
      content: "\f77c";
    }

    .fa-baby-carriage:before {
      content: "\f77d";
    }

    .fa-backspace:before {
      content: "\f55a";
    }

    .fa-backward:before {
      content: "\f04a";
    }

    .fa-bacon:before {
      content: "\f7e5";
    }

    .fa-balance-scale:before {
      content: "\f24e";
    }

    .fa-balance-scale-left:before {
      content: "\f515";
    }

    .fa-balance-scale-right:before {
      content: "\f516";
    }

    .fa-ban:before {
      content: "\f05e";
    }

    .fa-band-aid:before {
      content: "\f462";
    }

    .fa-bandcamp:before {
      content: "\f2d5";
    }

    .fa-barcode:before {
      content: "\f02a";
    }

    .fa-bars:before {
      content: "\f0c9";
    }

    .fa-baseball-ball:before {
      content: "\f433";
    }

    .fa-basketball-ball:before {
      content: "\f434";
    }

    .fa-bath:before {
      content: "\f2cd";
    }

    .fa-battery-empty:before {
      content: "\f244";
    }

    .fa-battery-full:before {
      content: "\f240";
    }

    .fa-battery-half:before {
      content: "\f242";
    }

    .fa-battery-quarter:before {
      content: "\f243";
    }

    .fa-battery-three-quarters:before {
      content: "\f241";
    }

    .fa-battle-net:before {
      content: "\f835";
    }

    .fa-bed:before {
      content: "\f236";
    }

    .fa-beer:before {
      content: "\f0fc";
    }

    .fa-behance:before {
      content: "\f1b4";
    }

    .fa-behance-square:before {
      content: "\f1b5";
    }

    .fa-bell:before {
      content: "\f0f3";
    }

    .fa-bell-slash:before {
      content: "\f1f6";
    }

    .fa-bezier-curve:before {
      content: "\f55b";
    }

    .fa-bible:before {
      content: "\f647";
    }

    .fa-bicycle:before {
      content: "\f206";
    }

    .fa-biking:before {
      content: "\f84a";
    }

    .fa-bimobject:before {
      content: "\f378";
    }

    .fa-binoculars:before {
      content: "\f1e5";
    }

    .fa-biohazard:before {
      content: "\f780";
    }

    .fa-birthday-cake:before {
      content: "\f1fd";
    }

    .fa-bitbucket:before {
      content: "\f171";
    }

    .fa-bitcoin:before {
      content: "\f379";
    }

    .fa-bity:before {
      content: "\f37a";
    }

    .fa-black-tie:before {
      content: "\f27e";
    }

    .fa-blackberry:before {
      content: "\f37b";
    }

    .fa-blender:before {
      content: "\f517";
    }

    .fa-blender-phone:before {
      content: "\f6b6";
    }

    .fa-blind:before {
      content: "\f29d";
    }

    .fa-blog:before {
      content: "\f781";
    }

    .fa-blogger:before {
      content: "\f37c";
    }

    .fa-blogger-b:before {
      content: "\f37d";
    }

    .fa-bluetooth:before {
      content: "\f293";
    }

    .fa-bluetooth-b:before {
      content: "\f294";
    }

    .fa-bold:before {
      content: "\f032";
    }

    .fa-bolt:before {
      content: "\f0e7";
    }

    .fa-bomb:before {
      content: "\f1e2";
    }

    .fa-bone:before {
      content: "\f5d7";
    }

    .fa-bong:before {
      content: "\f55c";
    }

    .fa-book:before {
      content: "\f02d";
    }

    .fa-book-dead:before {
      content: "\f6b7";
    }

    .fa-book-medical:before {
      content: "\f7e6";
    }

    .fa-book-open:before {
      content: "\f518";
    }

    .fa-book-reader:before {
      content: "\f5da";
    }

    .fa-bookmark:before {
      content: "\f02e";
    }

    .fa-bootstrap:before {
      content: "\f836";
    }

    .fa-border-all:before {
      content: "\f84c";
    }

    .fa-border-none:before {
      content: "\f850";
    }

    .fa-border-style:before {
      content: "\f853";
    }

    .fa-bowling-ball:before {
      content: "\f436";
    }

    .fa-box:before {
      content: "\f466";
    }

    .fa-box-open:before {
      content: "\f49e";
    }

    .fa-boxes:before {
      content: "\f468";
    }

    .fa-braille:before {
      content: "\f2a1";
    }

    .fa-brain:before {
      content: "\f5dc";
    }

    .fa-bread-slice:before {
      content: "\f7ec";
    }

    .fa-briefcase:before {
      content: "\f0b1";
    }

    .fa-briefcase-medical:before {
      content: "\f469";
    }

    .fa-broadcast-tower:before {
      content: "\f519";
    }

    .fa-broom:before {
      content: "\f51a";
    }

    .fa-brush:before {
      content: "\f55d";
    }

    .fa-btc:before {
      content: "\f15a";
    }

    .fa-buffer:before {
      content: "\f837";
    }

    .fa-bug:before {
      content: "\f188";
    }

    .fa-building:before {
      content: "\f1ad";
    }

    .fa-bullhorn:before {
      content: "\f0a1";
    }

    .fa-bullseye:before {
      content: "\f140";
    }

    .fa-burn:before {
      content: "\f46a";
    }

    .fa-buromobelexperte:before {
      content: "\f37f";
    }

    .fa-bus:before {
      content: "\f207";
    }

    .fa-bus-alt:before {
      content: "\f55e";
    }

    .fa-business-time:before {
      content: "\f64a";
    }

    .fa-buysellads:before {
      content: "\f20d";
    }

    .fa-calculator:before {
      content: "\f1ec";
    }

    .fa-calendar:before {
      content: "\f133";
    }

    .fa-calendar-alt:before {
      content: "\f073";
    }

    .fa-calendar-check:before {
      content: "\f274";
    }

    .fa-calendar-day:before {
      content: "\f783";
    }

    .fa-calendar-minus:before {
      content: "\f272";
    }

    .fa-calendar-plus:before {
      content: "\f271";
    }

    .fa-calendar-times:before {
      content: "\f273";
    }

    .fa-calendar-week:before {
      content: "\f784";
    }

    .fa-camera:before {
      content: "\f030";
    }

    .fa-camera-retro:before {
      content: "\f083";
    }

    .fa-campground:before {
      content: "\f6bb";
    }

    .fa-canadian-maple-leaf:before {
      content: "\f785";
    }

    .fa-candy-cane:before {
      content: "\f786";
    }

    .fa-cannabis:before {
      content: "\f55f";
    }

    .fa-capsules:before {
      content: "\f46b";
    }

    .fa-car:before {
      content: "\f1b9";
    }

    .fa-car-alt:before {
      content: "\f5de";
    }

    .fa-car-battery:before {
      content: "\f5df";
    }

    .fa-car-crash:before {
      content: "\f5e1";
    }

    .fa-car-side:before {
      content: "\f5e4";
    }

    .fa-caret-down:before {
      content: "\f0d7";
    }

    .fa-caret-left:before {
      content: "\f0d9";
    }

    .fa-caret-right:before {
      content: "\f0da";
    }

    .fa-caret-square-down:before {
      content: "\f150";
    }

    .fa-caret-square-left:before {
      content: "\f191";
    }

    .fa-caret-square-right:before {
      content: "\f152";
    }

    .fa-caret-square-up:before {
      content: "\f151";
    }

    .fa-caret-up:before {
      content: "\f0d8";
    }

    .fa-carrot:before {
      content: "\f787";
    }

    .fa-cart-arrow-down:before {
      content: "\f218";
    }

    .fa-cart-plus:before {
      content: "\f217";
    }

    .fa-cash-register:before {
      content: "\f788";
    }

    .fa-cat:before {
      content: "\f6be";
    }

    .fa-cc-amazon-pay:before {
      content: "\f42d";
    }

    .fa-cc-amex:before {
      content: "\f1f3";
    }

    .fa-cc-apple-pay:before {
      content: "\f416";
    }

    .fa-cc-diners-club:before {
      content: "\f24c";
    }

    .fa-cc-discover:before {
      content: "\f1f2";
    }

    .fa-cc-jcb:before {
      content: "\f24b";
    }

    .fa-cc-mastercard:before {
      content: "\f1f1";
    }

    .fa-cc-paypal:before {
      content: "\f1f4";
    }

    .fa-cc-stripe:before {
      content: "\f1f5";
    }

    .fa-cc-visa:before {
      content: "\f1f0";
    }

    .fa-centercode:before {
      content: "\f380";
    }

    .fa-centos:before {
      content: "\f789";
    }

    .fa-certificate:before {
      content: "\f0a3";
    }

    .fa-chair:before {
      content: "\f6c0";
    }

    .fa-chalkboard:before {
      content: "\f51b";
    }

    .fa-chalkboard-teacher:before {
      content: "\f51c";
    }

    .fa-charging-station:before {
      content: "\f5e7";
    }

    .fa-chart-area:before {
      content: "\f1fe";
    }

    .fa-chart-bar:before {
      content: "\f080";
    }

    .fa-chart-line:before {
      content: "\f201";
    }

    .fa-chart-pie:before {
      content: "\f200";
    }

    .fa-check:before {
      content: "\f00c";
    }

    .fa-check-circle:before {
      content: "\f058";
    }

    .fa-check-double:before {
      content: "\f560";
    }

    .fa-check-square:before {
      content: "\f14a";
    }

    .fa-cheese:before {
      content: "\f7ef";
    }

    .fa-chess:before {
      content: "\f439";
    }

    .fa-chess-bishop:before {
      content: "\f43a";
    }

    .fa-chess-board:before {
      content: "\f43c";
    }

    .fa-chess-king:before {
      content: "\f43f";
    }

    .fa-chess-knight:before {
      content: "\f441";
    }

    .fa-chess-pawn:before {
      content: "\f443";
    }

    .fa-chess-queen:before {
      content: "\f445";
    }

    .fa-chess-rook:before {
      content: "\f447";
    }

    .fa-chevron-circle-down:before {
      content: "\f13a";
    }

    .fa-chevron-circle-left:before {
      content: "\f137";
    }

    .fa-chevron-circle-right:before {
      content: "\f138";
    }

    .fa-chevron-circle-up:before {
      content: "\f139";
    }

    .fa-chevron-down:before {
      content: "\f078";
    }

    .fa-chevron-left:before {
      content: "\f053";
    }

    .fa-chevron-right:before {
      content: "\f054";
    }

    .fa-chevron-up:before {
      content: "\f077";
    }

    .fa-child:before {
      content: "\f1ae";
    }

    .fa-chrome:before {
      content: "\f268";
    }

    .fa-chromecast:before {
      content: "\f838";
    }

    .fa-church:before {
      content: "\f51d";
    }

    .fa-circle:before {
      content: "\f111";
    }

    .fa-circle-notch:before {
      content: "\f1ce";
    }

    .fa-city:before {
      content: "\f64f";
    }

    .fa-clinic-medical:before {
      content: "\f7f2";
    }

    .fa-clipboard:before {
      content: "\f328";
    }

    .fa-clipboard-check:before {
      content: "\f46c";
    }

    .fa-clipboard-list:before {
      content: "\f46d";
    }

    .fa-clock:before {
      content: "\f017";
    }

    .fa-clone:before {
      content: "\f24d";
    }

    .fa-closed-captioning:before {
      content: "\f20a";
    }

    .fa-cloud:before {
      content: "\f0c2";
    }

    .fa-cloud-download-alt:before {
      content: "\f381";
    }

    .fa-cloud-meatball:before {
      content: "\f73b";
    }

    .fa-cloud-moon:before {
      content: "\f6c3";
    }

    .fa-cloud-moon-rain:before {
      content: "\f73c";
    }

    .fa-cloud-rain:before {
      content: "\f73d";
    }

    .fa-cloud-showers-heavy:before {
      content: "\f740";
    }

    .fa-cloud-sun:before {
      content: "\f6c4";
    }

    .fa-cloud-sun-rain:before {
      content: "\f743";
    }

    .fa-cloud-upload-alt:before {
      content: "\f382";
    }

    .fa-cloudscale:before {
      content: "\f383";
    }

    .fa-cloudsmith:before {
      content: "\f384";
    }

    .fa-cloudversify:before {
      content: "\f385";
    }

    .fa-cocktail:before {
      content: "\f561";
    }

    .fa-code:before {
      content: "\f121";
    }

    .fa-code-branch:before {
      content: "\f126";
    }

    .fa-codepen:before {
      content: "\f1cb";
    }

    .fa-codiepie:before {
      content: "\f284";
    }

    .fa-coffee:before {
      content: "\f0f4";
    }

    .fa-cog:before {
      content: "\f013";
    }

    .fa-cogs:before {
      content: "\f085";
    }

    .fa-coins:before {
      content: "\f51e";
    }

    .fa-columns:before {
      content: "\f0db";
    }

    .fa-comment:before {
      content: "\f075";
    }

    .fa-comment-alt:before {
      content: "\f27a";
    }

    .fa-comment-dollar:before {
      content: "\f651";
    }

    .fa-comment-dots:before {
      content: "\f4ad";
    }

    .fa-comment-medical:before {
      content: "\f7f5";
    }

    .fa-comment-slash:before {
      content: "\f4b3";
    }

    .fa-comments:before {
      content: "\f086";
    }

    .fa-comments-dollar:before {
      content: "\f653";
    }

    .fa-compact-disc:before {
      content: "\f51f";
    }

    .fa-compass:before {
      content: "\f14e";
    }

    .fa-compress:before {
      content: "\f066";
    }

    .fa-compress-arrows-alt:before {
      content: "\f78c";
    }

    .fa-concierge-bell:before {
      content: "\f562";
    }

    .fa-confluence:before {
      content: "\f78d";
    }

    .fa-connectdevelop:before {
      content: "\f20e";
    }

    .fa-contao:before {
      content: "\f26d";
    }

    .fa-cookie:before {
      content: "\f563";
    }

    .fa-cookie-bite:before {
      content: "\f564";
    }

    .fa-copy:before {
      content: "\f0c5";
    }

    .fa-copyright:before {
      content: "\f1f9";
    }

    .fa-cotton-bureau:before {
      content: "\f89e";
    }

    .fa-couch:before {
      content: "\f4b8";
    }

    .fa-cpanel:before {
      content: "\f388";
    }

    .fa-creative-commons:before {
      content: "\f25e";
    }

    .fa-creative-commons-by:before {
      content: "\f4e7";
    }

    .fa-creative-commons-nc:before {
      content: "\f4e8";
    }

    .fa-creative-commons-nc-eu:before {
      content: "\f4e9";
    }

    .fa-creative-commons-nc-jp:before {
      content: "\f4ea";
    }

    .fa-creative-commons-nd:before {
      content: "\f4eb";
    }

    .fa-creative-commons-pd:before {
      content: "\f4ec";
    }

    .fa-creative-commons-pd-alt:before {
      content: "\f4ed";
    }

    .fa-creative-commons-remix:before {
      content: "\f4ee";
    }

    .fa-creative-commons-sa:before {
      content: "\f4ef";
    }

    .fa-creative-commons-sampling:before {
      content: "\f4f0";
    }

    .fa-creative-commons-sampling-plus:before {
      content: "\f4f1";
    }

    .fa-creative-commons-share:before {
      content: "\f4f2";
    }

    .fa-creative-commons-zero:before {
      content: "\f4f3";
    }

    .fa-credit-card:before {
      content: "\f09d";
    }

    .fa-critical-role:before {
      content: "\f6c9";
    }

    .fa-crop:before {
      content: "\f125";
    }

    .fa-crop-alt:before {
      content: "\f565";
    }

    .fa-cross:before {
      content: "\f654";
    }

    .fa-crosshairs:before {
      content: "\f05b";
    }

    .fa-crow:before {
      content: "\f520";
    }

    .fa-crown:before {
      content: "\f521";
    }

    .fa-crutch:before {
      content: "\f7f7";
    }

    .fa-css3:before {
      content: "\f13c";
    }

    .fa-css3-alt:before {
      content: "\f38b";
    }

    .fa-cube:before {
      content: "\f1b2";
    }

    .fa-cubes:before {
      content: "\f1b3";
    }

    .fa-cut:before {
      content: "\f0c4";
    }

    .fa-cuttlefish:before {
      content: "\f38c";
    }

    .fa-d-and-d:before {
      content: "\f38d";
    }

    .fa-d-and-d-beyond:before {
      content: "\f6ca";
    }

    .fa-dashcube:before {
      content: "\f210";
    }

    .fa-database:before {
      content: "\f1c0";
    }

    .fa-deaf:before {
      content: "\f2a4";
    }

    .fa-delicious:before {
      content: "\f1a5";
    }

    .fa-democrat:before {
      content: "\f747";
    }

    .fa-deploydog:before {
      content: "\f38e";
    }

    .fa-deskpro:before {
      content: "\f38f";
    }

    .fa-desktop:before {
      content: "\f108";
    }

    .fa-dev:before {
      content: "\f6cc";
    }

    .fa-deviantart:before {
      content: "\f1bd";
    }

    .fa-dharmachakra:before {
      content: "\f655";
    }

    .fa-dhl:before {
      content: "\f790";
    }

    .fa-diagnoses:before {
      content: "\f470";
    }

    .fa-diaspora:before {
      content: "\f791";
    }

    .fa-dice:before {
      content: "\f522";
    }

    .fa-dice-d20:before {
      content: "\f6cf";
    }

    .fa-dice-d6:before {
      content: "\f6d1";
    }

    .fa-dice-five:before {
      content: "\f523";
    }

    .fa-dice-four:before {
      content: "\f524";
    }

    .fa-dice-one:before {
      content: "\f525";
    }

    .fa-dice-six:before {
      content: "\f526";
    }

    .fa-dice-three:before {
      content: "\f527";
    }

    .fa-dice-two:before {
      content: "\f528";
    }

    .fa-digg:before {
      content: "\f1a6";
    }

    .fa-digital-ocean:before {
      content: "\f391";
    }

    .fa-digital-tachograph:before {
      content: "\f566";
    }

    .fa-directions:before {
      content: "\f5eb";
    }

    .fa-discord:before {
      content: "\f392";
    }

    .fa-discourse:before {
      content: "\f393";
    }

    .fa-divide:before {
      content: "\f529";
    }

    .fa-dizzy:before {
      content: "\f567";
    }

    .fa-dna:before {
      content: "\f471";
    }

    .fa-dochub:before {
      content: "\f394";
    }

    .fa-docker:before {
      content: "\f395";
    }

    .fa-dog:before {
      content: "\f6d3";
    }

    .fa-dollar-sign:before {
      content: "\f155";
    }

    .fa-dolly:before {
      content: "\f472";
    }

    .fa-dolly-flatbed:before {
      content: "\f474";
    }

    .fa-donate:before {
      content: "\f4b9";
    }

    .fa-door-closed:before {
      content: "\f52a";
    }

    .fa-door-open:before {
      content: "\f52b";
    }

    .fa-dot-circle:before {
      content: "\f192";
    }

    .fa-dove:before {
      content: "\f4ba";
    }

    .fa-download:before {
      content: "\f019";
    }

    .fa-draft2digital:before {
      content: "\f396";
    }

    .fa-drafting-compass:before {
      content: "\f568";
    }

    .fa-dragon:before {
      content: "\f6d5";
    }

    .fa-draw-polygon:before {
      content: "\f5ee";
    }

    .fa-dribbble:before {
      content: "\f17d";
    }

    .fa-dribbble-square:before {
      content: "\f397";
    }

    .fa-dropbox:before {
      content: "\f16b";
    }

    .fa-drum:before {
      content: "\f569";
    }

    .fa-drum-steelpan:before {
      content: "\f56a";
    }

    .fa-drumstick-bite:before {
      content: "\f6d7";
    }

    .fa-drupal:before {
      content: "\f1a9";
    }

    .fa-dumbbell:before {
      content: "\f44b";
    }

    .fa-dumpster:before {
      content: "\f793";
    }

    .fa-dumpster-fire:before {
      content: "\f794";
    }

    .fa-dungeon:before {
      content: "\f6d9";
    }

    .fa-dyalog:before {
      content: "\f399";
    }

    .fa-earlybirds:before {
      content: "\f39a";
    }

    .fa-ebay:before {
      content: "\f4f4";
    }

    .fa-edge:before {
      content: "\f282";
    }

    .fa-edit:before {
      content: "\f044";
    }

    .fa-egg:before {
      content: "\f7fb";
    }

    .fa-eject:before {
      content: "\f052";
    }

    .fa-elementor:before {
      content: "\f430";
    }

    .fa-ellipsis-h:before {
      content: "\f141";
    }

    .fa-ellipsis-v:before {
      content: "\f142";
    }

    .fa-ello:before {
      content: "\f5f1";
    }

    .fa-ember:before {
      content: "\f423";
    }

    .fa-empire:before {
      content: "\f1d1";
    }

    .fa-envelope:before {
      content: "\f0e0";
    }

    .fa-envelope-open:before {
      content: "\f2b6";
    }

    .fa-envelope-open-text:before {
      content: "\f658";
    }

    .fa-envelope-square:before {
      content: "\f199";
    }

    .fa-envira:before {
      content: "\f299";
    }

    .fa-equals:before {
      content: "\f52c";
    }

    .fa-eraser:before {
      content: "\f12d";
    }

    .fa-erlang:before {
      content: "\f39d";
    }

    .fa-ethereum:before {
      content: "\f42e";
    }

    .fa-ethernet:before {
      content: "\f796";
    }

    .fa-etsy:before {
      content: "\f2d7";
    }

    .fa-euro-sign:before {
      content: "\f153";
    }

    .fa-evernote:before {
      content: "\f839";
    }

    .fa-exchange-alt:before {
      content: "\f362";
    }

    .fa-exclamation:before {
      content: "\f12a";
    }

    .fa-exclamation-circle:before {
      content: "\f06a";
    }

    .fa-exclamation-triangle:before {
      content: "\f071";
    }

    .fa-expand:before {
      content: "\f065";
    }

    .fa-expand-arrows-alt:before {
      content: "\f31e";
    }

    .fa-expeditedssl:before {
      content: "\f23e";
    }

    .fa-external-link-alt:before {
      content: "\f35d";
    }

    .fa-external-link-square-alt:before {
      content: "\f360";
    }

    .fa-eye:before {
      content: "\f06e";
    }

    .fa-eye-dropper:before {
      content: "\f1fb";
    }

    .fa-eye-slash:before {
      content: "\f070";
    }

    .fa-facebook:before {
      content: "\f09a";
    }

    .fa-facebook-f:before {
      content: "\f39e";
    }

    .fa-facebook-messenger:before {
      content: "\f39f";
    }

    .fa-facebook-square:before {
      content: "\f082";
    }

    .fa-fan:before {
      content: "\f863";
    }

    .fa-fantasy-flight-games:before {
      content: "\f6dc";
    }

    .fa-fast-backward:before {
      content: "\f049";
    }

    .fa-fast-forward:before {
      content: "\f050";
    }

    .fa-fax:before {
      content: "\f1ac";
    }

    .fa-feather:before {
      content: "\f52d";
    }

    .fa-feather-alt:before {
      content: "\f56b";
    }

    .fa-fedex:before {
      content: "\f797";
    }

    .fa-fedora:before {
      content: "\f798";
    }

    .fa-female:before {
      content: "\f182";
    }

    .fa-fighter-jet:before {
      content: "\f0fb";
    }

    .fa-figma:before {
      content: "\f799";
    }

    .fa-file:before {
      content: "\f15b";
    }

    .fa-file-alt:before {
      content: "\f15c";
    }

    .fa-file-archive:before {
      content: "\f1c6";
    }

    .fa-file-audio:before {
      content: "\f1c7";
    }

    .fa-file-code:before {
      content: "\f1c9";
    }

    .fa-file-contract:before {
      content: "\f56c";
    }

    .fa-file-csv:before {
      content: "\f6dd";
    }

    .fa-file-download:before {
      content: "\f56d";
    }

    .fa-file-excel:before {
      content: "\f1c3";
    }

    .fa-file-export:before {
      content: "\f56e";
    }

    .fa-file-image:before {
      content: "\f1c5";
    }

    .fa-file-import:before {
      content: "\f56f";
    }

    .fa-file-invoice:before {
      content: "\f570";
    }

    .fa-file-invoice-dollar:before {
      content: "\f571";
    }

    .fa-file-medical:before {
      content: "\f477";
    }

    .fa-file-medical-alt:before {
      content: "\f478";
    }

    .fa-file-pdf:before {
      content: "\f1c1";
    }

    .fa-file-powerpoint:before {
      content: "\f1c4";
    }

    .fa-file-prescription:before {
      content: "\f572";
    }

    .fa-file-signature:before {
      content: "\f573";
    }

    .fa-file-upload:before {
      content: "\f574";
    }

    .fa-file-video:before {
      content: "\f1c8";
    }

    .fa-file-word:before {
      content: "\f1c2";
    }

    .fa-fill:before {
      content: "\f575";
    }

    .fa-fill-drip:before {
      content: "\f576";
    }

    .fa-film:before {
      content: "\f008";
    }

    .fa-filter:before {
      content: "\f0b0";
    }

    .fa-fingerprint:before {
      content: "\f577";
    }

    .fa-fire:before {
      content: "\f06d";
    }

    .fa-fire-alt:before {
      content: "\f7e4";
    }

    .fa-fire-extinguisher:before {
      content: "\f134";
    }

    .fa-firefox:before {
      content: "\f269";
    }

    .fa-first-aid:before {
      content: "\f479";
    }

    .fa-first-order:before {
      content: "\f2b0";
    }

    .fa-first-order-alt:before {
      content: "\f50a";
    }

    .fa-firstdraft:before {
      content: "\f3a1";
    }

    .fa-fish:before {
      content: "\f578";
    }

    .fa-fist-raised:before {
      content: "\f6de";
    }

    .fa-flag:before {
      content: "\f024";
    }

    .fa-flag-checkered:before {
      content: "\f11e";
    }

    .fa-flag-usa:before {
      content: "\f74d";
    }

    .fa-flask:before {
      content: "\f0c3";
    }

    .fa-flickr:before {
      content: "\f16e";
    }

    .fa-flipboard:before {
      content: "\f44d";
    }

    .fa-flushed:before {
      content: "\f579";
    }

    .fa-fly:before {
      content: "\f417";
    }

    .fa-folder:before {
      content: "\f07b";
    }

    .fa-folder-minus:before {
      content: "\f65d";
    }

    .fa-folder-open:before {
      content: "\f07c";
    }

    .fa-folder-plus:before {
      content: "\f65e";
    }

    .fa-font:before {
      content: "\f031";
    }

    .fa-font-awesome:before {
      content: "\f2b4";
    }

    .fa-font-awesome-alt:before {
      content: "\f35c";
    }

    .fa-font-awesome-flag:before {
      content: "\f425";
    }

    .fa-font-awesome-logo-full:before {
      content: "\f4e6";
    }

    .fa-fonticons:before {
      content: "\f280";
    }

    .fa-fonticons-fi:before {
      content: "\f3a2";
    }

    .fa-football-ball:before {
      content: "\f44e";
    }

    .fa-fort-awesome:before {
      content: "\f286";
    }

    .fa-fort-awesome-alt:before {
      content: "\f3a3";
    }

    .fa-forumbee:before {
      content: "\f211";
    }

    .fa-forward:before {
      content: "\f04e";
    }

    .fa-foursquare:before {
      content: "\f180";
    }

    .fa-free-code-camp:before {
      content: "\f2c5";
    }

    .fa-freebsd:before {
      content: "\f3a4";
    }

    .fa-frog:before {
      content: "\f52e";
    }

    .fa-frown:before {
      content: "\f119";
    }

    .fa-frown-open:before {
      content: "\f57a";
    }

    .fa-fulcrum:before {
      content: "\f50b";
    }

    .fa-funnel-dollar:before {
      content: "\f662";
    }

    .fa-futbol:before {
      content: "\f1e3";
    }

    .fa-galactic-republic:before {
      content: "\f50c";
    }

    .fa-galactic-senate:before {
      content: "\f50d";
    }

    .fa-gamepad:before {
      content: "\f11b";
    }

    .fa-gas-pump:before {
      content: "\f52f";
    }

    .fa-gavel:before {
      content: "\f0e3";
    }

    .fa-gem:before {
      content: "\f3a5";
    }

    .fa-genderless:before {
      content: "\f22d";
    }

    .fa-get-pocket:before {
      content: "\f265";
    }

    .fa-gg:before {
      content: "\f260";
    }

    .fa-gg-circle:before {
      content: "\f261";
    }

    .fa-ghost:before {
      content: "\f6e2";
    }

    .fa-gift:before {
      content: "\f06b";
    }

    .fa-gifts:before {
      content: "\f79c";
    }

    .fa-git:before {
      content: "\f1d3";
    }

    .fa-git-alt:before {
      content: "\f841";
    }

    .fa-git-square:before {
      content: "\f1d2";
    }

    .fa-github:before {
      content: "\f09b";
    }

    .fa-github-alt:before {
      content: "\f113";
    }

    .fa-github-square:before {
      content: "\f092";
    }

    .fa-gitkraken:before {
      content: "\f3a6";
    }

    .fa-gitlab:before {
      content: "\f296";
    }

    .fa-gitter:before {
      content: "\f426";
    }

    .fa-glass-cheers:before {
      content: "\f79f";
    }

    .fa-glass-martini:before {
      content: "\f000";
    }

    .fa-glass-martini-alt:before {
      content: "\f57b";
    }

    .fa-glass-whiskey:before {
      content: "\f7a0";
    }

    .fa-glasses:before {
      content: "\f530";
    }

    .fa-glide:before {
      content: "\f2a5";
    }

    .fa-glide-g:before {
      content: "\f2a6";
    }

    .fa-globe:before {
      content: "\f0ac";
    }

    .fa-globe-africa:before {
      content: "\f57c";
    }

    .fa-globe-americas:before {
      content: "\f57d";
    }

    .fa-globe-asia:before {
      content: "\f57e";
    }

    .fa-globe-europe:before {
      content: "\f7a2";
    }

    .fa-gofore:before {
      content: "\f3a7";
    }

    .fa-golf-ball:before {
      content: "\f450";
    }

    .fa-goodreads:before {
      content: "\f3a8";
    }

    .fa-goodreads-g:before {
      content: "\f3a9";
    }

    .fa-google:before {
      content: "\f1a0";
    }

    .fa-google-drive:before {
      content: "\f3aa";
    }

    .fa-google-play:before {
      content: "\f3ab";
    }

    .fa-google-plus:before {
      content: "\f2b3";
    }

    .fa-google-plus-g:before {
      content: "\f0d5";
    }

    .fa-google-plus-square:before {
      content: "\f0d4";
    }

    .fa-google-wallet:before {
      content: "\f1ee";
    }

    .fa-gopuram:before {
      content: "\f664";
    }

    .fa-graduation-cap:before {
      content: "\f19d";
    }

    .fa-gratipay:before {
      content: "\f184";
    }

    .fa-grav:before {
      content: "\f2d6";
    }

    .fa-greater-than:before {
      content: "\f531";
    }

    .fa-greater-than-equal:before {
      content: "\f532";
    }

    .fa-grimace:before {
      content: "\f57f";
    }

    .fa-grin:before {
      content: "\f580";
    }

    .fa-grin-alt:before {
      content: "\f581";
    }

    .fa-grin-beam:before {
      content: "\f582";
    }

    .fa-grin-beam-sweat:before {
      content: "\f583";
    }

    .fa-grin-hearts:before {
      content: "\f584";
    }

    .fa-grin-squint:before {
      content: "\f585";
    }

    .fa-grin-squint-tears:before {
      content: "\f586";
    }

    .fa-grin-stars:before {
      content: "\f587";
    }

    .fa-grin-tears:before {
      content: "\f588";
    }

    .fa-grin-tongue:before {
      content: "\f589";
    }

    .fa-grin-tongue-squint:before {
      content: "\f58a";
    }

    .fa-grin-tongue-wink:before {
      content: "\f58b";
    }

    .fa-grin-wink:before {
      content: "\f58c";
    }

    .fa-grip-horizontal:before {
      content: "\f58d";
    }

    .fa-grip-lines:before {
      content: "\f7a4";
    }

    .fa-grip-lines-vertical:before {
      content: "\f7a5";
    }

    .fa-grip-vertical:before {
      content: "\f58e";
    }

    .fa-gripfire:before {
      content: "\f3ac";
    }

    .fa-grunt:before {
      content: "\f3ad";
    }

    .fa-guitar:before {
      content: "\f7a6";
    }

    .fa-gulp:before {
      content: "\f3ae";
    }

    .fa-h-square:before {
      content: "\f0fd";
    }

    .fa-hacker-news:before {
      content: "\f1d4";
    }

    .fa-hacker-news-square:before {
      content: "\f3af";
    }

    .fa-hackerrank:before {
      content: "\f5f7";
    }

    .fa-hamburger:before {
      content: "\f805";
    }

    .fa-hammer:before {
      content: "\f6e3";
    }

    .fa-hamsa:before {
      content: "\f665";
    }

    .fa-hand-holding:before {
      content: "\f4bd";
    }

    .fa-hand-holding-heart:before {
      content: "\f4be";
    }

    .fa-hand-holding-usd:before {
      content: "\f4c0";
    }

    .fa-hand-lizard:before {
      content: "\f258";
    }

    .fa-hand-middle-finger:before {
      content: "\f806";
    }

    .fa-hand-paper:before {
      content: "\f256";
    }

    .fa-hand-peace:before {
      content: "\f25b";
    }

    .fa-hand-point-down:before {
      content: "\f0a7";
    }

    .fa-hand-point-left:before {
      content: "\f0a5";
    }

    .fa-hand-point-right:before {
      content: "\f0a4";
    }

    .fa-hand-point-up:before {
      content: "\f0a6";
    }

    .fa-hand-pointer:before {
      content: "\f25a";
    }

    .fa-hand-rock:before {
      content: "\f255";
    }

    .fa-hand-scissors:before {
      content: "\f257";
    }

    .fa-hand-spock:before {
      content: "\f259";
    }

    .fa-hands:before {
      content: "\f4c2";
    }

    .fa-hands-helping:before {
      content: "\f4c4";
    }

    .fa-handshake:before {
      content: "\f2b5";
    }

    .fa-hanukiah:before {
      content: "\f6e6";
    }

    .fa-hard-hat:before {
      content: "\f807";
    }

    .fa-hashtag:before {
      content: "\f292";
    }

    .fa-hat-wizard:before {
      content: "\f6e8";
    }

    .fa-haykal:before {
      content: "\f666";
    }

    .fa-hdd:before {
      content: "\f0a0";
    }

    .fa-heading:before {
      content: "\f1dc";
    }

    .fa-headphones:before {
      content: "\f025";
    }

    .fa-headphones-alt:before {
      content: "\f58f";
    }

    .fa-headset:before {
      content: "\f590";
    }

    .fa-heart:before {
      content: "\f004";
    }

    .fa-heart-broken:before {
      content: "\f7a9";
    }

    .fa-heartbeat:before {
      content: "\f21e";
    }

    .fa-helicopter:before {
      content: "\f533";
    }

    .fa-highlighter:before {
      content: "\f591";
    }

    .fa-hiking:before {
      content: "\f6ec";
    }

    .fa-hippo:before {
      content: "\f6ed";
    }

    .fa-hips:before {
      content: "\f452";
    }

    .fa-hire-a-helper:before {
      content: "\f3b0";
    }

    .fa-history:before {
      content: "\f1da";
    }

    .fa-hockey-puck:before {
      content: "\f453";
    }

    .fa-holly-berry:before {
      content: "\f7aa";
    }

    .fa-home:before {
      content: "\f015";
    }

    .fa-hooli:before {
      content: "\f427";
    }

    .fa-hornbill:before {
      content: "\f592";
    }

    .fa-horse:before {
      content: "\f6f0";
    }

    .fa-horse-head:before {
      content: "\f7ab";
    }

    .fa-hospital:before {
      content: "\f0f8";
    }

    .fa-hospital-alt:before {
      content: "\f47d";
    }

    .fa-hospital-symbol:before {
      content: "\f47e";
    }

    .fa-hot-tub:before {
      content: "\f593";
    }

    .fa-hotdog:before {
      content: "\f80f";
    }

    .fa-hotel:before {
      content: "\f594";
    }

    .fa-hotjar:before {
      content: "\f3b1";
    }

    .fa-hourglass:before {
      content: "\f254";
    }

    .fa-hourglass-end:before {
      content: "\f253";
    }

    .fa-hourglass-half:before {
      content: "\f252";
    }

    .fa-hourglass-start:before {
      content: "\f251";
    }

    .fa-house-damage:before {
      content: "\f6f1";
    }

    .fa-houzz:before {
      content: "\f27c";
    }

    .fa-hryvnia:before {
      content: "\f6f2";
    }

    .fa-html5:before {
      content: "\f13b";
    }

    .fa-hubspot:before {
      content: "\f3b2";
    }

    .fa-i-cursor:before {
      content: "\f246";
    }

    .fa-ice-cream:before {
      content: "\f810";
    }

    .fa-icicles:before {
      content: "\f7ad";
    }

    .fa-icons:before {
      content: "\f86d";
    }

    .fa-id-badge:before {
      content: "\f2c1";
    }

    .fa-id-card:before {
      content: "\f2c2";
    }

    .fa-id-card-alt:before {
      content: "\f47f";
    }

    .fa-igloo:before {
      content: "\f7ae";
    }

    .fa-image:before {
      content: "\f03e";
    }

    .fa-images:before {
      content: "\f302";
    }

    .fa-imdb:before {
      content: "\f2d8";
    }

    .fa-inbox:before {
      content: "\f01c";
    }

    .fa-indent:before {
      content: "\f03c";
    }

    .fa-industry:before {
      content: "\f275";
    }

    .fa-infinity:before {
      content: "\f534";
    }

    .fa-info:before {
      content: "\f129";
    }

    .fa-info-circle:before {
      content: "\f05a";
    }

    .fa-instagram:before {
      content: "\f16d";
    }

    .fa-intercom:before {
      content: "\f7af";
    }

    .fa-internet-explorer:before {
      content: "\f26b";
    }

    .fa-invision:before {
      content: "\f7b0";
    }

    .fa-ioxhost:before {
      content: "\f208";
    }

    .fa-italic:before {
      content: "\f033";
    }

    .fa-itch-io:before {
      content: "\f83a";
    }

    .fa-itunes:before {
      content: "\f3b4";
    }

    .fa-itunes-note:before {
      content: "\f3b5";
    }

    .fa-java:before {
      content: "\f4e4";
    }

    .fa-jedi:before {
      content: "\f669";
    }

    .fa-jedi-order:before {
      content: "\f50e";
    }

    .fa-jenkins:before {
      content: "\f3b6";
    }

    .fa-jira:before {
      content: "\f7b1";
    }

    .fa-joget:before {
      content: "\f3b7";
    }

    .fa-joint:before {
      content: "\f595";
    }

    .fa-joomla:before {
      content: "\f1aa";
    }

    .fa-journal-whills:before {
      content: "\f66a";
    }

    .fa-js:before {
      content: "\f3b8";
    }

    .fa-js-square:before {
      content: "\f3b9";
    }

    .fa-jsfiddle:before {
      content: "\f1cc";
    }

    .fa-kaaba:before {
      content: "\f66b";
    }

    .fa-kaggle:before {
      content: "\f5fa";
    }

    .fa-key:before {
      content: "\f084";
    }

    .fa-keybase:before {
      content: "\f4f5";
    }

    .fa-keyboard:before {
      content: "\f11c";
    }

    .fa-keycdn:before {
      content: "\f3ba";
    }

    .fa-khanda:before {
      content: "\f66d";
    }

    .fa-kickstarter:before {
      content: "\f3bb";
    }

    .fa-kickstarter-k:before {
      content: "\f3bc";
    }

    .fa-kiss:before {
      content: "\f596";
    }

    .fa-kiss-beam:before {
      content: "\f597";
    }

    .fa-kiss-wink-heart:before {
      content: "\f598";
    }

    .fa-kiwi-bird:before {
      content: "\f535";
    }

    .fa-korvue:before {
      content: "\f42f";
    }

    .fa-landmark:before {
      content: "\f66f";
    }

    .fa-language:before {
      content: "\f1ab";
    }

    .fa-laptop:before {
      content: "\f109";
    }

    .fa-laptop-code:before {
      content: "\f5fc";
    }

    .fa-laptop-medical:before {
      content: "\f812";
    }

    .fa-laravel:before {
      content: "\f3bd";
    }

    .fa-lastfm:before {
      content: "\f202";
    }

    .fa-lastfm-square:before {
      content: "\f203";
    }

    .fa-laugh:before {
      content: "\f599";
    }

    .fa-laugh-beam:before {
      content: "\f59a";
    }

    .fa-laugh-squint:before {
      content: "\f59b";
    }

    .fa-laugh-wink:before {
      content: "\f59c";
    }

    .fa-layer-group:before {
      content: "\f5fd";
    }

    .fa-leaf:before {
      content: "\f06c";
    }

    .fa-leanpub:before {
      content: "\f212";
    }

    .fa-lemon:before {
      content: "\f094";
    }

    .fa-less:before {
      content: "\f41d";
    }

    .fa-less-than:before {
      content: "\f536";
    }

    .fa-less-than-equal:before {
      content: "\f537";
    }

    .fa-level-down-alt:before {
      content: "\f3be";
    }

    .fa-level-up-alt:before {
      content: "\f3bf";
    }

    .fa-life-ring:before {
      content: "\f1cd";
    }

    .fa-lightbulb:before {
      content: "\f0eb";
    }

    .fa-line:before {
      content: "\f3c0";
    }

    .fa-link:before {
      content: "\f0c1";
    }

    .fa-linkedin:before {
      content: "\f08c";
    }

    .fa-linkedin-in:before {
      content: "\f0e1";
    }

    .fa-linode:before {
      content: "\f2b8";
    }

    .fa-linux:before {
      content: "\f17c";
    }

    .fa-lira-sign:before {
      content: "\f195";
    }

    .fa-list:before {
      content: "\f03a";
    }

    .fa-list-alt:before {
      content: "\f022";
    }

    .fa-list-ol:before {
      content: "\f0cb";
    }

    .fa-list-ul:before {
      content: "\f0ca";
    }

    .fa-location-arrow:before {
      content: "\f124";
    }

    .fa-lock:before {
      content: "\f023";
    }

    .fa-lock-open:before {
      content: "\f3c1";
    }

    .fa-long-arrow-alt-down:before {
      content: "\f309";
    }

    .fa-long-arrow-alt-left:before {
      content: "\f30a";
    }

    .fa-long-arrow-alt-right:before {
      content: "\f30b";
    }

    .fa-long-arrow-alt-up:before {
      content: "\f30c";
    }

    .fa-low-vision:before {
      content: "\f2a8";
    }

    .fa-luggage-cart:before {
      content: "\f59d";
    }

    .fa-lyft:before {
      content: "\f3c3";
    }

    .fa-magento:before {
      content: "\f3c4";
    }

    .fa-magic:before {
      content: "\f0d0";
    }

    .fa-magnet:before {
      content: "\f076";
    }

    .fa-mail-bulk:before {
      content: "\f674";
    }

    .fa-mailchimp:before {
      content: "\f59e";
    }

    .fa-male:before {
      content: "\f183";
    }

    .fa-mandalorian:before {
      content: "\f50f";
    }

    .fa-map:before {
      content: "\f279";
    }

    .fa-map-marked:before {
      content: "\f59f";
    }

    .fa-map-marked-alt:before {
      content: "\f5a0";
    }

    .fa-map-marker:before {
      content: "\f041";
    }

    .fa-map-marker-alt:before {
      content: "\f3c5";
    }

    .fa-map-pin:before {
      content: "\f276";
    }

    .fa-map-signs:before {
      content: "\f277";
    }

    .fa-markdown:before {
      content: "\f60f";
    }

    .fa-marker:before {
      content: "\f5a1";
    }

    .fa-mars:before {
      content: "\f222";
    }

    .fa-mars-double:before {
      content: "\f227";
    }

    .fa-mars-stroke:before {
      content: "\f229";
    }

    .fa-mars-stroke-h:before {
      content: "\f22b";
    }

    .fa-mars-stroke-v:before {
      content: "\f22a";
    }

    .fa-mask:before {
      content: "\f6fa";
    }

    .fa-mastodon:before {
      content: "\f4f6";
    }

    .fa-maxcdn:before {
      content: "\f136";
    }

    .fa-medal:before {
      content: "\f5a2";
    }

    .fa-medapps:before {
      content: "\f3c6";
    }

    .fa-medium:before {
      content: "\f23a";
    }

    .fa-medium-m:before {
      content: "\f3c7";
    }

    .fa-medkit:before {
      content: "\f0fa";
    }

    .fa-medrt:before {
      content: "\f3c8";
    }

    .fa-meetup:before {
      content: "\f2e0";
    }

    .fa-megaport:before {
      content: "\f5a3";
    }

    .fa-meh:before {
      content: "\f11a";
    }

    .fa-meh-blank:before {
      content: "\f5a4";
    }

    .fa-meh-rolling-eyes:before {
      content: "\f5a5";
    }

    .fa-memory:before {
      content: "\f538";
    }

    .fa-mendeley:before {
      content: "\f7b3";
    }

    .fa-menorah:before {
      content: "\f676";
    }

    .fa-mercury:before {
      content: "\f223";
    }

    .fa-meteor:before {
      content: "\f753";
    }

    .fa-microchip:before {
      content: "\f2db";
    }

    .fa-microphone:before {
      content: "\f130";
    }

    .fa-microphone-alt:before {
      content: "\f3c9";
    }

    .fa-microphone-alt-slash:before {
      content: "\f539";
    }

    .fa-microphone-slash:before {
      content: "\f131";
    }

    .fa-microscope:before {
      content: "\f610";
    }

    .fa-microsoft:before {
      content: "\f3ca";
    }

    .fa-minus:before {
      content: "\f068";
    }

    .fa-minus-circle:before {
      content: "\f056";
    }

    .fa-minus-square:before {
      content: "\f146";
    }

    .fa-mitten:before {
      content: "\f7b5";
    }

    .fa-mix:before {
      content: "\f3cb";
    }

    .fa-mixcloud:before {
      content: "\f289";
    }

    .fa-mizuni:before {
      content: "\f3cc";
    }

    .fa-mobile:before {
      content: "\f10b";
    }

    .fa-mobile-alt:before {
      content: "\f3cd";
    }

    .fa-modx:before {
      content: "\f285";
    }

    .fa-monero:before {
      content: "\f3d0";
    }

    .fa-money-bill:before {
      content: "\f0d6";
    }

    .fa-money-bill-alt:before {
      content: "\f3d1";
    }

    .fa-money-bill-wave:before {
      content: "\f53a";
    }

    .fa-money-bill-wave-alt:before {
      content: "\f53b";
    }

    .fa-money-check:before {
      content: "\f53c";
    }

    .fa-money-check-alt:before {
      content: "\f53d";
    }

    .fa-monument:before {
      content: "\f5a6";
    }

    .fa-moon:before {
      content: "\f186";
    }

    .fa-mortar-pestle:before {
      content: "\f5a7";
    }

    .fa-mosque:before {
      content: "\f678";
    }

    .fa-motorcycle:before {
      content: "\f21c";
    }

    .fa-mountain:before {
      content: "\f6fc";
    }

    .fa-mouse-pointer:before {
      content: "\f245";
    }

    .fa-mug-hot:before {
      content: "\f7b6";
    }

    .fa-music:before {
      content: "\f001";
    }

    .fa-napster:before {
      content: "\f3d2";
    }

    .fa-neos:before {
      content: "\f612";
    }

    .fa-network-wired:before {
      content: "\f6ff";
    }

    .fa-neuter:before {
      content: "\f22c";
    }

    .fa-newspaper:before {
      content: "\f1ea";
    }

    .fa-nimblr:before {
      content: "\f5a8";
    }

    .fa-node:before {
      content: "\f419";
    }

    .fa-node-js:before {
      content: "\f3d3";
    }

    .fa-not-equal:before {
      content: "\f53e";
    }

    .fa-notes-medical:before {
      content: "\f481";
    }

    .fa-npm:before {
      content: "\f3d4";
    }

    .fa-ns8:before {
      content: "\f3d5";
    }

    .fa-nutritionix:before {
      content: "\f3d6";
    }

    .fa-object-group:before {
      content: "\f247";
    }

    .fa-object-ungroup:before {
      content: "\f248";
    }

    .fa-odnoklassniki:before {
      content: "\f263";
    }

    .fa-odnoklassniki-square:before {
      content: "\f264";
    }

    .fa-oil-can:before {
      content: "\f613";
    }

    .fa-old-republic:before {
      content: "\f510";
    }

    .fa-om:before {
      content: "\f679";
    }

    .fa-opencart:before {
      content: "\f23d";
    }

    .fa-openid:before {
      content: "\f19b";
    }

    .fa-opera:before {
      content: "\f26a";
    }

    .fa-optin-monster:before {
      content: "\f23c";
    }

    .fa-osi:before {
      content: "\f41a";
    }

    .fa-otter:before {
      content: "\f700";
    }

    .fa-outdent:before {
      content: "\f03b";
    }

    .fa-page4:before {
      content: "\f3d7";
    }

    .fa-pagelines:before {
      content: "\f18c";
    }

    .fa-pager:before {
      content: "\f815";
    }

    .fa-paint-brush:before {
      content: "\f1fc";
    }

    .fa-paint-roller:before {
      content: "\f5aa";
    }

    .fa-palette:before {
      content: "\f53f";
    }

    .fa-palfed:before {
      content: "\f3d8";
    }

    .fa-pallet:before {
      content: "\f482";
    }

    .fa-paper-plane:before {
      content: "\f1d8";
    }

    .fa-paperclip:before {
      content: "\f0c6";
    }

    .fa-parachute-box:before {
      content: "\f4cd";
    }

    .fa-paragraph:before {
      content: "\f1dd";
    }

    .fa-parking:before {
      content: "\f540";
    }

    .fa-passport:before {
      content: "\f5ab";
    }

    .fa-pastafarianism:before {
      content: "\f67b";
    }

    .fa-paste:before {
      content: "\f0ea";
    }

    .fa-patreon:before {
      content: "\f3d9";
    }

    .fa-pause:before {
      content: "\f04c";
    }

    .fa-pause-circle:before {
      content: "\f28b";
    }

    .fa-paw:before {
      content: "\f1b0";
    }

    .fa-paypal:before {
      content: "\f1ed";
    }

    .fa-peace:before {
      content: "\f67c";
    }

    .fa-pen:before {
      content: "\f304";
    }

    .fa-pen-alt:before {
      content: "\f305";
    }

    .fa-pen-fancy:before {
      content: "\f5ac";
    }

    .fa-pen-nib:before {
      content: "\f5ad";
    }

    .fa-pen-square:before {
      content: "\f14b";
    }

    .fa-pencil-alt:before {
      content: "\f303";
    }

    .fa-pencil-ruler:before {
      content: "\f5ae";
    }

    .fa-penny-arcade:before {
      content: "\f704";
    }

    .fa-people-carry:before {
      content: "\f4ce";
    }

    .fa-pepper-hot:before {
      content: "\f816";
    }

    .fa-percent:before {
      content: "\f295";
    }

    .fa-percentage:before {
      content: "\f541";
    }

    .fa-periscope:before {
      content: "\f3da";
    }

    .fa-person-booth:before {
      content: "\f756";
    }

    .fa-phabricator:before {
      content: "\f3db";
    }

    .fa-phoenix-framework:before {
      content: "\f3dc";
    }

    .fa-phoenix-squadron:before {
      content: "\f511";
    }

    .fa-phone:before {
      content: "\f095";
    }

    .fa-phone-alt:before {
      content: "\f879";
    }

    .fa-phone-slash:before {
      content: "\f3dd";
    }

    .fa-phone-square:before {
      content: "\f098";
    }

    .fa-phone-square-alt:before {
      content: "\f87b";
    }

    .fa-phone-volume:before {
      content: "\f2a0";
    }

    .fa-photo-video:before {
      content: "\f87c";
    }

    .fa-php:before {
      content: "\f457";
    }

    .fa-pied-piper:before {
      content: "\f2ae";
    }

    .fa-pied-piper-alt:before {
      content: "\f1a8";
    }

    .fa-pied-piper-hat:before {
      content: "\f4e5";
    }

    .fa-pied-piper-pp:before {
      content: "\f1a7";
    }

    .fa-piggy-bank:before {
      content: "\f4d3";
    }

    .fa-pills:before {
      content: "\f484";
    }

    .fa-pinterest:before {
      content: "\f0d2";
    }

    .fa-pinterest-p:before {
      content: "\f231";
    }

    .fa-pinterest-square:before {
      content: "\f0d3";
    }

    .fa-pizza-slice:before {
      content: "\f818";
    }

    .fa-place-of-worship:before {
      content: "\f67f";
    }

    .fa-plane:before {
      content: "\f072";
    }

    .fa-plane-arrival:before {
      content: "\f5af";
    }

    .fa-plane-departure:before {
      content: "\f5b0";
    }

    .fa-play:before {
      content: "\f04b";
    }

    .fa-play-circle:before {
      content: "\f144";
    }

    .fa-playstation:before {
      content: "\f3df";
    }

    .fa-plug:before {
      content: "\f1e6";
    }

    .fa-plus:before {
      content: "\f067";
    }

    .fa-plus-circle:before {
      content: "\f055";
    }

    .fa-plus-square:before {
      content: "\f0fe";
    }

    .fa-podcast:before {
      content: "\f2ce";
    }

    .fa-poll:before {
      content: "\f681";
    }

    .fa-poll-h:before {
      content: "\f682";
    }

    .fa-poo:before {
      content: "\f2fe";
    }

    .fa-poo-storm:before {
      content: "\f75a";
    }

    .fa-poop:before {
      content: "\f619";
    }

    .fa-portrait:before {
      content: "\f3e0";
    }

    .fa-pound-sign:before {
      content: "\f154";
    }

    .fa-power-off:before {
      content: "\f011";
    }

    .fa-pray:before {
      content: "\f683";
    }

    .fa-praying-hands:before {
      content: "\f684";
    }

    .fa-prescription:before {
      content: "\f5b1";
    }

    .fa-prescription-bottle:before {
      content: "\f485";
    }

    .fa-prescription-bottle-alt:before {
      content: "\f486";
    }

    .fa-print:before {
      content: "\f02f";
    }

    .fa-procedures:before {
      content: "\f487";
    }

    .fa-product-hunt:before {
      content: "\f288";
    }

    .fa-project-diagram:before {
      content: "\f542";
    }

    .fa-pushed:before {
      content: "\f3e1";
    }

    .fa-puzzle-piece:before {
      content: "\f12e";
    }

    .fa-python:before {
      content: "\f3e2";
    }

    .fa-qq:before {
      content: "\f1d6";
    }

    .fa-qrcode:before {
      content: "\f029";
    }

    .fa-question:before {
      content: "\f128";
    }

    .fa-question-circle:before {
      content: "\f059";
    }

    .fa-quidditch:before {
      content: "\f458";
    }

    .fa-quinscape:before {
      content: "\f459";
    }

    .fa-quora:before {
      content: "\f2c4";
    }

    .fa-quote-left:before {
      content: "\f10d";
    }

    .fa-quote-right:before {
      content: "\f10e";
    }

    .fa-quran:before {
      content: "\f687";
    }

    .fa-r-project:before {
      content: "\f4f7";
    }

    .fa-radiation:before {
      content: "\f7b9";
    }

    .fa-radiation-alt:before {
      content: "\f7ba";
    }

    .fa-rainbow:before {
      content: "\f75b";
    }

    .fa-random:before {
      content: "\f074";
    }

    .fa-raspberry-pi:before {
      content: "\f7bb";
    }

    .fa-ravelry:before {
      content: "\f2d9";
    }

    .fa-react:before {
      content: "\f41b";
    }

    .fa-reacteurope:before {
      content: "\f75d";
    }

    .fa-readme:before {
      content: "\f4d5";
    }

    .fa-rebel:before {
      content: "\f1d0";
    }

    .fa-receipt:before {
      content: "\f543";
    }

    .fa-recycle:before {
      content: "\f1b8";
    }

    .fa-red-river:before {
      content: "\f3e3";
    }

    .fa-reddit:before {
      content: "\f1a1";
    }

    .fa-reddit-alien:before {
      content: "\f281";
    }

    .fa-reddit-square:before {
      content: "\f1a2";
    }

    .fa-redhat:before {
      content: "\f7bc";
    }

    .fa-redo:before {
      content: "\f01e";
    }

    .fa-redo-alt:before {
      content: "\f2f9";
    }

    .fa-registered:before {
      content: "\f25d";
    }

    .fa-remove-format:before {
      content: "\f87d";
    }

    .fa-renren:before {
      content: "\f18b";
    }

    .fa-reply:before {
      content: "\f3e5";
    }

    .fa-reply-all:before {
      content: "\f122";
    }

    .fa-replyd:before {
      content: "\f3e6";
    }

    .fa-republican:before {
      content: "\f75e";
    }

    .fa-researchgate:before {
      content: "\f4f8";
    }

    .fa-resolving:before {
      content: "\f3e7";
    }

    .fa-restroom:before {
      content: "\f7bd";
    }

    .fa-retweet:before {
      content: "\f079";
    }

    .fa-rev:before {
      content: "\f5b2";
    }

    .fa-ribbon:before {
      content: "\f4d6";
    }

    .fa-ring:before {
      content: "\f70b";
    }

    .fa-road:before {
      content: "\f018";
    }

    .fa-robot:before {
      content: "\f544";
    }

    .fa-rocket:before {
      content: "\f135";
    }

    .fa-rocketchat:before {
      content: "\f3e8";
    }

    .fa-rockrms:before {
      content: "\f3e9";
    }

    .fa-route:before {
      content: "\f4d7";
    }

    .fa-rss:before {
      content: "\f09e";
    }

    .fa-rss-square:before {
      content: "\f143";
    }

    .fa-ruble-sign:before {
      content: "\f158";
    }

    .fa-ruler:before {
      content: "\f545";
    }

    .fa-ruler-combined:before {
      content: "\f546";
    }

    .fa-ruler-horizontal:before {
      content: "\f547";
    }

    .fa-ruler-vertical:before {
      content: "\f548";
    }

    .fa-running:before {
      content: "\f70c";
    }

    .fa-rupee-sign:before {
      content: "\f156";
    }

    .fa-sad-cry:before {
      content: "\f5b3";
    }

    .fa-sad-tear:before {
      content: "\f5b4";
    }

    .fa-safari:before {
      content: "\f267";
    }

    .fa-salesforce:before {
      content: "\f83b";
    }

    .fa-sass:before {
      content: "\f41e";
    }

    .fa-satellite:before {
      content: "\f7bf";
    }

    .fa-satellite-dish:before {
      content: "\f7c0";
    }

    .fa-save:before {
      content: "\f0c7";
    }

    .fa-schlix:before {
      content: "\f3ea";
    }

    .fa-school:before {
      content: "\f549";
    }

    .fa-screwdriver:before {
      content: "\f54a";
    }

    .fa-scribd:before {
      content: "\f28a";
    }

    .fa-scroll:before {
      content: "\f70e";
    }

    .fa-sd-card:before {
      content: "\f7c2";
    }

    .fa-search:before {
      content: "\f002";
    }

    .fa-search-dollar:before {
      content: "\f688";
    }

    .fa-search-location:before {
      content: "\f689";
    }

    .fa-search-minus:before {
      content: "\f010";
    }

    .fa-search-plus:before {
      content: "\f00e";
    }

    .fa-searchengin:before {
      content: "\f3eb";
    }

    .fa-seedling:before {
      content: "\f4d8";
    }

    .fa-sellcast:before {
      content: "\f2da";
    }

    .fa-sellsy:before {
      content: "\f213";
    }

    .fa-server:before {
      content: "\f233";
    }

    .fa-servicestack:before {
      content: "\f3ec";
    }

    .fa-shapes:before {
      content: "\f61f";
    }

    .fa-share:before {
      content: "\f064";
    }

    .fa-share-alt:before {
      content: "\f1e0";
    }

    .fa-share-alt-square:before {
      content: "\f1e1";
    }

    .fa-share-square:before {
      content: "\f14d";
    }

    .fa-shekel-sign:before {
      content: "\f20b";
    }

    .fa-shield-alt:before {
      content: "\f3ed";
    }

    .fa-ship:before {
      content: "\f21a";
    }

    .fa-shipping-fast:before {
      content: "\f48b";
    }

    .fa-shirtsinbulk:before {
      content: "\f214";
    }

    .fa-shoe-prints:before {
      content: "\f54b";
    }

    .fa-shopping-bag:before {
      content: "\f290";
    }

    .fa-shopping-basket:before {
      content: "\f291";
    }

    .fa-shopping-cart:before {
      content: "\f07a";
    }

    .fa-shopware:before {
      content: "\f5b5";
    }

    .fa-shower:before {
      content: "\f2cc";
    }

    .fa-shuttle-van:before {
      content: "\f5b6";
    }

    .fa-sign:before {
      content: "\f4d9";
    }

    .fa-sign-in-alt:before {
      content: "\f2f6";
    }

    .fa-sign-language:before {
      content: "\f2a7";
    }

    .fa-sign-out-alt:before {
      content: "\f2f5";
    }

    .fa-signal:before {
      content: "\f012";
    }

    .fa-signature:before {
      content: "\f5b7";
    }

    .fa-sim-card:before {
      content: "\f7c4";
    }

    .fa-simplybuilt:before {
      content: "\f215";
    }

    .fa-sistrix:before {
      content: "\f3ee";
    }

    .fa-sitemap:before {
      content: "\f0e8";
    }

    .fa-sith:before {
      content: "\f512";
    }

    .fa-skating:before {
      content: "\f7c5";
    }

    .fa-sketch:before {
      content: "\f7c6";
    }

    .fa-skiing:before {
      content: "\f7c9";
    }

    .fa-skiing-nordic:before {
      content: "\f7ca";
    }

    .fa-skull:before {
      content: "\f54c";
    }

    .fa-skull-crossbones:before {
      content: "\f714";
    }

    .fa-skyatlas:before {
      content: "\f216";
    }

    .fa-skype:before {
      content: "\f17e";
    }

    .fa-slack:before {
      content: "\f198";
    }

    .fa-slack-hash:before {
      content: "\f3ef";
    }

    .fa-slash:before {
      content: "\f715";
    }

    .fa-sleigh:before {
      content: "\f7cc";
    }

    .fa-sliders-h:before {
      content: "\f1de";
    }

    .fa-slideshare:before {
      content: "\f1e7";
    }

    .fa-smile:before {
      content: "\f118";
    }

    .fa-smile-beam:before {
      content: "\f5b8";
    }

    .fa-smile-wink:before {
      content: "\f4da";
    }

    .fa-smog:before {
      content: "\f75f";
    }

    .fa-smoking:before {
      content: "\f48d";
    }

    .fa-smoking-ban:before {
      content: "\f54d";
    }

    .fa-sms:before {
      content: "\f7cd";
    }

    .fa-snapchat:before {
      content: "\f2ab";
    }

    .fa-snapchat-ghost:before {
      content: "\f2ac";
    }

    .fa-snapchat-square:before {
      content: "\f2ad";
    }

    .fa-snowboarding:before {
      content: "\f7ce";
    }

    .fa-snowflake:before {
      content: "\f2dc";
    }

    .fa-snowman:before {
      content: "\f7d0";
    }

    .fa-snowplow:before {
      content: "\f7d2";
    }

    .fa-socks:before {
      content: "\f696";
    }

    .fa-solar-panel:before {
      content: "\f5ba";
    }

    .fa-sort:before {
      content: "\f0dc";
    }

    .fa-sort-alpha-down:before {
      content: "\f15d";
    }

    .fa-sort-alpha-down-alt:before {
      content: "\f881";
    }

    .fa-sort-alpha-up:before {
      content: "\f15e";
    }

    .fa-sort-alpha-up-alt:before {
      content: "\f882";
    }

    .fa-sort-amount-down:before {
      content: "\f160";
    }

    .fa-sort-amount-down-alt:before {
      content: "\f884";
    }

    .fa-sort-amount-up:before {
      content: "\f161";
    }

    .fa-sort-amount-up-alt:before {
      content: "\f885";
    }

    .fa-sort-down:before {
      content: "\f0dd";
    }

    .fa-sort-numeric-down:before {
      content: "\f162";
    }

    .fa-sort-numeric-down-alt:before {
      content: "\f886";
    }

    .fa-sort-numeric-up:before {
      content: "\f163";
    }

    .fa-sort-numeric-up-alt:before {
      content: "\f887";
    }

    .fa-sort-up:before {
      content: "\f0de";
    }

    .fa-soundcloud:before {
      content: "\f1be";
    }

    .fa-sourcetree:before {
      content: "\f7d3";
    }

    .fa-spa:before {
      content: "\f5bb";
    }

    .fa-space-shuttle:before {
      content: "\f197";
    }

    .fa-speakap:before {
      content: "\f3f3";
    }

    .fa-speaker-deck:before {
      content: "\f83c";
    }

    .fa-spell-check:before {
      content: "\f891";
    }

    .fa-spider:before {
      content: "\f717";
    }

    .fa-spinner:before {
      content: "\f110";
    }

    .fa-splotch:before {
      content: "\f5bc";
    }

    .fa-spotify:before {
      content: "\f1bc";
    }

    .fa-spray-can:before {
      content: "\f5bd";
    }

    .fa-square:before {
      content: "\f0c8";
    }

    .fa-square-full:before {
      content: "\f45c";
    }

    .fa-square-root-alt:before {
      content: "\f698";
    }

    .fa-squarespace:before {
      content: "\f5be";
    }

    .fa-stack-exchange:before {
      content: "\f18d";
    }

    .fa-stack-overflow:before {
      content: "\f16c";
    }

    .fa-stackpath:before {
      content: "\f842";
    }

    .fa-stamp:before {
      content: "\f5bf";
    }

    .fa-star:before {
      content: "\f005";
    }

    .fa-star-and-crescent:before {
      content: "\f699";
    }

    .fa-star-half:before {
      content: "\f089";
    }

    .fa-star-half-alt:before {
      content: "\f5c0";
    }

    .fa-star-of-david:before {
      content: "\f69a";
    }

    .fa-star-of-life:before {
      content: "\f621";
    }

    .fa-staylinked:before {
      content: "\f3f5";
    }

    .fa-steam:before {
      content: "\f1b6";
    }

    .fa-steam-square:before {
      content: "\f1b7";
    }

    .fa-steam-symbol:before {
      content: "\f3f6";
    }

    .fa-step-backward:before {
      content: "\f048";
    }

    .fa-step-forward:before {
      content: "\f051";
    }

    .fa-stethoscope:before {
      content: "\f0f1";
    }

    .fa-sticker-mule:before {
      content: "\f3f7";
    }

    .fa-sticky-note:before {
      content: "\f249";
    }

    .fa-stop:before {
      content: "\f04d";
    }

    .fa-stop-circle:before {
      content: "\f28d";
    }

    .fa-stopwatch:before {
      content: "\f2f2";
    }

    .fa-store:before {
      content: "\f54e";
    }

    .fa-store-alt:before {
      content: "\f54f";
    }

    .fa-strava:before {
      content: "\f428";
    }

    .fa-stream:before {
      content: "\f550";
    }

    .fa-street-view:before {
      content: "\f21d";
    }

    .fa-strikethrough:before {
      content: "\f0cc";
    }

    .fa-stripe:before {
      content: "\f429";
    }

    .fa-stripe-s:before {
      content: "\f42a";
    }

    .fa-stroopwafel:before {
      content: "\f551";
    }

    .fa-studiovinari:before {
      content: "\f3f8";
    }

    .fa-stumbleupon:before {
      content: "\f1a4";
    }

    .fa-stumbleupon-circle:before {
      content: "\f1a3";
    }

    .fa-subscript:before {
      content: "\f12c";
    }

    .fa-subway:before {
      content: "\f239";
    }

    .fa-suitcase:before {
      content: "\f0f2";
    }

    .fa-suitcase-rolling:before {
      content: "\f5c1";
    }

    .fa-sun:before {
      content: "\f185";
    }

    .fa-superpowers:before {
      content: "\f2dd";
    }

    .fa-superscript:before {
      content: "\f12b";
    }

    .fa-supple:before {
      content: "\f3f9";
    }

    .fa-surprise:before {
      content: "\f5c2";
    }

    .fa-suse:before {
      content: "\f7d6";
    }

    .fa-swatchbook:before {
      content: "\f5c3";
    }

    .fa-swimmer:before {
      content: "\f5c4";
    }

    .fa-swimming-pool:before {
      content: "\f5c5";
    }

    .fa-symfony:before {
      content: "\f83d";
    }

    .fa-synagogue:before {
      content: "\f69b";
    }

    .fa-sync:before {
      content: "\f021";
    }

    .fa-sync-alt:before {
      content: "\f2f1";
    }

    .fa-syringe:before {
      content: "\f48e";
    }

    .fa-table:before {
      content: "\f0ce";
    }

    .fa-table-tennis:before {
      content: "\f45d";
    }

    .fa-tablet:before {
      content: "\f10a";
    }

    .fa-tablet-alt:before {
      content: "\f3fa";
    }

    .fa-tablets:before {
      content: "\f490";
    }

    .fa-tachometer-alt:before {
      content: "\f3fd";
    }

    .fa-tag:before {
      content: "\f02b";
    }

    .fa-tags:before {
      content: "\f02c";
    }

    .fa-tape:before {
      content: "\f4db";
    }

    .fa-tasks:before {
      content: "\f0ae";
    }

    .fa-taxi:before {
      content: "\f1ba";
    }

    .fa-teamspeak:before {
      content: "\f4f9";
    }

    .fa-teeth:before {
      content: "\f62e";
    }

    .fa-teeth-open:before {
      content: "\f62f";
    }

    .fa-telegram:before {
      content: "\f2c6";
    }

    .fa-telegram-plane:before {
      content: "\f3fe";
    }

    .fa-temperature-high:before {
      content: "\f769";
    }

    .fa-temperature-low:before {
      content: "\f76b";
    }

    .fa-tencent-weibo:before {
      content: "\f1d5";
    }

    .fa-tenge:before {
      content: "\f7d7";
    }

    .fa-terminal:before {
      content: "\f120";
    }

    .fa-text-height:before {
      content: "\f034";
    }

    .fa-text-width:before {
      content: "\f035";
    }

    .fa-th:before {
      content: "\f00a";
    }

    .fa-th-large:before {
      content: "\f009";
    }

    .fa-th-list:before {
      content: "\f00b";
    }

    .fa-the-red-yeti:before {
      content: "\f69d";
    }

    .fa-theater-masks:before {
      content: "\f630";
    }

    .fa-themeco:before {
      content: "\f5c6";
    }

    .fa-themeisle:before {
      content: "\f2b2";
    }

    .fa-thermometer:before {
      content: "\f491";
    }

    .fa-thermometer-empty:before {
      content: "\f2cb";
    }

    .fa-thermometer-full:before {
      content: "\f2c7";
    }

    .fa-thermometer-half:before {
      content: "\f2c9";
    }

    .fa-thermometer-quarter:before {
      content: "\f2ca";
    }

    .fa-thermometer-three-quarters:before {
      content: "\f2c8";
    }

    .fa-think-peaks:before {
      content: "\f731";
    }

    .fa-thumbs-down:before {
      content: "\f165";
    }

    .fa-thumbs-up:before {
      content: "\f164";
    }

    .fa-thumbtack:before {
      content: "\f08d";
    }

    .fa-ticket-alt:before {
      content: "\f3ff";
    }

    .fa-times:before {
      content: "\f00d";
    }

    .fa-times-circle:before {
      content: "\f057";
    }

    .fa-tint:before {
      content: "\f043";
    }

    .fa-tint-slash:before {
      content: "\f5c7";
    }

    .fa-tired:before {
      content: "\f5c8";
    }

    .fa-toggle-off:before {
      content: "\f204";
    }

    .fa-toggle-on:before {
      content: "\f205";
    }

    .fa-toilet:before {
      content: "\f7d8";
    }

    .fa-toilet-paper:before {
      content: "\f71e";
    }

    .fa-toolbox:before {
      content: "\f552";
    }

    .fa-tools:before {
      content: "\f7d9";
    }

    .fa-tooth:before {
      content: "\f5c9";
    }

    .fa-torah:before {
      content: "\f6a0";
    }

    .fa-torii-gate:before {
      content: "\f6a1";
    }

    .fa-tractor:before {
      content: "\f722";
    }

    .fa-trade-federation:before {
      content: "\f513";
    }

    .fa-trademark:before {
      content: "\f25c";
    }

    .fa-traffic-light:before {
      content: "\f637";
    }

    .fa-train:before {
      content: "\f238";
    }

    .fa-tram:before {
      content: "\f7da";
    }

    .fa-transgender:before {
      content: "\f224";
    }

    .fa-transgender-alt:before {
      content: "\f225";
    }

    .fa-trash:before {
      content: "\f1f8";
    }

    .fa-trash-alt:before {
      content: "\f2ed";
    }

    .fa-trash-restore:before {
      content: "\f829";
    }

    .fa-trash-restore-alt:before {
      content: "\f82a";
    }

    .fa-tree:before {
      content: "\f1bb";
    }

    .fa-trello:before {
      content: "\f181";
    }

    .fa-tripadvisor:before {
      content: "\f262";
    }

    .fa-trophy:before {
      content: "\f091";
    }

    .fa-truck:before {
      content: "\f0d1";
    }

    .fa-truck-loading:before {
      content: "\f4de";
    }

    .fa-truck-monster:before {
      content: "\f63b";
    }

    .fa-truck-moving:before {
      content: "\f4df";
    }

    .fa-truck-pickup:before {
      content: "\f63c";
    }

    .fa-tshirt:before {
      content: "\f553";
    }

    .fa-tty:before {
      content: "\f1e4";
    }

    .fa-tumblr:before {
      content: "\f173";
    }

    .fa-tumblr-square:before {
      content: "\f174";
    }

    .fa-tv:before {
      content: "\f26c";
    }

    .fa-twitch:before {
      content: "\f1e8";
    }

    .fa-twitter:before {
      content: "\f099";
    }

    .fa-twitter-square:before {
      content: "\f081";
    }

    .fa-typo3:before {
      content: "\f42b";
    }

    .fa-uber:before {
      content: "\f402";
    }

    .fa-ubuntu:before {
      content: "\f7df";
    }

    .fa-uikit:before {
      content: "\f403";
    }

    .fa-umbrella:before {
      content: "\f0e9";
    }

    .fa-umbrella-beach:before {
      content: "\f5ca";
    }

    .fa-underline:before {
      content: "\f0cd";
    }

    .fa-undo:before {
      content: "\f0e2";
    }

    .fa-undo-alt:before {
      content: "\f2ea";
    }

    .fa-uniregistry:before {
      content: "\f404";
    }

    .fa-universal-access:before {
      content: "\f29a";
    }

    .fa-university:before {
      content: "\f19c";
    }

    .fa-unlink:before {
      content: "\f127";
    }

    .fa-unlock:before {
      content: "\f09c";
    }

    .fa-unlock-alt:before {
      content: "\f13e";
    }

    .fa-untappd:before {
      content: "\f405";
    }

    .fa-upload:before {
      content: "\f093";
    }

    .fa-ups:before {
      content: "\f7e0";
    }

    .fa-usb:before {
      content: "\f287";
    }

    .fa-user:before {
      content: "\f007";
    }

    .fa-user-alt:before {
      content: "\f406";
    }

    .fa-user-alt-slash:before {
      content: "\f4fa";
    }

    .fa-user-astronaut:before {
      content: "\f4fb";
    }

    .fa-user-check:before {
      content: "\f4fc";
    }

    .fa-user-circle:before {
      content: "\f2bd";
    }

    .fa-user-clock:before {
      content: "\f4fd";
    }

    .fa-user-cog:before {
      content: "\f4fe";
    }

    .fa-user-edit:before {
      content: "\f4ff";
    }

    .fa-user-friends:before {
      content: "\f500";
    }

    .fa-user-graduate:before {
      content: "\f501";
    }

    .fa-user-injured:before {
      content: "\f728";
    }

    .fa-user-lock:before {
      content: "\f502";
    }

    .fa-user-md:before {
      content: "\f0f0";
    }

    .fa-user-minus:before {
      content: "\f503";
    }

    .fa-user-ninja:before {
      content: "\f504";
    }

    .fa-user-nurse:before {
      content: "\f82f";
    }

    .fa-user-plus:before {
      content: "\f234";
    }

    .fa-user-secret:before {
      content: "\f21b";
    }

    .fa-user-shield:before {
      content: "\f505";
    }

    .fa-user-slash:before {
      content: "\f506";
    }

    .fa-user-tag:before {
      content: "\f507";
    }

    .fa-user-tie:before {
      content: "\f508";
    }

    .fa-user-times:before {
      content: "\f235";
    }

    .fa-users:before {
      content: "\f0c0";
    }

    .fa-users-cog:before {
      content: "\f509";
    }

    .fa-usps:before {
      content: "\f7e1";
    }

    .fa-ussunnah:before {
      content: "\f407";
    }

    .fa-utensil-spoon:before {
      content: "\f2e5";
    }

    .fa-utensils:before {
      content: "\f2e7";
    }

    .fa-vaadin:before {
      content: "\f408";
    }

    .fa-vector-square:before {
      content: "\f5cb";
    }

    .fa-venus:before {
      content: "\f221";
    }

    .fa-venus-double:before {
      content: "\f226";
    }

    .fa-venus-mars:before {
      content: "\f228";
    }

    .fa-viacoin:before {
      content: "\f237";
    }

    .fa-viadeo:before {
      content: "\f2a9";
    }

    .fa-viadeo-square:before {
      content: "\f2aa";
    }

    .fa-vial:before {
      content: "\f492";
    }

    .fa-vials:before {
      content: "\f493";
    }

    .fa-viber:before {
      content: "\f409";
    }

    .fa-video:before {
      content: "\f03d";
    }

    .fa-video-slash:before {
      content: "\f4e2";
    }

    .fa-vihara:before {
      content: "\f6a7";
    }

    .fa-vimeo:before {
      content: "\f40a";
    }

    .fa-vimeo-square:before {
      content: "\f194";
    }

    .fa-vimeo-v:before {
      content: "\f27d";
    }

    .fa-vine:before {
      content: "\f1ca";
    }

    .fa-vk:before {
      content: "\f189";
    }

    .fa-vnv:before {
      content: "\f40b";
    }

    .fa-voicemail:before {
      content: "\f897";
    }

    .fa-volleyball-ball:before {
      content: "\f45f";
    }

    .fa-volume-down:before {
      content: "\f027";
    }

    .fa-volume-mute:before {
      content: "\f6a9";
    }

    .fa-volume-off:before {
      content: "\f026";
    }

    .fa-volume-up:before {
      content: "\f028";
    }

    .fa-vote-yea:before {
      content: "\f772";
    }

    .fa-vr-cardboard:before {
      content: "\f729";
    }

    .fa-vuejs:before {
      content: "\f41f";
    }

    .fa-walking:before {
      content: "\f554";
    }

    .fa-wallet:before {
      content: "\f555";
    }

    .fa-warehouse:before {
      content: "\f494";
    }

    .fa-water:before {
      content: "\f773";
    }

    .fa-wave-square:before {
      content: "\f83e";
    }

    .fa-waze:before {
      content: "\f83f";
    }

    .fa-weebly:before {
      content: "\f5cc";
    }

    .fa-weibo:before {
      content: "\f18a";
    }

    .fa-weight:before {
      content: "\f496";
    }

    .fa-weight-hanging:before {
      content: "\f5cd";
    }

    .fa-weixin:before {
      content: "\f1d7";
    }

    .fa-whatsapp:before {
      content: "\f232";
    }

    .fa-whatsapp-square:before {
      content: "\f40c";
    }

    .fa-wheelchair:before {
      content: "\f193";
    }

    .fa-whmcs:before {
      content: "\f40d";
    }

    .fa-wifi:before {
      content: "\f1eb";
    }

    .fa-wikipedia-w:before {
      content: "\f266";
    }

    .fa-wind:before {
      content: "\f72e";
    }

    .fa-window-close:before {
      content: "\f410";
    }

    .fa-window-maximize:before {
      content: "\f2d0";
    }

    .fa-window-minimize:before {
      content: "\f2d1";
    }

    .fa-window-restore:before {
      content: "\f2d2";
    }

    .fa-windows:before {
      content: "\f17a";
    }

    .fa-wine-bottle:before {
      content: "\f72f";
    }

    .fa-wine-glass:before {
      content: "\f4e3";
    }

    .fa-wine-glass-alt:before {
      content: "\f5ce";
    }

    .fa-wix:before {
      content: "\f5cf";
    }

    .fa-wizards-of-the-coast:before {
      content: "\f730";
    }

    .fa-wolf-pack-battalion:before {
      content: "\f514";
    }

    .fa-won-sign:before {
      content: "\f159";
    }

    .fa-wordpress:before {
      content: "\f19a";
    }

    .fa-wordpress-simple:before {
      content: "\f411";
    }

    .fa-wpbeginner:before {
      content: "\f297";
    }

    .fa-wpexplorer:before {
      content: "\f2de";
    }

    .fa-wpforms:before {
      content: "\f298";
    }

    .fa-wpressr:before {
      content: "\f3e4";
    }

    .fa-wrench:before {
      content: "\f0ad";
    }

    .fa-x-ray:before {
      content: "\f497";
    }

    .fa-xbox:before {
      content: "\f412";
    }

    .fa-xing:before {
      content: "\f168";
    }

    .fa-xing-square:before {
      content: "\f169";
    }

    .fa-y-combinator:before {
      content: "\f23b";
    }

    .fa-yahoo:before {
      content: "\f19e";
    }

    .fa-yammer:before {
      content: "\f840";
    }

    .fa-yandex:before {
      content: "\f413";
    }

    .fa-yandex-international:before {
      content: "\f414";
    }

    .fa-yarn:before {
      content: "\f7e3";
    }

    .fa-yelp:before {
      content: "\f1e9";
    }

    .fa-yen-sign:before {
      content: "\f157";
    }

    .fa-yin-yang:before {
      content: "\f6ad";
    }

    .fa-yoast:before {
      content: "\f2b1";
    }

    .fa-youtube:before {
      content: "\f167";
    }

    .fa-youtube-square:before {
      content: "\f431";
    }

    .fa-zhihu:before {
      content: "\f63f";
    }

    .sr-only {
      border: 0;
      clip: rect(0, 0, 0, 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px;
    }

    .sr-only-focusable:active,
    .sr-only-focusable:focus {
      clip: auto;
      height: auto;
      margin: 0;
      overflow: visible;
      position: static;
      width: auto;
    }

   
    .form-control-plaintext.form-control-sm,
    .form-control-plaintext.form-control-lg {
      padding-right: 0;
      padding-left: 0
    }

    .form-control-sm {
      height: calc(1.5em + .5rem + 2px);
      padding: .25rem .5rem;
      font-size: .875rem;
      line-height: 1.5;
      border-radius: .2rem
    }

    .form-control-lg {
      height: calc(1.5em + 1rem + 2px);
      padding: .5rem 1rem;
      font-size: 1.25rem;
      line-height: 1.5;
      border-radius: .3rem
    }

    select.form-control[size],
    select.form-control[multiple] {
      height: auto
    }

    textarea.form-control {
      height: auto
    }

    .form-group {
      margin-bottom: 1rem
    }

    .form-text {
      display: block;
      margin-top: .25rem
    }

    .form-row {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-wrap: wrap;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      margin-right: -5px;
      margin-left: -5px
    }

    .form-row>.col,
    .form-row>[class*="col-"] {
      padding-right: 5px;
      padding-left: 5px
    }

    .form-check {
      position: relative;
      display: block;
      padding-left: 1.25rem
    }

    .form-check-input {
      position: absolute;
      margin-top: .3rem;
      margin-left: -1.25rem
    }

    .form-check-input:disabled~.form-check-label {
      color: #6c757d
    }

    .form-check-label {
      margin-bottom: 0
    }

    .form-check-inline {
      display: -webkit-inline-box;
      display: -webkit-inline-flex;
      display: -ms-inline-flexbox;
      display: inline-flex;
      -webkit-box-align: center;
      -webkit-align-items: center;
      -ms-flex-align: center;
      align-items: center;
      padding-left: 0;
      margin-right: .75rem
    }

    .form-check-inline .form-check-input {
      position: static;
      margin-top: 0;
      margin-right: .3125rem;
      margin-left: 0
    }

    .valid-feedback {
      display: none;
      width: 100%;
      margin-top: .25rem;
      font-size: 80%;
      color: #28a745
    }

    .valid-tooltip {
      position: absolute;
      top: 100%;
      z-index: 5;
      display: none;
      max-width: 100%;
      padding: .25rem .5rem;
      margin-top: .1rem;
      font-size: .875rem;
      line-height: 1.5;
      color: #fff;
      background-color: rgba(40, 167, 69, 0.9);
      border-radius: .25rem
    }

    .was-validated .form-control:valid,
    .form-control.is-valid {
      border-color: #28a745;
      padding-right: calc(1.5em + .75rem);
      /* background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e"); */
      background-repeat: no-repeat;
      background-position: center right calc(.375em + .1875rem);
      background-size: calc(.75em + .375rem) calc(.75em + .375rem)
    }

    .was-validated .form-control:valid:focus,
    .form-control.is-valid:focus {
      border-color: #28a745;
      box-shadow: 0 0 0 .2rem rgba(40, 167, 69, 0.25)
    }

    .was-validated .form-control:valid~.valid-feedback,
    .was-validated .form-control:valid~.valid-tooltip,
    .form-control.is-valid~.valid-feedback,
    .form-control.is-valid~.valid-tooltip {
      display: block
    }

    .was-validated textarea.form-control:valid,
    textarea.form-control.is-valid {
      padding-right: calc(1.5em + .75rem);
      background-position: top calc(.375em + .1875rem) right calc(.375em + .1875rem)
    }

    .was-validated .custom-select:valid,
    .custom-select.is-valid {
      border-color: #28a745;
      padding-right: calc((1em + .75rem) * 3 / 4 + 1.75rem);
      background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right .75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") #fff no-repeat center right 1.75rem/calc(.75em + .375rem) calc(.75em + .375rem)
    }

    .was-validated .custom-select:valid:focus,
    .custom-select.is-valid:focus {
      border-color: #28a745;
      box-shadow: 0 0 0 .2rem rgba(40, 167, 69, 0.25)
    }

    .was-validated .custom-select:valid~.valid-feedback,
    .was-validated .custom-select:valid~.valid-tooltip,
    .custom-select.is-valid~.valid-feedback,
    .custom-select.is-valid~.valid-tooltip {
      display: block
    }

    .was-validated .form-control-file:valid~.valid-feedback,
    .was-validated .form-control-file:valid~.valid-tooltip,
    .form-control-file.is-valid~.valid-feedback,
    .form-control-file.is-valid~.valid-tooltip {
      display: block
    }

    .was-validated .form-check-input:valid~.form-check-label,
    .form-check-input.is-valid~.form-check-label {
      color: #28a745
    }

    .was-validated .form-check-input:valid~.valid-feedback,
    .was-validated .form-check-input:valid~.valid-tooltip,
    .form-check-input.is-valid~.valid-feedback,
    .form-check-input.is-valid~.valid-tooltip {
      display: block
    }

    .was-validated .custom-control-input:valid~.custom-control-label,
    .custom-control-input.is-valid~.custom-control-label {
      color: #28a745
    }

    .was-validated .custom-control-input:valid~.custom-control-label::before,
    .custom-control-input.is-valid~.custom-control-label::before {
      border-color: #28a745
    }

    .was-validated .custom-control-input:valid~.valid-feedback,
    .was-validated .custom-control-input:valid~.valid-tooltip,
    .custom-control-input.is-valid~.valid-feedback,
    .custom-control-input.is-valid~.valid-tooltip {
      display: block
    }

    .was-validated .custom-control-input:valid:checked~.custom-control-label::before,
    .custom-control-input.is-valid:checked~.custom-control-label::before {
      border-color: #34ce57;
      background-color: #34ce57
    }

    .was-validated .custom-control-input:valid:focus~.custom-control-label::before,
    .custom-control-input.is-valid:focus~.custom-control-label::before {
      box-shadow: 0 0 0 .2rem rgba(40, 167, 69, 0.25)
    }

    .was-validated .custom-control-input:valid:focus:not(:checked)~.custom-control-label::before,
    .custom-control-input.is-valid:focus:not(:checked)~.custom-control-label::before {
      border-color: #28a745
    }

    .was-validated .custom-file-input:valid~.custom-file-label,
    .custom-file-input.is-valid~.custom-file-label {
      border-color: #28a745
    }

    .was-validated .custom-file-input:valid~.valid-feedback,
    .was-validated .custom-file-input:valid~.valid-tooltip,
    .custom-file-input.is-valid~.valid-feedback,
    .custom-file-input.is-valid~.valid-tooltip {
      display: block
    }

    .was-validated .custom-file-input:valid:focus~.custom-file-label,
    .custom-file-input.is-valid:focus~.custom-file-label {
      border-color: #28a745;
      box-shadow: 0 0 0 .2rem rgba(40, 167, 69, 0.25)
    }

    .invalid-feedback {
      display: none;
      width: 100%;
      margin-top: .25rem;
      font-size: 80%;
      color: #dc3545
    }

    .invalid-tooltip {
      position: absolute;
      top: 100%;
      z-index: 5;
      display: none;
      max-width: 100%;
      padding: .25rem .5rem;
      margin-top: .1rem;
      font-size: .875rem;
      line-height: 1.5;
      color: #fff;
      background-color: rgba(220, 53, 69, 0.9);
      border-radius: .25rem
    }

    .was-validated .form-control:invalid,
    .form-control.is-invalid {
      border-color: #dc3545;
      padding-right: calc(1.5em + .75rem);
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E");
      background-repeat: no-repeat;
      background-position: center right calc(.375em + .1875rem);
      background-size: calc(.75em + .375rem) calc(.75em + .375rem)
    }

    .was-validated .form-control:invalid:focus,
    .form-control.is-invalid:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 .2rem rgba(220, 53, 69, 0.25)
    }

    .was-validated .form-control:invalid~.invalid-feedback,
    .was-validated .form-control:invalid~.invalid-tooltip,
    .form-control.is-invalid~.invalid-feedback,
    .form-control.is-invalid~.invalid-tooltip {
      display: block
    }

    .was-validated textarea.form-control:invalid,
    textarea.form-control.is-invalid {
      padding-right: calc(1.5em + .75rem);
      background-position: top calc(.375em + .1875rem) right calc(.375em + .1875rem)
    }

    .was-validated .custom-select:invalid,
    .custom-select.is-invalid {
      border-color: #dc3545;
      padding-right: calc((1em + .75rem) * 3 / 4 + 1.75rem);
      background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right .75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E") #fff no-repeat center right 1.75rem/calc(.75em + .375rem) calc(.75em + .375rem)
    }

    .was-validated .custom-select:invalid:focus,
    .custom-select.is-invalid:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 .2rem rgba(220, 53, 69, 0.25)
    }

    .was-validated .custom-select:invalid~.invalid-feedback,
    .was-validated .custom-select:invalid~.invalid-tooltip,
    .custom-select.is-invalid~.invalid-feedback,
    .custom-select.is-invalid~.invalid-tooltip {
      display: block
    }

    .was-validated .form-control-file:invalid~.invalid-feedback,
    .was-validated .form-control-file:invalid~.invalid-tooltip,
    .form-control-file.is-invalid~.invalid-feedback,
    .form-control-file.is-invalid~.invalid-tooltip {
      display: block
    }

    .was-validated .form-check-input:invalid~.form-check-label,
    .form-check-input.is-invalid~.form-check-label {
      color: #dc3545
    }

    .was-validated .form-check-input:invalid~.invalid-feedback,
    .was-validated .form-check-input:invalid~.invalid-tooltip,
    .form-check-input.is-invalid~.invalid-feedback,
    .form-check-input.is-invalid~.invalid-tooltip {
      display: block
    }

    .was-validated .custom-control-input:invalid~.custom-control-label,
    .custom-control-input.is-invalid~.custom-control-label {
      color: #dc3545
    }

    .was-validated .custom-control-input:invalid~.custom-control-label::before,
    .custom-control-input.is-invalid~.custom-control-label::before {
      border-color: #dc3545
    }

    .was-validated .custom-control-input:invalid~.invalid-feedback,
    .was-validated .custom-control-input:invalid~.invalid-tooltip,
    .custom-control-input.is-invalid~.invalid-feedback,
    .custom-control-input.is-invalid~.invalid-tooltip {
      display: block
    }

    .was-validated .custom-control-input:invalid:checked~.custom-control-label::before,
    .custom-control-input.is-invalid:checked~.custom-control-label::before {
      border-color: #e4606d;
      background-color: #e4606d
    }

    .was-validated .custom-control-input:invalid:focus~.custom-control-label::before,
    .custom-control-input.is-invalid:focus~.custom-control-label::before {
      box-shadow: 0 0 0 .2rem rgba(220, 53, 69, 0.25)
    }

    .was-validated .custom-control-input:invalid:focus:not(:checked)~.custom-control-label::before,
    .custom-control-input.is-invalid:focus:not(:checked)~.custom-control-label::before {
      border-color: #dc3545
    }

    .was-validated .custom-file-input:invalid~.custom-file-label,
    .custom-file-input.is-invalid~.custom-file-label {
      border-color: #dc3545
    }

    .was-validated .custom-file-input:invalid~.invalid-feedback,
    .was-validated .custom-file-input:invalid~.invalid-tooltip,
    .custom-file-input.is-invalid~.invalid-feedback,
    .custom-file-input.is-invalid~.invalid-tooltip {
      display: block
    }

    .was-validated .custom-file-input:invalid:focus~.custom-file-label,
    .custom-file-input.is-invalid:focus~.custom-file-label {
      border-color: #dc3545;
      box-shadow: 0 0 0 .2rem rgba(220, 53, 69, 0.25)
    }

    .form-inline {
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: horizontal;
      -webkit-box-direction: normal;
      -webkit-flex-flow: row wrap;
      -ms-flex-flow: row wrap;
      flex-flow: row wrap;
      -webkit-box-align: center;
      -webkit-align-items: center;
      -ms-flex-align: center;
      align-items: center
    }

    .form-inline .form-check {
      width: 100%
    }

    @media (min-width: 576px) {
      .form-inline label {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin-bottom: 0
      }

      .form-inline .form-group {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-flex: 0;
        -webkit-flex: 0 0 auto;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -webkit-flex-flow: row wrap;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        margin-bottom: 0
      }

      .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
      }

      .form-inline .form-control-plaintext {
        display: inline-block
      }

      .form-inline .input-group,
      .form-inline .custom-select {
        width: auto
      }

      .form-inline .form-check {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        width: auto;
        padding-left: 0
      }

      .form-inline .form-check-input {
        position: relative;
        -webkit-flex-shrink: 0;
        -ms-flex-negative: 0;
        flex-shrink: 0;
        margin-top: 0;
        margin-right: .25rem;
        margin-left: 0
      }

      .form-inline .custom-control {
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center
      }

      .form-inline .custom-control-label {
        margin-bottom: 0
      }
    }

    .btn {
      display: inline-block;
      font-weight: 400;
      color: #212529;
      text-align: center;
      vertical-align: middle;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      background-color: transparent;
      border: 1px solid transparent;
      padding: .375rem .75rem;
      font-size: 1rem;
      line-height: 1.5;
      border-radius: .25rem;
      -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out
    }

    @media (prefers-reduced-motion: reduce) {
      .btn {
        -webkit-transition: none;
        transition: none
      }
    }

    .btn:hover {
      color: #212529;
      text-decoration: none
    }

    .btn:focus,
    .btn.focus {
      outline: 0;
      box-shadow: 0 0 0 .2rem rgba(7, 112, 109, 0.25)
    }

    .btn.disabled,
    .btn:disabled {
      opacity: .65
    }

    a.btn.disabled,
    fieldset:disabled a.btn {
      pointer-events: none
    }

    .btn-primary {
      color: #fff;
      background-color: #07706d;
      border-color: #07706d
    }

    .btn-primary:hover {
      color: #fff;
      background-color: #054c4a;
      border-color: #04403e
    }

    .btn-primary:focus,
    .btn-primary.focus {
      box-shadow: 0 0 0 .2rem rgba(44, 133, 131, 0.5)
    }

    .btn-primary.disabled,
    .btn-primary:disabled {
      color: #fff;
      background-color: #07706d;
      border-color: #07706d
    }

    .btn-primary:not(:disabled):not(.disabled):active,
    .btn-primary:not(:disabled):not(.disabled).active,
    .show>.btn-primary.dropdown-toggle {
      color: #fff;
      background-color: #04403e;
      border-color: #033433
    }

    .btn-primary:not(:disabled):not(.disabled):active:focus,
    .btn-primary:not(:disabled):not(.disabled).active:focus,
    .show>.btn-primary.dropdown-toggle:focus {
      box-shadow: 0 0 0 .2rem rgba(44, 133, 131, 0.5)
    }

    .btn-secondary {
      color: #212529;
      background-color: #85bd48;
      border-color: #85bd48
    }

    .btn-secondary:hover {
      color: #fff;
      background-color: #72a43b;
      border-color: #6b9a38
    }

    .btn-secondary:focus,
    .btn-secondary.focus {
      box-shadow: 0 0 0 .2rem rgba(118, 166, 67, 0.5)
    }

    .btn-secondary.disabled,
    .btn-secondary:disabled {
      color: #212529;
      background-color: #85bd48;
      border-color: #85bd48
    }

    .btn-secondary:not(:disabled):not(.disabled):active,
    .btn-secondary:not(:disabled):not(.disabled).active,
    .show>.btn-secondary.dropdown-toggle {
      color: #fff;
      background-color: #6b9a38;
      border-color: #659134
    }

    .btn-secondary:not(:disabled):not(.disabled):active:focus,
    .btn-secondary:not(:disabled):not(.disabled).active:focus,
    .show>.btn-secondary.dropdown-toggle:focus {
      box-shadow: 0 0 0 .2rem rgba(118, 166, 67, 0.5)
    }

    .btn-success {
      color: #fff;
      background-color: #28a745;
      border-color: #28a745
    }

    .btn-success:hover {
      color: #fff;
      background-color: #218838;
      border-color: #1e7e34
    }

    .btn-success:focus,
    .btn-success.focus {
      box-shadow: 0 0 0 .2rem rgba(72, 180, 97, 0.5)
    }

    .btn-success.disabled,
    .btn-success:disabled {
      color: #fff;
      background-color: #28a745;
      border-color: #28a745
    }

    .btn-success:not(:disabled):not(.disabled):active,
    .btn-success:not(:disabled):not(.disabled).active,
    .show>.btn-success.dropdown-toggle {
      color: #fff;
      background-color: #1e7e34;
      border-color: #1c7430
    }

    .btn-success:not(:disabled):not(.disabled):active:focus,
    .btn-success:not(:disabled):not(.disabled).active:focus,
    .show>.btn-success.dropdown-toggle:focus {
      box-shadow: 0 0 0 .2rem rgba(72, 180, 97, 0.5)
    }

    .btn-info {
      color: #fff;
      background-color: #17a2b8;
      border-color: #17a2b8
    }

    .btn-info:hover {
      color: #fff;
      background-color: #138496;
      border-color: #117a8b
    }

    .btn-info:focus,
    .btn-info.focus {
      box-shadow: 0 0 0 .2rem rgba(58, 176, 195, 0.5)
    }

    .btn-info.disabled,
    .btn-info:disabled {
      color: #fff;
      background-color: #17a2b8;
      border-color: #17a2b8
    }

    .btn-info:not(:disabled):not(.disabled):active,
    .btn-info:not(:disabled):not(.disabled).active,
    .show>.btn-info.dropdown-toggle {
      color: #fff;
      background-color: #117a8b;
      border-color: #10707f
    }

    .btn-info:not(:disabled):not(.disabled):active:focus,
    .btn-info:not(:disabled):not(.disabled).active:focus,
    .show>.btn-info.dropdown-toggle:focus {
      box-shadow: 0 0 0 .2rem rgba(58, 176, 195, 0.5)
    }

    .btn-warning {
      color: #212529;
      background-color: #ffc107;
      border-color: #ffc107
    }

    .btn-warning:hover {
      color: #212529;
      background-color: #e0a800;
      border-color: #d39e00
    }

    .btn-warning:focus,
    .btn-warning.focus {
      box-shadow: 0 0 0 .2rem rgba(222, 170, 12, 0.5)
    }

    .btn-warning.disabled,
    .btn-warning:disabled {
      color: #212529;
      background-color: #ffc107;
      border-color: #ffc107
    }

    .btn-warning:not(:disabled):not(.disabled):active,
    .btn-warning:not(:disabled):not(.disabled).active,
    .show>.btn-warning.dropdown-toggle {
      color: #212529;
      background-color: #d39e00;
      border-color: #c69500
    }

    .btn-warning:not(:disabled):not(.disabled):active:focus,
    .btn-warning:not(:disabled):not(.disabled).active:focus,
    .show>.btn-warning.dropdown-toggle:focus {
      box-shadow: 0 0 0 .2rem rgba(222, 170, 12, 0.5)
    }

    .btn-danger {
      color: #fff;
      background-color: #dc3545;
      border-color: #dc3545
    }

    .btn-danger:hover {
      color: #fff;
      background-color: #c82333;
      border-color: #bd2130
    }

    .btn-danger:focus,
    .btn-danger.focus {
      box-shadow: 0 0 0 .2rem rgba(225, 83, 97, 0.5)
    }

    .btn-danger.disabled,
    .btn-danger:disabled {
      color: #fff;
      background-color: #dc3545;
      border-color: #dc3545
    }

    .btn-danger:not(:disabled):not(.disabled):active,
    .btn-danger:not(:disabled):not(.disabled).active,
    .show>.btn-danger.dropdown-toggle {
      color: #fff;
      background-color: #bd2130;
      border-color: #b21f2d
    }

    .btn-danger:not(:disabled):not(.disabled):active:focus,
    .btn-danger:not(:disabled):not(.disabled).active:focus,
    .show>.btn-danger.dropdown-toggle:focus {
      box-shadow: 0 0 0 .2rem rgba(225, 83, 97, 0.5)
    }

    .btn-light {
      color: #212529;
      background-color: #f8f9fa;
      border-color: #f8f9fa
    }

    .btn-light:hover {
      color: #212529;
      background-color: #e2e6ea;
      border-color: #dae0e5
    }

    .btn-light:focus,
    .btn-light.focus {
      box-shadow: 0 0 0 .2rem rgba(216, 217, 219, 0.5)
    }

    .btn-light.disabled,
    .btn-light:disabled {
      color: #212529;
      background-color: #f8f9fa;
      border-color: #f8f9fa
    }

    .btn-light:not(:disabled):not(.disabled):active,
    .btn-light:not(:disabled):not(.disabled).active,
    .show>.btn-light.dropdown-toggle {
      color: #212529;
      background-color: #dae0e5;
      border-color: #d3d9df
    }

    .btn-light:not(:disabled):not(.disabled):active:focus,
    .btn-light:not(:disabled):not(.disabled).active:focus,
    .show>.btn-light.dropdown-toggle:focus {
      box-shadow: 0 0 0 .2rem rgba(216, 217, 219, 0.5)
    }

    .btn-dark {
      color: #fff;
      background-color: #343a40;
      border-color: #343a40
    }

    .btn-dark:hover {
      color: #fff;
      background-color: #23272b;
      border-color: #1d2124
    }

    .btn-dark:focus,
    .btn-dark.focus {
      box-shadow: 0 0 0 .2rem rgba(82, 88, 93, 0.5)
    }

    .btn-dark.disabled,
    .btn-dark:disabled {
      color: #fff;
      background-color: #343a40;
      border-color: #343a40
    }

    .btn-dark:not(:disabled):not(.disabled):active,
    .btn-dark:not(:disabled):not(.disabled).active,
    .show>.btn-dark.dropdown-toggle {
      color: #fff;
      background-color: #1d2124;
      border-color: #171a1d
    }

    .btn-dark:not(:disabled):not(.disabled):active:focus,
    .btn-dark:not(:disabled):not(.disabled).active:focus,
    .show>.btn-dark.dropdown-toggle:focus {
      box-shadow: 0 0 0 .2rem rgba(82, 88, 93, 0.5)
    }

    .btn-outline-primary {
      color: #07706d;
      border-color: #07706d
    }

    .btn-outline-primary:hover {
      color: #fff;
      background-color: #07706d;
      border-color: #07706d
    }

    .btn-outline-primary:focus,
    .btn-outline-primary.focus {
      box-shadow: 0 0 0 .2rem rgba(7, 112, 109, 0.5)
    }

    .btn-outline-primary.disabled,
    .btn-outline-primary:disabled {
      color: #07706d;
      background-color: transparent
    }

    .btn-outline-primary:not(:disabled):not(.disabled):active,
    .btn-outline-primary:not(:disabled):not(.disabled).active,
    .show>.btn-outline-primary.dropdown-toggle {
      color: #fff;
      background-color: #07706d;
      border-color: #07706d
    }

    .btn-outline-primary:not(:disabled):not(.disabled):active:focus,
    .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
    .show>.btn-outline-primary.dropdown-toggle:focus {
      box-shadow: 0 0 0 .2rem rgba(7, 112, 109, 0.5)
    }

    .btn-outline-secondary {
      color: #85bd48;
      border-color: #85bd48
    }

    .btn-outline-secondary:hover {
      color: #212529;
      background-color: #85bd48;
      border-color: #85bd48
    }

    .btn-outline-secondary:focus,
    .btn-outline-secondary.focus {
      box-shadow: 0 0 0 .2rem rgba(133, 189, 72, 0.5)
    }

    .btn-outline-secondary.disabled,
    .btn-outline-secondary:disabled {
      color: #85bd48;
      background-color: transparent
    }

    .btn-outline-secondary:not(:disabled):not(.disabled):active,
    .btn-outline-secondary:not(:disabled):not(.disabled).active,
    .show>.btn-outline-secondary.dropdown-toggle {
      color: #212529;
      background-color: #85bd48;
      border-color: #85bd48
    }

    .btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
    .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
    .show>.btn-outline-secondary.dropdown-toggle:focus {
      box-shadow: 0 0 0 .2rem rgba(133, 189, 72, 0.5)
    }

    

    .header-left .dropdown-menu i.text-danger {
      color: #dc3545 !important;
    }

    .sticky .header-left a,
    .sticky .header-left i {
      color: #046F6D !important;
    }

    .modal-open .modal.modal-search {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      color: #fff;
      min-height: 333px;
      background-color: #0f1721;
      background-image: linear-gradient(220deg, #000 30.33%, #389492 83.58%, #046F6D 101.33%);
      max-width: 100%;
    }

    .modal-open .modal-search::before {
      content: "";
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      z-index: -2;
      background: url("data:null;base64,"), url("data:null;base64,");
      background-repeat: no-repeat, no-repeat;
      background-position: left top, right top;
    }

    .modal-open .modal-search::after {
      content: "";
      display: block;
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      flex-shrink: 0;
      background: rgba(255, 255, 255, 0.01);
      backdrop-filter: blur(6px);
      z-index: -1;
    }

    .modal-search .modal-dialog {
      width: 100%;
      max-width: 100%;
      margin: 0;
      height: 100vh;
      display: flex;
      align-items: center;
    }

    .modal-search .modal-content {
      width: 100%;
      max-width: 100%;
      margin: 0;
      height: 100vh;
      background: transparent;
      padding-top: 3rem;
    }

    .modal-search .modal-header {
      border: 0;
    }

    .modal-search .modal-header .close {
      text-shadow: none;
      font-weight: 400;
      opacity: 1;
      font-size: 1.25rem;
    }

    .modal-search .modal-header button.close:focus {
      outline: 0px auto -webkit-focus-ring-color;
      box-shadow: 0 0 0 0.2rem rgb(255 255 255 / 25%) !important;
      opacity: 1 !important;
      padding: 0.25rem !important;
      border-radius: 8px;
    }

    .modal-search .modal-body {
      width: 100%;
      max-width: 100%;
      margin: 0;
      height: 100vh;
      display: flex;
      align-items: flex-start;
      flex-flow: column;
      padding-top: 5rem;
    }

    .modal-search-title {
      text-align: center;
      line-height: 2;
    }

    .modal-search-title .slogan-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 3rem;
    }

    .modal-search .form-search {
      -webkit-box-flex: 1;
      -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
      flex-grow: 1;
      margin-bottom: 1rem;
    }

    .modal-search .form-item-search {
      display: flex;
      align-items: center;
    }

    .modal-search .form-item-search .btn {
      margin-inline-start: -7.75rem;
      margin-top: 0.25rem;
    }

    .modal-search .search-api-form.search {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      width: 100%;
    }

    .modal-search .form-control:focus {
      background: #ffffff;
      box-shadow: 0 0 0 .2rem rgba(7, 112, 109, 0.25) !important;
    }

    .modal-search .search-api-form.search .btn.btn-white {
      color: #046F6D;
      font-size: 15px;
      text-decoration: underline !important;
      border: 1.5px solid #07706D;
      margin-bottom: 1rem;
    }

    .modal-search .search-api-form.search .btn.btn-white:hover,
    .sticky .header-left .modal-search .search-api-form.search .btn.btn-white:hover,
    .modal-search .search-api-form.search .btn.btn-white:focus,
    .sticky .header-left .modal-search .search-api-form.search .btn.btn-white:focus {
      color: #ffffff !important;
      border: 1.5px solid #ffffff !important;
    }

    .modal-search .form-control:focus {
      background: #ffffff;
      box-shadow: 0 0 0 .32rem rgb(255 255 255 / 40%) !important;
    }

    .modal-search .modal-search-suggestions {
      background: #e9eaec;
      width: 100%;
      margin-top: 10rem;
      height: 100%;
    }

    .modal-search .card {
      padding: 1rem .5rem !important;
      border-radius: 10px !important;
      box-shadow: 0px 4px 4px 0px #0000000D !important;
      transition: background-color 0.5s ease;
    }

    .modal-search .card .card-image-container.icon {
      width: 4rem;
      height: 4rem;
    }

    .rounded-4 {
      border-radius: 1rem;
    }

    .modal-search .card .card-image-container img {
      display: block;
      height: 100%;
      width: 100%;
      object-fit: fill;
    }

    .modal-search .card .card-text {
      color: #043433;
      font-weight: 700;
      font-size: 0.92rem;
    }

    .modal-search .card.minimal:hover {
      background-color: #84BC47;
    }

    @media (max-width: 576px) {
      .modal-search .search-api-form.search {
        padding-right: 0rem !important;
        padding-left: 0rem !important;
      }
    }

    /*notifications*/
    #notifications-count {
      position: absolute;
      right: auto;
      left: 7px;
      top: 1px;
      border-radius: 100%;
      padding: 5px 4px;
    }

    .nav-item-notifications i {
      font-size: 1.25rem !important;
    }

    /*Web Accesability*/
    .webAccessability-ddl .accesability.dropdown-menu {
      min-width: 200px;
      left: 0;
      right: auto;
      text-align: center;
      box-shadow: 4px 3px 9px 0px #dbdbdbb5 !important;
    }

    [dir="ltr"] .webAccessability-ddl .accesability.dropdown-menu {
      left: auto;
      right: 0;
    }

    @media (max-width: 991.98px) {
      .webAccessability-ddl .accesability.dropdown-menu {
        left: 0;
        right: auto;
      }
    }

    .webAccessability-ddl .accesability.dropdown-menu~.arrow {
      display: none;
    }

    .webAccessability-ddl .accesability.dropdown-menu.show~.arrow {
      display: block;
      position: absolute;
      bottom: -4px;
      right: calc(50% - 6px);
      transform: rotate(180deg);
    }

    .webAccessability-ddl .accesability li {
      list-style: none;
      display: block;
      padding: 1rem;
      border-bottom: 1px solid #ddd;
    }

    .webAccessability-ddl .accesability li:last-child {
      border-bottom: 0px solid #ddd;
    }

    .webAccessability-ddl .accesability .font-size .font-size-content {
      display: block;
      position: relative;
      margin-top: 1rem;
      right: auto;
      width: auto;
    }

    .webAccessability-ddl .accesability .font-size .font-size-content button {
      background-color: #f1f1f1;
      font-size: 1rem;
      width: 2rem;
      margin: 0 0.25rem;
      border-radius: 0.25rem;
      padding: 0.25rem;
    }

    .webAccessability-ddl .accesability .font-size .font-size-content button:hover {
      background-color: #e5e5e5;
    }

    .webAccessability-ddl .accesability span.small {
      min-width: 90px;
      display: inline-block;
      text-align: start;
    }

    .webAccessability-ddl .accesability li .switcher label,
    .webAccessability-ddl .accesability li button.print-page {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 0 1rem;
    }

    .webAccessability-ddl .accesability.dropdown-menu li:hover {
      background: #84BC47 !important;
    }

    .webAccessability-ddl .accesability.dropdown-menu li:hover span.small {
      color: #fff !important;
    }

    /* switcher */
    div.switcher+div.switcher {
      margin-top: 10px;
    }

    div.switcher label {
      padding: 0;
    }

    div.switcher label * {
      vertical-align: middle;
    }

    div.switcher label input {
      display: none;
    }

    div.switcher label input+span {
      position: relative;
      display: inline-block;
      margin-right: 10px;
      width: 26px;
      height: 16px;
      background: #6c757d;
      border: 2px solid #6c757d;
      border-radius: 50px;
      transition: all 0.3s ease-in-out;
      cursor: pointer;
    }

    [dir="ltr"] div.switcher label input+span {
      margin-left: 10px;
      margin-right: 0;
    }

    div.switcher label input+span small,
    div.switcher label input+span .small {
      position: absolute;
      display: block;
      width: 50%;
      height: 100%;
      background: #fff;
      border-radius: 50%;
      transition: all 0.3s ease-in-out;
      left: 0;
    }

    div.switcher label input:checked+span {
      background: #d63384;
      border-color: #d63384;
    }

    div.switcher label input:checked+span small,
    div.switcher label input:checked+span .small {
      left: 50%;
    }

    .accesability {
      margin: 0;
      padding: 0;
    }

    .accesability li {
      list-style: none;
      display: inline-block;
    }

    .accesability li:hover {
      cursor: pointer;
    }

    .accesability .font-size {
      position: relative;
    }

    .accesability .font-size:hover .accesability-label {
      color: #84BC47;
    }

    .accesability .font-size:hover path {
      stroke: #84BC47;
    }

    .accesability .font-size .font-size-content {
      display: none;
      position: absolute;
      right: -39px;
      width: 102px;
    }

    .accesability .font-size .font-size-content button {
      border: 0;
      background: 0;
      background-color: #fff;
      font-size: 1rem;
    }

    .modal-body .accesability .font-size .font-size-content button {
      background-color: #ddd;
    }

    .accesability .font-size:hover .font-size-content {
      display: block;
    }

    .accesability button.print-page {
      background: 0;
      border: 0;
      padding: 0;
    }

    .accesability button.print-page:hover .accesability-label {
      color: #84BC47
    }

    .accesability button.print-page svg path {
      stroke: white !important;
    }

    .accesability button.print-page:hover svg path {
      stroke: #84BC47 !important;
    }

    .accesability .font-size .font-size-content button:hover,
    div#DeafServicePlace:hover {
      background-color: #68a12d !important;
      color: #fff !important;
    }

    /***************header-left End***************/
  </style>
  <style>
    @charset "UTF-8";
    @import url("data:text/css; charset=utf-8;base64,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");
    @import url("data:text/css; charset=utf-8;base64,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");

    html {
      --muneer-font-family: "Rubik", -apple-system, BlinkMacSystemFont, avenir next, avenir, helvetica neue, helvetica, ubuntu, roboto, noto, segoe ui, arial, sans-serif;
      --muneer-font-family-rtl: "Sourse Sans Pro", -apple-system, BlinkMacSystemFont, avenir next, avenir, helvetica neue, helvetica, ubuntu, roboto, noto, segoe ui, arial, sans-serif;
      --muneer-bg: #ffffff;
      --muneer-bg-dark: #16191b;
      --muneer-text: #333;
      --muneer-text-dark: #deeffd;
      --muneer-color: rgba(0, 108, 242, 1);
      --muneer-color-dark: rgba(0, 108, 242, 1);
      --muneer-color-accent: rgba(87, 206, 251, 1);
      --muneer-color-transparent: #EDF5F8;
      --muneer-color-transparent-dark: rgba(86, 106, 126, 0.1);
      --muneer-border-radius: 20px;
      --muneer-animate: 600ms;
      --muneer-overlay: rgba(2, 83, 238, .5);
      --muneer-btn-margin: 10px;
      --muneer-btn-padding: 20px;
      --muneer-btn-radius: 50px;
      --muneer-btn-color: #ffffff;
      --muneer-btn-color-hover: rgba(0, 108, 242, 1);
      --muneer-btn-bg: rgba(0, 108, 242, 1);
      --muneer-btn-bg-hover: #ffffff;
      --muneer-btn-size: 24px;
      --muneer-btn-delay: 0s;
      --muneer-keyboard-light-bg: #ececec;
      --muneer-keyboard-light-key-bg: #ffffff;
      --muneer-keyboard-light-key: #111111;
      --muneer-keyboard-dark-bg: #000000CC;
      --muneer-keyboard-dark-key-bg: rgba(0, 0, 0, .5);
      --muneer-keyboard-dark-key: #ffffff;
      --muneer-text-magnifier-bg-color: #333333;
      --muneer-text-magnifier-color: #ffffff;
      --muneer-text-magnifier-font-size: 36px;
      --muneer-highlight-titles-style: solid;
      --muneer-highlight-titles-color: #639af9;
      --muneer-highlight-titles-width: 2px;
      --muneer-highlight-titles-offset: 2px;
      --muneer-highlight-links-style: solid;
      --muneer-highlight-links-color: #639af9;
      --muneer-highlight-links-width: 2px;
      --muneer-highlight-links-offset: 2px;
      --muneer-reading-guide-width: 500px;
      --muneer-reading-guide-height: 17px;
      --muneer-reading-guide-radius: 10px;
      --muneer-reading-guide-bg: #0274e6;
      --muneer-reading-guide-border-width: 7px;
      --muneer-reading-guide-border-color: #000000;
      --muneer-reading-guide-arrow: 10px;
      --muneer-reading-guide-arrow-margin: -10px;
      --muneer-reading-mask: rgba(0, 0, 0, 0.7);
      --muneer-highlight-hover-style: solid;
      --muneer-highlight-hover-color: #639af9;
      --muneer-highlight-hover-width: 2px;
      --muneer-highlight-hover-offset: 2px;
      --muneer-highlight-focus-style: solid;
      --muneer-highlight-focus-color: #639af9;
      --muneer-highlight-focus-width: 2px;
      --muneer-highlight-focus-offset: 2px;
      --muneer-tts-bg: rgba(0, 108, 242, 1);
      --muneer-tts-color: rgba(255, 255, 255, 1)
    }

    #muneer-action-content-scaling .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,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");
      mask-image: url("data:image/svg+xml;base64,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")
    }

    #muneer-action-voice-navigation .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCI+PHBhdGggZmlsbD0iY3VycmVudENvbG9yIiBkPSJNMiAyMnYtMnExLjE1IDAgMi4yNzUtLjE1dDIuMi0uNTVxLTEuMTUtLjU3NS0xLjgxMi0xLjY2M1Q0IDE1LjI3NVYxM2g0di0zaDMuMzc1TDguMSAzLjQ1bDEuOC0uOSAzLjI3NSA2LjU1cS41IDEtLjA3NSAxLjk1dC0xLjcuOTVIMTB2MXEwIC44MjUtLjU4NyAxLjQxM1Q4IDE1SDZ2LjI3NXEwIC44NzUuNTM3IDEuNTM4VDcuOSAxNy43bC4zLjA3NXExIC4yNSAxLjEyNSAxLjI1dC0uNzc1IDEuNXEtMS41LjgyNS0zLjE2MyAxLjE1VDIgMjJabTE0LjMtMi44NS0xLjQyNS0xLjRxLjUyNS0uNTI1LjgyNS0xLjIxM3QuMy0xLjQ4N3EwLS44LS4zLTEuNDg4dC0uODI1LTEuMjEybDEuNDI1LTEuNDI1cS44LjggMS4yNSAxLjg2M1QxOCAxNS4wNXEwIDEuMi0uNDUgMi4yNXQtMS4yNSAxLjg1Wk0xOS4xMjUgMjIgMTcuNyAyMC41NzVxMS4wNzUtMS4wNzUgMS42ODgtMi40ODhUMjAgMTUuMDVxMC0xLjY1LS42MTMtMy4wNVQxNy43IDkuNTI1TDE5LjEyNSA4LjFxMS4zNSAxLjM1IDIuMTEzIDMuMTI1VDIyIDE1LjA1cTAgMi4wMjUtLjc2MyAzLjgxM1QxOS4xMjYgMjJaIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCI+PHBhdGggZmlsbD0iY3VycmVudENvbG9yIiBkPSJNMiAyMnYtMnExLjE1IDAgMi4yNzUtLjE1dDIuMi0uNTVxLTEuMTUtLjU3NS0xLjgxMi0xLjY2M1Q0IDE1LjI3NVYxM2g0di0zaDMuMzc1TDguMSAzLjQ1bDEuOC0uOSAzLjI3NSA2LjU1cS41IDEtLjA3NSAxLjk1dC0xLjcuOTVIMTB2MXEwIC44MjUtLjU4NyAxLjQxM1Q4IDE1SDZ2LjI3NXEwIC44NzUuNTM3IDEuNTM4VDcuOSAxNy43bC4zLjA3NXExIC4yNSAxLjEyNSAxLjI1dC0uNzc1IDEuNXEtMS41LjgyNS0zLjE2MyAxLjE1VDIgMjJabTE0LjMtMi44NS0xLjQyNS0xLjRxLjUyNS0uNTI1LjgyNS0xLjIxM3QuMy0xLjQ4N3EwLS44LS4zLTEuNDg4dC0uODI1LTEuMjEybDEuNDI1LTEuNDI1cS44LjggMS4yNSAxLjg2M1QxOCAxNS4wNXEwIDEuMi0uNDUgMi4yNXQtMS4yNSAxLjg1Wk0xOS4xMjUgMjIgMTcuNyAyMC41NzVxMS4wNzUtMS4wNzUgMS42ODgtMi40ODhUMjAgMTUuMDVxMC0xLjY1LS42MTMtMy4wNVQxNy43IDkuNTI1TDE5LjEyNSA4LjFxMS4zNSAxLjM1IDIuMTEzIDMuMTI1VDIyIDE1LjA1cTAgMi4wMjUtLjc2MyAzLjgxM1QxOS4xMjYgMjJaIi8+PC9zdmc+")
    }

    #muneer-action-text-magnifier .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTI3LjM0IDI2LjE2OC4wMDYuMDA2YS44MzQuODM0IDAgMCAxIC4xOTIuOTE1bC40NjMuMTktLjQ2My0uMTlhLjgzMi44MzIgMCAwIDEtMS4xLjQ1bC0uMTk4LjQ1OS4xOTctLjQ2YS44MzMuODMzIDAgMCAxLS4yNy0uMTg2bC0uMDA2LS4wMDctNC43NzQtNC43NzMtLjMxMS0uMzEyLS4zNDkuMjdhMTAuMTY2IDEwLjE2NiAwIDEgMSAxLjc5Ny0xLjc5N2wtLjI3LjM0OS4zMTIuMzExLjAwMS4wMDIgNC43NzMgNC43NzNaTTE1LjMyNyAxMy4xNjd2LjVoMy4xNjdhLjgzMy44MzMgMCAxIDEgMCAxLjY2NmgtMy4xNjdWMTguNWEuODMzLjgzMyAwIDEgMS0xLjY2NiAwdi0zLjE2N2gtMy4xNjdhLjgzMy44MzMgMCAwIDEgMC0xLjY2NmgzLjE2N1YxMC41YS44MzMuODMzIDAgMCAxIDEuNjY2IDB2Mi42NjdaTTE0LjQ5NCAyM2E4LjUwMiA4LjUwMiAwIDAgMCA2LjAxLTE0LjUxQTguNSA4LjUgMCAxIDAgMTQuNDk0IDIzWiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTI3LjM0IDI2LjE2OC4wMDYuMDA2YS44MzQuODM0IDAgMCAxIC4xOTIuOTE1bC40NjMuMTktLjQ2My0uMTlhLjgzMi44MzIgMCAwIDEtMS4xLjQ1bC0uMTk4LjQ1OS4xOTctLjQ2YS44MzMuODMzIDAgMCAxLS4yNy0uMTg2bC0uMDA2LS4wMDctNC43NzQtNC43NzMtLjMxMS0uMzEyLS4zNDkuMjdhMTAuMTY2IDEwLjE2NiAwIDEgMSAxLjc5Ny0xLjc5N2wtLjI3LjM0OS4zMTIuMzExLjAwMS4wMDIgNC43NzMgNC43NzNaTTE1LjMyNyAxMy4xNjd2LjVoMy4xNjdhLjgzMy44MzMgMCAxIDEgMCAxLjY2NmgtMy4xNjdWMTguNWEuODMzLjgzMyAwIDEgMS0xLjY2NiAwdi0zLjE2N2gtMy4xNjdhLjgzMy44MzMgMCAwIDEgMC0xLjY2NmgzLjE2N1YxMC41YS44MzMuODMzIDAgMCAxIDEuNjY2IDB2Mi42NjdaTTE0LjQ5NCAyM2E4LjUwMiA4LjUwMiAwIDAgMCA2LjAxLTE0LjUxQTguNSA4LjUgMCAxIDAgMTQuNDk0IDIzWiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+")
    }

    #muneer-action-readable-font .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE4LjE0OCAyNCAxMS40ODIgOCA0LjgxNSAyNG0xMC42NjYtNS4zMzNoLThNMjguODE1IDI0di00bTAgMHYtNG0wIDRhNCA0IDAgMSAxLTggMCA0IDQgMCAwIDEgOCAwWiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE4LjE0OCAyNCAxMS40ODIgOCA0LjgxNSAyNG0xMC42NjYtNS4zMzNoLThNMjguODE1IDI0di00bTAgMHYtNG0wIDRhNCA0IDAgMSAxLTggMCA0IDQgMCAwIDEgOCAwWiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==")
    }

    #muneer-action-dyslexia-font .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTQuODE1IDIzIDUuNzYtMTQuNThoMS44NkwxOC4xNzUgMjNoLTIuODJsLS41Ni0yLjM4aC02LjU2TDcuNDk1IDIzaC0yLjY4Wm05LjM4LTQuNy0yLjctOC4wOC0yLjY4IDguMDhoNS4zOFpNMjguMzQ0IDE2Ljc2VjIzaC0xLjh2LTEuNTZjLS44MiAxLjItMiAxLjg0LTMuNzggMS44NC0yLjIyIDAtMy42Ni0xLjM2LTMuNjYtMy40NiAwLTIuNDYgMS42Ni0zLjcyIDQuOTItMy43MmgyLjUydi0uMThjMC0xLjY2LTEtMi42Ni0zLjEtMi44LTEuMjQgMC0yLjQ0LjMtMy41NC45di0xLjQ2YzEuMzItLjUgMi41Ni0uNzYgMy43Mi0uNzYgMy4xOCAwIDQuNzIgMS42MiA0LjcyIDQuOTZabS0yLjguNjZoLTEuNjJjLTIuNjIgMC0zLjMuNTYtMy4zMiAxLjkuMiAxLjEyIDEuMTQgMS4yMiAyLjQ0IDEuMjIgMS44IDAgMi41LS4zOCAyLjUtMi43MnYtLjRaIiBmaWxsPSIjMDA2Q0YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTQuODE1IDIzIDUuNzYtMTQuNThoMS44NkwxOC4xNzUgMjNoLTIuODJsLS41Ni0yLjM4aC02LjU2TDcuNDk1IDIzaC0yLjY4Wm05LjM4LTQuNy0yLjctOC4wOC0yLjY4IDguMDhoNS4zOFpNMjguMzQ0IDE2Ljc2VjIzaC0xLjh2LTEuNTZjLS44MiAxLjItMiAxLjg0LTMuNzggMS44NC0yLjIyIDAtMy42Ni0xLjM2LTMuNjYtMy40NiAwLTIuNDYgMS42Ni0zLjcyIDQuOTItMy43MmgyLjUydi0uMThjMC0xLjY2LTEtMi42Ni0zLjEtMi44LTEuMjQgMC0yLjQ0LjMtMy41NC45di0xLjQ2YzEuMzItLjUgMi41Ni0uNzYgMy43Mi0uNzYgMy4xOCAwIDQuNzIgMS42MiA0LjcyIDQuOTZabS0yLjguNjZoLTEuNjJjLTIuNjIgMC0zLjMuNTYtMy4zMiAxLjkuMiAxLjEyIDEuMTQgMS4yMiAyLjQ0IDEuMjIgMS44IDAgMi41LS4zOCAyLjUtMi43MnYtLjRaIiBmaWxsPSIjMDA2Q0YyIi8+PC9zdmc+")
    }

    #muneer-action-highlight-titles .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI2LjgxNSA0aC0yMGEyIDIgMCAwIDAtMiAydjIwYTIgMiAwIDAgMCAyIDJoMjBhMiAyIDAgMCAwIDItMlY2YTIgMiAwIDAgMC0yLTJaIiBzdHJva2U9IiMwMDZDRjIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjxwYXRoIGQ9Ik0xMS40ODIgMTIuNjY3di0yaDEwLjY2NnYybS02LjY2NiAxMGgyLjY2Nk0xNi44MTUgMTJ2MTAuNjY3IiBzdHJva2U9IiMwMDZDRjIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI2LjgxNSA0aC0yMGEyIDIgMCAwIDAtMiAydjIwYTIgMiAwIDAgMCAyIDJoMjBhMiAyIDAgMCAwIDItMlY2YTIgMiAwIDAgMC0yLTJaIiBzdHJva2U9IiMwMDZDRjIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjxwYXRoIGQ9Ik0xMS40ODIgMTIuNjY3di0yaDEwLjY2NnYybS02LjY2NiAxMGgyLjY2Nk0xNi44MTUgMTJ2MTAuNjY3IiBzdHJva2U9IiMwMDZDRjIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+")
    }

    #muneer-action-highlight-links .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTEyLjQ5NCAyMCA4LThNMTUuMTYgOGwuNjE3LS43MTVhNi42NjcgNi42NjcgMCAwIDEgOS40MjggOS40M2wtLjcxMi42MThNMTcuODI3IDI0bC0uNTMuNzEyYTYuNzU3IDYuNzU3IDAgMCAxLTkuNTAyIDAgNi42MjkgNi42MjkgMCAwIDEgMC05LjQyOGwuNjk5LS42MTciIHN0cm9rZT0iIzAwNkNGMiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTEyLjQ5NCAyMCA4LThNMTUuMTYgOGwuNjE3LS43MTVhNi42NjcgNi42NjcgMCAwIDEgOS40MjggOS40M2wtLjcxMi42MThNMTcuODI3IDI0bC0uNTMuNzEyYTYuNzU3IDYuNzU3IDAgMCAxLTkuNTAyIDAgNi42MjkgNi42MjkgMCAwIDEgMC05LjQyOGwuNjk5LS42MTciIHN0cm9rZT0iIzAwNkNGMiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48L3N2Zz4=")
    }

    #muneer-action-font-sizing .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjUiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE4LjAwNiAyMVYxMW0wIDEwLTItMi41bTIgMi41IDItMi41bS0yLTcuNS0yIDJtMi0yIDIgMm0tMTEtOHYxMm0wIDBoLTJtMiAwaDJtNC0xMFY1aC0xMnYyIiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjUiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE4LjAwNiAyMVYxMW0wIDEwLTItMi41bTIgMi41IDItMi41bS0yLTcuNS0yIDJtMi0yIDIgMm0tMTEtOHYxMm0wIDBoLTJtMiAwaDJtNC0xMFY1aC0xMnYyIiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48L3N2Zz4=")
    }

    #muneer-action-line-height .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjUiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTMuMDA2IDggMy0zbTAgMCAzIDNtLTMtM3YxNG0tMy0zIDMgM20wIDAgMy0zbTQtMTBoN20tNyA2aDdtLTcgNmg3IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjUiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTMuMDA2IDggMy0zbTAgMCAzIDNtLTMtM3YxNG0tMy0zIDMgM20wIDAgMy0zbTQtMTBoN20tNyA2aDdtLTcgNmg3IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+")
    }

    #muneer-action-letter-spacing .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTIzLjI0MSAxNS41NDggNS4yMy0xMi4zNjd2LS4wMDJhLjY0My42NDMgMCAwIDEgMS4xODQuNTAzTDIzLjM3IDE4LjUzNmwtLjAxNy4wNGEuNjQyLjY0MiAwIDAgMS0xLjE2Ni0uMDRMMTUuOTAyIDMuNjhhLjY0NC42NDQgMCAwIDEgLjgzMy0uODQ2LjY0My42NDMgMCAwIDEgLjM1LjM0NWguMDAxbDUuMjM0IDEyLjM3LjQ2IDEuMDg5LjQ2MS0xLjA5Wm0tNi4xNTUgMi40ODdhLjY0My42NDMgMCAwIDEtMS4xODQuNTAybC0xLjYzOC0zLjg3MS0uMTMtLjMwNUg2LjI4MmwtLjEyOS4zMDUtMS42MzcgMy44N2EuNjQ0LjY0NCAwIDAgMS0xLjE4NS0uNTAxTDkuNjE2IDMuMTc4YS42NDMuNjQzIDAgMCAxIC41OTItLjM5MmguMDAxYS42NDMuNjQzIDAgMCAxIC41OTEuMzkybDYuMjg2IDE0Ljg1N1ptLTQuMjQ4LTQuOTZoLjc1NGwtLjI5NC0uNjk1LTIuNjMtNi4yMTMtLjQ2LTEuMDg4LS40NiAxLjA4OC0yLjYyOSA2LjIxMy0uMjk0LjY5NWg2LjAxM1ptMTIuMzA1IDkuMzdhLjY0My42NDMgMCAwIDEgLjkwNi0uMDgybDMuNDI5IDIuODU4YS42NDMuNjQzIDAgMCAxIDAgLjk4OGwtMy40MjkgMi44NTdhLjY0My42NDMgMCAwIDEtLjgyMy0uOTg4bDEuMDAzLS44MzcgMS4wNTktLjg4NEg1LjY5OWwxLjA2Ljg4NCAxLjAwMy44MzdhLjY0My42NDMgMCAwIDEtLjgyMi45ODhMMy41MSAyNi4yMWEuNjQzLjY0MyAwIDAgMSAwLS45ODhsMy40MjktMi44NTdhLjY0My42NDMgMCAwIDEgLjgyMi45ODhsLTEuMDAzLjgzNS0xLjA2Mi44ODRoMjEuNTkybC0xLjA2LS44ODQtMS4wMDMtLjgzNmEuNjQzLjY0MyAwIDAgMS0uMDgzLS45MDVaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTIzLjI0MSAxNS41NDggNS4yMy0xMi4zNjd2LS4wMDJhLjY0My42NDMgMCAwIDEgMS4xODQuNTAzTDIzLjM3IDE4LjUzNmwtLjAxNy4wNGEuNjQyLjY0MiAwIDAgMS0xLjE2Ni0uMDRMMTUuOTAyIDMuNjhhLjY0NC42NDQgMCAwIDEgLjgzMy0uODQ2LjY0My42NDMgMCAwIDEgLjM1LjM0NWguMDAxbDUuMjM0IDEyLjM3LjQ2IDEuMDg5LjQ2MS0xLjA5Wm0tNi4xNTUgMi40ODdhLjY0My42NDMgMCAwIDEtMS4xODQuNTAybC0xLjYzOC0zLjg3MS0uMTMtLjMwNUg2LjI4MmwtLjEyOS4zMDUtMS42MzcgMy44N2EuNjQ0LjY0NCAwIDAgMS0xLjE4NS0uNTAxTDkuNjE2IDMuMTc4YS42NDMuNjQzIDAgMCAxIC41OTItLjM5MmguMDAxYS42NDMuNjQzIDAgMCAxIC41OTEuMzkybDYuMjg2IDE0Ljg1N1ptLTQuMjQ4LTQuOTZoLjc1NGwtLjI5NC0uNjk1LTIuNjMtNi4yMTMtLjQ2LTEuMDg4LS40NiAxLjA4OC0yLjYyOSA2LjIxMy0uMjk0LjY5NWg2LjAxM1ptMTIuMzA1IDkuMzdhLjY0My42NDMgMCAwIDEgLjkwNi0uMDgybDMuNDI5IDIuODU4YS42NDMuNjQzIDAgMCAxIDAgLjk4OGwtMy40MjkgMi44NTdhLjY0My42NDMgMCAwIDEtLjgyMy0uOTg4bDEuMDAzLS44MzcgMS4wNTktLjg4NEg1LjY5OWwxLjA2Ljg4NCAxLjAwMy44MzdhLjY0My42NDMgMCAwIDEtLjgyMi45ODhMMy41MSAyNi4yMWEuNjQzLjY0MyAwIDAgMSAwLS45ODhsMy40MjktMi44NTdhLjY0My42NDMgMCAwIDEgLjgyMi45ODhsLTEuMDAzLjgzNS0xLjA2Mi44ODRoMjEuNTkybC0xLjA2LS44ODQtMS4wMDMtLjgzNmEuNjQzLjY0MyAwIDAgMS0uMDgzLS45MDVaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiLz48L3N2Zz4=")
    }

    #muneer-action-word-spacing .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMyAzMiI+PHBhdGggZD0ibTIzLjIgMTUuNSA1LjItMTIuNGMwLS4yLjItLjMuNC0uM2guNWMuMiAwIC4zLjIuMy4zdi41bC02LjMgMTQuOWMwIC4xLS4xLjItLjIuMy0uMSAwLS4yLjEtLjQuMXMtLjIgMC0uNC0uMWMtLjEgMC0uMi0uMi0uMi0uM0wxNS45IDMuN3YtLjRjMC0uMi4yLS4zLjMtLjRoLjVjLjIgMCAuMy4yLjQuM2w1LjIgMTIuNC41IDEuMS41LTEuMVptMS45IDYuOS4yLS4yaC40cy4yIDAgLjIuMSAzLjQgMi45IDMuNCAyLjlsLjIuMnYuNnMwIC4yLS4yLjItMy40IDIuOS0zLjQgMi45Yy0uMS4xLS4zLjItLjUuMS0uMiAwLS4zIDAtLjQtLjItLjEtLjEtLjItLjMtLjEtLjUgMC0uMiAwLS4zLjItLjRzMS0uOCAxLS44bDEuMS0uOUg1LjdsMS4xLjkgMSAuOC4yLjJ2LjRzMCAuMi0uMS4ybC0uMi4yaC0uNHMtLjIgMC0uMi0uMWwtMy40LTIuOS0uMi0uMnYtLjZzMC0uMi4yLS4yIDMuNC0yLjkgMy40LTIuOS4xIDAgLjItLjFoLjRzLjEgMCAuMi4yYzAgMCAwIC4xLjEuMnYuNHMwIC4xLS4yLjJsLTEgLjgtMS4xLjloMjEuNmwtMS4xLS45LTEtLjhjLS4xLS4xLS4yLS4zLS4yLS40IDAtLjIgMC0uMy4xLS41Wk0xMy4yIDguOGMuOC43IDEuMiAxLjggMS4yIDMuMnY1LjRjMCAuOC0uNiAxLjQtMS40IDEuNHYtMS43Yy0uMy42LS44IDEtMS41IDEuMy0uNy4zLTEuNC41LTIuMy41UzcgMTguNiA2LjMgMThjLS43LS42LTEuMS0xLjQtMS4xLTIuM3MuMy0xLjcgMS0yLjNjLjctLjYgMS44LS45IDMuMi0uOWgzLjV2LS43YzAtMS0uMy0xLjctLjgtMi4yLS41LS41LTEuMy0uNy0yLjMtLjdzLTEuNC4xLTIgLjMtMS4yLjUtMS43IDFsLS43LTEuMWMuNi0uNSAxLjItLjggMi0xLjEuOC0uMyAxLjYtLjQgMi41LS40IDEuNCAwIDIuNS40IDMuMyAxLjFabS0xLjcgOC40Yy42LS40IDEtLjkgMS4zLTEuN3YtMS44SDkuM2MtMS45IDAtMi44LjctMi44IDJzLjIgMS4yLjcgMS41IDEuMi42IDIuMS42IDEuNi0uMiAyLjItLjZaIiBzdHlsZT0iZmlsbDojMDA2Y2YyO3N0cm9rZTojMDA2Y2YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMyAzMiI+PHBhdGggZD0ibTIzLjIgMTUuNSA1LjItMTIuNGMwLS4yLjItLjMuNC0uM2guNWMuMiAwIC4zLjIuMy4zdi41bC02LjMgMTQuOWMwIC4xLS4xLjItLjIuMy0uMSAwLS4yLjEtLjQuMXMtLjIgMC0uNC0uMWMtLjEgMC0uMi0uMi0uMi0uM0wxNS45IDMuN3YtLjRjMC0uMi4yLS4zLjMtLjRoLjVjLjIgMCAuMy4yLjQuM2w1LjIgMTIuNC41IDEuMS41LTEuMVptMS45IDYuOS4yLS4yaC40cy4yIDAgLjIuMSAzLjQgMi45IDMuNCAyLjlsLjIuMnYuNnMwIC4yLS4yLjItMy40IDIuOS0zLjQgMi45Yy0uMS4xLS4zLjItLjUuMS0uMiAwLS4zIDAtLjQtLjItLjEtLjEtLjItLjMtLjEtLjUgMC0uMiAwLS4zLjItLjRzMS0uOCAxLS44bDEuMS0uOUg1LjdsMS4xLjkgMSAuOC4yLjJ2LjRzMCAuMi0uMS4ybC0uMi4yaC0uNHMtLjIgMC0uMi0uMWwtMy40LTIuOS0uMi0uMnYtLjZzMC0uMi4yLS4yIDMuNC0yLjkgMy40LTIuOS4xIDAgLjItLjFoLjRzLjEgMCAuMi4yYzAgMCAwIC4xLjEuMnYuNHMwIC4xLS4yLjJsLTEgLjgtMS4xLjloMjEuNmwtMS4xLS45LTEtLjhjLS4xLS4xLS4yLS4zLS4yLS40IDAtLjIgMC0uMy4xLS41Wk0xMy4yIDguOGMuOC43IDEuMiAxLjggMS4yIDMuMnY1LjRjMCAuOC0uNiAxLjQtMS40IDEuNHYtMS43Yy0uMy42LS44IDEtMS41IDEuMy0uNy4zLTEuNC41LTIuMy41UzcgMTguNiA2LjMgMThjLS43LS42LTEuMS0xLjQtMS4xLTIuM3MuMy0xLjcgMS0yLjNjLjctLjYgMS44LS45IDMuMi0uOWgzLjV2LS43YzAtMS0uMy0xLjctLjgtMi4yLS41LS41LTEuMy0uNy0yLjMtLjdzLTEuNC4xLTIgLjMtMS4yLjUtMS43IDFsLS43LTEuMWMuNi0uNSAxLjItLjggMi0xLjEuOC0uMyAxLjYtLjQgMi41LS40IDEuNCAwIDIuNS40IDMuMyAxLjFabS0xLjcgOC40Yy42LS40IDEtLjkgMS4zLTEuN3YtMS44SDkuM2MtMS45IDAtMi44LjctMi44IDJzLjIgMS4yLjcgMS41IDEuMi42IDIuMS42IDEuNi0uMiAyLjItLjZaIiBzdHlsZT0iZmlsbDojMDA2Y2YyO3N0cm9rZTojMDA2Y2YyIi8+PC9zdmc+")
    }

    #muneer-action-text-alignment .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMyAzMiI+PHBhdGggZD0iTTE4LjggOC44aC0xNGMtLjIgMC0uNCAwLS42LS4yUzQgOC4yIDQgOHMwLS40LjItLjYuNC0uMi42LS4yaDE0Yy4yIDAgLjQgMCAuNi4ycy4yLjQuMi42IDAgLjQtLjIuNmMtLjIuMi0uNC4yLS42LjJabTAgNS40aC0xNGMtLjIgMC0uNCAwLS42LS4ycy0uMi0uNC0uMi0uNiAwLS40LjItLjZjLjItLjIuNC0uMi42LS4yaDE0Yy4yIDAgLjQgMCAuNi4ycy4yLjQuMi42IDAgLjQtLjIuNmMtLjIuMi0uNC4yLS42LjJabTEwIDEwLjZoLTE0Yy0uMiAwLS40IDAtLjYtLjJzLS4yLS40LS4yLS42IDAtLjQuMi0uNi40LS4yLjYtLjJoMTRjLjIgMCAuNCAwIC42LjJzLjIuNC4yLjYgMCAuNC0uMi42LS40LjItLjYuMlptLTE0LTdoMTRjLjIgMCAuNCAwIC42LjJzLjIuNC4yLjYgMCAuNC0uMi42LS40LjItLjYuMmgtMTRjLS4yIDAtLjQgMC0uNi0uMnMtLjItLjQtLjItLjYgMC0uNC4yLS42LjQtLjIuNi0uMloiIHN0eWxlPSJmaWxsOiMwMDZjZjI7c3Ryb2tlOiMwMDZjZjIiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMyAzMiI+PHBhdGggZD0iTTE4LjggOC44aC0xNGMtLjIgMC0uNCAwLS42LS4yUzQgOC4yIDQgOHMwLS40LjItLjYuNC0uMi42LS4yaDE0Yy4yIDAgLjQgMCAuNi4ycy4yLjQuMi42IDAgLjQtLjIuNmMtLjIuMi0uNC4yLS42LjJabTAgNS40aC0xNGMtLjIgMC0uNCAwLS42LS4ycy0uMi0uNC0uMi0uNiAwLS40LjItLjZjLjItLjIuNC0uMi42LS4yaDE0Yy4yIDAgLjQgMCAuNi4ycy4yLjQuMi42IDAgLjQtLjIuNmMtLjIuMi0uNC4yLS42LjJabTEwIDEwLjZoLTE0Yy0uMiAwLS40IDAtLjYtLjJzLS4yLS40LS4yLS42IDAtLjQuMi0uNi40LS4yLjYtLjJoMTRjLjIgMCAuNCAwIC42LjJzLjIuNC4yLjYgMCAuNC0uMi42LS40LjItLjYuMlptLTE0LTdoMTRjLjIgMCAuNCAwIC42LjJzLjIuNC4yLjYgMCAuNC0uMi42LS40LjItLjYuMmgtMTRjLS4yIDAtLjQgMC0uNi0uMnMtLjItLjQtLjItLjYgMC0uNC4yLS42LjQtLjIuNi0uMloiIHN0eWxlPSJmaWxsOiMwMDZjZjI7c3Ryb2tlOiMwMDZjZjIiLz48L3N2Zz4=")
    }

    #muneer-action-align-left .muneer-icon,
    #muneer-action-text-alignment .muneer-icon[data-icon=align-left] {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4LjgxNSA4LjgzM2gtMjRhLjgzMy44MzMgMCAxIDEgMC0xLjY2NmgyNGEuODMzLjgzMyAwIDAgMSAwIDEuNjY2Wm0tNS4zMzQgNS4zMzRINC44MTZhLjgzMy44MzMgMCAxIDEgMC0xLjY2N2gxOC42NjdhLjgzMy44MzMgMCAxIDEgMCAxLjY2N1ptNS4zMzQgNS4zMzNoLTI0YS44MzMuODMzIDAgMSAxIDAtMS42NjdoMjRhLjgzMy44MzMgMCAwIDEgMCAxLjY2N1ptLTUuMzM0IDUuMzMzSDQuODE2YS44MzMuODMzIDAgMSAxIDAtMS42NjZoMTguNjY3YS44MzMuODMzIDAgMSAxIDAgMS42NjZaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4LjgxNSA4LjgzM2gtMjRhLjgzMy44MzMgMCAxIDEgMC0xLjY2NmgyNGEuODMzLjgzMyAwIDAgMSAwIDEuNjY2Wm0tNS4zMzQgNS4zMzRINC44MTZhLjgzMy44MzMgMCAxIDEgMC0xLjY2N2gxOC42NjdhLjgzMy44MzMgMCAxIDEgMCAxLjY2N1ptNS4zMzQgNS4zMzNoLTI0YS44MzMuODMzIDAgMSAxIDAtMS42NjdoMjRhLjgzMy44MzMgMCAwIDEgMCAxLjY2N1ptLTUuMzM0IDUuMzMzSDQuODE2YS44MzMuODMzIDAgMSAxIDAtMS42NjZoMTguNjY3YS44MzMuODMzIDAgMSAxIDAgMS42NjZaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiLz48L3N2Zz4=")
    }

    #muneer-action-align-center .muneer-icon,
    #muneer-action-text-alignment .muneer-icon[data-icon=align-center] {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4LjgxNSA4LjgzM2gtMjRhLjgzMy44MzMgMCAxIDEgMC0xLjY2NmgyNGEuODMzLjgzMyAwIDEgMSAwIDEuNjY2Wk05LjU1OSAxMi43NDRhLjgzMy44MzMgMCAwIDEgLjU5LS4yNDRoMTMuMzMzYS44MzMuODMzIDAgMCAxIDAgMS42NjdIMTAuMTQ4YS44MzMuODMzIDAgMCAxLS41OS0xLjQyM1ptLTQuNzQ0IDUuMDloMjRhLjgzMy44MzMgMCAxIDEgMCAxLjY2NmgtMjRhLjgzMy44MzMgMCAxIDEgMC0xLjY2N1ptNS4zMzMgNS4zMzNoMTMuMzM0YS44MzQuODM0IDAgMCAxIDAgMS42NjZIMTAuMTQ4YS44MzMuODMzIDAgMSAxIDAtMS42NjZaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4LjgxNSA4LjgzM2gtMjRhLjgzMy44MzMgMCAxIDEgMC0xLjY2NmgyNGEuODMzLjgzMyAwIDEgMSAwIDEuNjY2Wk05LjU1OSAxMi43NDRhLjgzMy44MzMgMCAwIDEgLjU5LS4yNDRoMTMuMzMzYS44MzMuODMzIDAgMCAxIDAgMS42NjdIMTAuMTQ4YS44MzMuODMzIDAgMCAxLS41OS0xLjQyM1ptLTQuNzQ0IDUuMDloMjRhLjgzMy44MzMgMCAxIDEgMCAxLjY2NmgtMjRhLjgzMy44MzMgMCAxIDEgMC0xLjY2N1ptNS4zMzMgNS4zMzNoMTMuMzM0YS44MzQuODM0IDAgMCAxIDAgMS42NjZIMTAuMTQ4YS44MzMuODMzIDAgMSAxIDAtMS42NjZaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiLz48L3N2Zz4=")
    }

    #muneer-action-align-right .muneer-icon,
    #muneer-action-text-alignment .muneer-icon[data-icon=align-right] {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4LjgxNSA4LjgzM2gtMjRhLjgzMy44MzMgMCAxIDEgMC0xLjY2NmgyNGEuODMzLjgzMyAwIDEgMSAwIDEuNjY2Wk0xMC4xNDggMjMuMTY3aDE4LjY2N2EuODM0LjgzNCAwIDEgMSAwIDEuNjY2SDEwLjE0OGEuODMzLjgzMyAwIDEgMSAwLTEuNjY2Wm0wLTEwLjY2N2gxOC42NjdhLjgzMy44MzMgMCAxIDEgMCAxLjY2N0gxMC4xNDhhLjgzMy44MzMgMCAxIDEgMC0xLjY2N1ptLTUuMzMzIDUuMzMzaDI0YS44MzMuODMzIDAgMSAxIDAgMS42NjdoLTI0YS44MzMuODMzIDAgMSAxIDAtMS42NjdaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4LjgxNSA4LjgzM2gtMjRhLjgzMy44MzMgMCAxIDEgMC0xLjY2NmgyNGEuODMzLjgzMyAwIDEgMSAwIDEuNjY2Wk0xMC4xNDggMjMuMTY3aDE4LjY2N2EuODM0LjgzNCAwIDEgMSAwIDEuNjY2SDEwLjE0OGEuODMzLjgzMyAwIDEgMSAwLTEuNjY2Wm0wLTEwLjY2N2gxOC42NjdhLjgzMy44MzMgMCAxIDEgMCAxLjY2N0gxMC4xNDhhLjgzMy44MzMgMCAxIDEgMC0xLjY2N1ptLTUuMzMzIDUuMzMzaDI0YS44MzMuODMzIDAgMSAxIDAgMS42NjdoLTI0YS44MzMuODMzIDAgMSAxIDAtMS42NjdaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiLz48L3N2Zz4=")
    }

    #muneer-action-align-justify .muneer-icon,
    #muneer-action-text-alignment .muneer-icon[data-icon=align-justify] {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMyAzMiI+PHBhdGggZD0iTTI4LjggOC44aC0yNGMtLjIgMC0uNCAwLS42LS4yQzQgOC40IDQgOC4yIDQgOHMwLS40LjItLjZjLjItLjIuNC0uMi42LS4yaDI0Yy4yIDAgLjQgMCAuNi4ycy4yLjQuMi42IDAgLjQtLjIuNmMtLjIuMi0uNC4yLS42LjJabTAgNS40aC0yNGMtLjIgMC0uNCAwLS42LS4yLS4yLS4yLS4yLS40LS4yLS42czAtLjQuMi0uNmMuMi0uMi40LS4yLjYtLjJoMjRjLjIgMCAuNCAwIC42LjJzLjIuNC4yLjYgMCAuNC0uMi42Yy0uMi4yLS40LjItLjYuMlptMCAxMC42aC0yNGMtLjIgMC0uNCAwLS42LS4yLS4yLS4yLS4yLS40LS4yLS42czAtLjQuMi0uNmMuMi0uMi40LS4yLjYtLjJoMjRjLjIgMCAuNCAwIC42LjJzLjIuNC4yLjYgMCAuNC0uMi42Yy0uMi4yLS40LjItLjYuMlptLTI0LTdoMjRjLjIgMCAuNCAwIC42LjJzLjIuNC4yLjYgMCAuNC0uMi42Yy0uMi4yLS40LjItLjYuMmgtMjRjLS4yIDAtLjQgMC0uNi0uMi0uMi0uMi0uMi0uNC0uMi0uNnMwLS40LjItLjZjLjItLjIuNC0uMi42LS4yWiIgc3R5bGU9ImZpbGw6IzAwNmNmMjtzdHJva2U6IzAwNmNmMiIvPjwvc3ZnPg==");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMyAzMiI+PHBhdGggZD0iTTI4LjggOC44aC0yNGMtLjIgMC0uNCAwLS42LS4yQzQgOC40IDQgOC4yIDQgOHMwLS40LjItLjZjLjItLjIuNC0uMi42LS4yaDI0Yy4yIDAgLjQgMCAuNi4ycy4yLjQuMi42IDAgLjQtLjIuNmMtLjIuMi0uNC4yLS42LjJabTAgNS40aC0yNGMtLjIgMC0uNCAwLS42LS4yLS4yLS4yLS4yLS40LS4yLS42czAtLjQuMi0uNmMuMi0uMi40LS4yLjYtLjJoMjRjLjIgMCAuNCAwIC42LjJzLjIuNC4yLjYgMCAuNC0uMi42Yy0uMi4yLS40LjItLjYuMlptMCAxMC42aC0yNGMtLjIgMC0uNCAwLS42LS4yLS4yLS4yLS4yLS40LS4yLS42czAtLjQuMi0uNmMuMi0uMi40LS4yLjYtLjJoMjRjLjIgMCAuNCAwIC42LjJzLjIuNC4yLjYgMCAuNC0uMi42Yy0uMi4yLS40LjItLjYuMlptLTI0LTdoMjRjLjIgMCAuNCAwIC42LjJzLjIuNC4yLjYgMCAuNC0uMi42Yy0uMi4yLS40LjItLjYuMmgtMjRjLS4yIDAtLjQgMC0uNi0uMi0uMi0uMi0uMi0uNC0uMi0uNnMwLS40LjItLjZjLjItLjIuNC0uMi42LS4yWiIgc3R5bGU9ImZpbGw6IzAwNmNmMjtzdHJva2U6IzAwNmNmMiIvPjwvc3ZnPg==")
    }

    #muneer-action-smart-contrast .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE0LjggNS4zYy0uMiAwLS4zLjItLjMuNHMuMS40LjMuNGwyLjIuOC44IDIuMmMwIC4yLjIuMy40LjNzLjQtLjEuNC0uM2wuOC0yLjIgMi4yLS44Yy4yIDAgLjMtLjIuMy0uNHMtLjEtLjQtLjMtLjRsLTIuMi0uOC0uOC0yLjJjMC0uMi0uMi0uMy0uNC0uM3MtLjQuMS0uNC4zTDE3IDQuNWwtMi4yLjhaTTEwIDQuOWMtLjEtLjItLjMtLjQtLjYtLjRzLS41LjEtLjYuNEw2LjcgOS40bC00LjUgMi4xYy0uMi4xLS40LjMtLjQuNnMuMS41LjQuNmw0LjUgMi4xIDIuMSA0LjVjLjEuMi4zLjQuNi40cy41LS4xLjYtLjRsMi4xLTQuNSA0LjUtMi4xYy4yLS4xLjQtLjMuNC0uNnMtLjEtLjUtLjQtLjZsLTQuNS0yLjFMMTAgNC45Wk0xNyAxN2wtMi4yLjhjLS4yIDAtLjMuMi0uMy40cy4xLjQuMy40bDIuMi44LjggMi4yYzAgLjIuMi4zLjQuM3MuNC0uMS40LS4zbC44LTIuMiAyLjItLjhjLjIgMCAuMy0uMi4zLS40cy0uMS0uNC0uMy0uNGwtMi4yLS44LS44LTIuMmMwLS4yLS4yLS4zLS40LS4zcy0uNC4xLS40LjNMMTcgMTdaIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE0LjggNS4zYy0uMiAwLS4zLjItLjMuNHMuMS40LjMuNGwyLjIuOC44IDIuMmMwIC4yLjIuMy40LjNzLjQtLjEuNC0uM2wuOC0yLjIgMi4yLS44Yy4yIDAgLjMtLjIuMy0uNHMtLjEtLjQtLjMtLjRsLTIuMi0uOC0uOC0yLjJjMC0uMi0uMi0uMy0uNC0uM3MtLjQuMS0uNC4zTDE3IDQuNWwtMi4yLjhaTTEwIDQuOWMtLjEtLjItLjMtLjQtLjYtLjRzLS41LjEtLjYuNEw2LjcgOS40bC00LjUgMi4xYy0uMi4xLS40LjMtLjQuNnMuMS41LjQuNmw0LjUgMi4xIDIuMSA0LjVjLjEuMi4zLjQuNi40cy41LS4xLjYtLjRsMi4xLTQuNSA0LjUtMi4xYy4yLS4xLjQtLjMuNC0uNnMtLjEtLjUtLjQtLjZsLTQuNS0yLjFMMTAgNC45Wk0xNyAxN2wtMi4yLjhjLS4yIDAtLjMuMi0uMy40cy4xLjQuMy40bDIuMi44LjggMi4yYzAgLjIuMi4zLjQuM3MuNC0uMS40LS4zbC44LTIuMiAyLjItLjhjLjIgMCAuMy0uMi4zLS40cy0uMS0uNC0uMy0uNGwtMi4yLS44LS44LTIuMmMwLS4yLS4yLS4zLS40LS4zcy0uNC4xLS40LjNMMTcgMTdaIi8+PC9zdmc+")
    }

    #muneer-action-dark-contrast .muneer-icon,
    #muneer-action-smart-contrast .muneer-icon[data-icon=dark-contrast] {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCI+PHBhdGggZmlsbD0iY3VycmVudENvbG9yIiBkPSJNMTMuMSAyM3EtMi4xIDAtMy45MzctLjh0LTMuMi0yLjE2M1E0LjYgMTguNjc1IDMuOCAxNi44MzdUMyAxMi45cTAtMy42NSAyLjMyNS02LjQzOFQxMS4yNSAzcS0uNDUgMi40NzUuMjc1IDQuODM4dDIuNSA0LjEzN3ExLjc3NSAxLjc3NSA0LjEzOCAyLjVUMjMgMTQuNzVxLS42NSAzLjYtMy40NSA1LjkyNVQxMy4xIDIzWiIvPjwvc3ZnPg==");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCI+PHBhdGggZmlsbD0iY3VycmVudENvbG9yIiBkPSJNMTMuMSAyM3EtMi4xIDAtMy45MzctLjh0LTMuMi0yLjE2M1E0LjYgMTguNjc1IDMuOCAxNi44MzdUMyAxMi45cTAtMy42NSAyLjMyNS02LjQzOFQxMS4yNSAzcS0uNDUgMi40NzUuMjc1IDQuODM4dDIuNSA0LjEzN3ExLjc3NSAxLjc3NSA0LjEzOCAyLjVUMjMgMTQuNzVxLS42NSAzLjYtMy40NSA1LjkyNVQxMy4xIDIzWiIvPjwvc3ZnPg==")
    }

    #muneer-action-invert-colors .muneer-icon,
    #muneer-action-smart-contrast .muneer-icon[data-icon=invert-colors] {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTMuOSAyLjNjLjUtLjUgMS4zLS41IDEuOCAwbDMuMiAzLjJMMTEgMy40YzEuMS0xLjEgMi45LTEuMSA0IDBsNiA2YzEuMSAxLjEgMS4xIDIuOSAwIDRsLTcuNSA3LjVjLTEuNSAxLjUtMy44IDEuNS01LjQgMGwtNC43LTQuN2MtMS41LTEuNS0xLjUtMy44IDAtNS40bDMuNy0zLjctMy4yLTMuMmMtLjUtLjUtLjUtMS4zIDAtMS44Wm02LjkgOC43TDguOSA5LjFsLTMuNyAzLjdjLS4yLjItLjMuMy0uMy42aDEyLjZsMS43LTEuN3YtLjRsLTYtNmgtLjRsLTIuMSAyLjEgMS45IDEuOWMuNS41LjUgMS4zIDAgMS44cy0xLjMuNS0xLjggMFpNMjAgMjIuMWMtMSAwLTEuNy0uOC0xLjctMS43cy45LTIuMiAxLjQtM2MuMi0uMi41LS4yLjcgMCAuNS44IDEuNCAyLjMgMS40IDNzLS44IDEuNy0xLjcgMS43WiIvPjwvc3ZnPg==");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTMuOSAyLjNjLjUtLjUgMS4zLS41IDEuOCAwbDMuMiAzLjJMMTEgMy40YzEuMS0xLjEgMi45LTEuMSA0IDBsNiA2YzEuMSAxLjEgMS4xIDIuOSAwIDRsLTcuNSA3LjVjLTEuNSAxLjUtMy44IDEuNS01LjQgMGwtNC43LTQuN2MtMS41LTEuNS0xLjUtMy44IDAtNS40bDMuNy0zLjctMy4yLTMuMmMtLjUtLjUtLjUtMS4zIDAtMS44Wm02LjkgOC43TDguOSA5LjFsLTMuNyAzLjdjLS4yLjItLjMuMy0uMy42aDEyLjZsMS43LTEuN3YtLjRsLTYtNmgtLjRsLTIuMSAyLjEgMS45IDEuOWMuNS41LjUgMS4zIDAgMS44cy0xLjMuNS0xLjggMFpNMjAgMjIuMWMtMSAwLTEuNy0uOC0xLjctMS43cy45LTIuMiAxLjQtM2MuMi0uMi41LS4yLjcgMCAuNS44IDEuNCAyLjMgMS40IDNzLS44IDEuNy0xLjcgMS43WiIvPjwvc3ZnPg==")
    }

    #muneer-action-light-contrast .muneer-icon,
    #muneer-action-smart-contrast .muneer-icon[data-icon=light-contrast] {
      -webkit-mask-image: url("data:image/svg+xml;base64,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");
      mask-image: url("data:image/svg+xml;base64,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")
    }

    #muneer-action-monochrome .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI2LjA4NSAxNy45NTZhOC43NjMgOC43NjMgMCAwIDAtMS4xODUtMy40NTNsLTYuNTItOS42OGMtLjU2LS44MzQtMS43MTYtMS4wNzEtMi41ODEtLjUzYTEuODM0IDEuODM0IDAgMCAwLS41NDcuNTNsLTYuNTI0IDkuNjhjLTIuMjYgMy43ODQtMS4zOCA4LjU4OCAyLjA5IDExLjM5NGE5LjU3MSA5LjU3MSAwIDAgMCA3LjUxIDEuOTg3bTExLjgyIDEuNDUtNi42NjYtNi42NjdtMCA2LjY2NiA2LjY2Ni02LjY2NiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI2LjA4NSAxNy45NTZhOC43NjMgOC43NjMgMCAwIDAtMS4xODUtMy40NTNsLTYuNTItOS42OGMtLjU2LS44MzQtMS43MTYtMS4wNzEtMi41ODEtLjUzYTEuODM0IDEuODM0IDAgMCAwLS41NDcuNTNsLTYuNTI0IDkuNjhjLTIuMjYgMy43ODQtMS4zOCA4LjU4OCAyLjA5IDExLjM5NGE5LjU3MSA5LjU3MSAwIDAgMCA3LjUxIDEuOTg3bTExLjgyIDEuNDUtNi42NjYtNi42NjdtMCA2LjY2NiA2LjY2Ni02LjY2NiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+")
    }

    #muneer-action-high-contrast .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE3LjMxNSA0Ljc1di0uNWgtLjVhMTEuNzUgMTEuNzUgMCAwIDAtOC4zMSAyMC4wNTggMTEuNjc3IDExLjY3NyAwIDAgMCA4LjMxMSAzLjQ0MmwuNDk5LS4wMDFWNC43NVpNMy4zMTUgMTZjMC03LjQ1NiA2LjA0NC0xMy41IDEzLjUtMTMuNXMxMy41IDYuMDQ0IDEzLjUgMTMuNS02LjA0NCAxMy41LTEzLjUgMTMuNS0xMy41LTYuMDQ0LTEzLjUtMTMuNVoiIGZpbGw9IiMwMDZDRjIiIHN0cm9rZT0iIzAwNkNGMiIvPjwvc3ZnPg==");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE3LjMxNSA0Ljc1di0uNWgtLjVhMTEuNzUgMTEuNzUgMCAwIDAtOC4zMSAyMC4wNTggMTEuNjc3IDExLjY3NyAwIDAgMCA4LjMxMSAzLjQ0MmwuNDk5LS4wMDFWNC43NVpNMy4zMTUgMTZjMC03LjQ1NiA2LjA0NC0xMy41IDEzLjUtMTMuNXMxMy41IDYuMDQ0IDEzLjUgMTMuNS02LjA0NCAxMy41LTEzLjUgMTMuNS0xMy41LTYuMDQ0LTEzLjUtMTMuNVoiIGZpbGw9IiMwMDZDRjIiIHN0cm9rZT0iIzAwNkNGMiIvPjwvc3ZnPg==")
    }

    #muneer-action-high-saturation .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcuODc3IDE2LjExN2E5LjQ0NCA5LjQ0NCAwIDAgMSAyLjQyNy00Ljc3NWwuMDA0LS4wMDMuMDIyLS4wMjIgNi4zMy02LjQ4MWEuMjE4LjIxOCAwIDAgMSAuMTU1LS4wN2MuMDUyIDAgLjEwOC4wMjIuMTU1LjA3bDYuMzMzIDYuNDg0LjAwMi4wMDJhOS40NDQgOS40NDQgMCAwIDEgMi40NDggNC43OTcgOS41OTggOS41OTggMCAwIDEtLjU1MyA1LjM5NmMtLjcxIDEuNzE1LTEuODgxIDMuMTYzLTMuMzYzIDQuMTY1YTguOTI2IDguOTI2IDAgMCAxLTUuMDIyIDEuNTUzIDguOTI2IDguOTI2IDAgMCAxLTUuMDIyLTEuNTUzYy0xLjQ4Mi0xLjAwMy0yLjY1NC0yLjQ1Mi0zLjM2My00LjE2N2E5LjU5OCA5LjU5OCAwIDAgMS0uNTUzLTUuMzk2WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcuODc3IDE2LjExN2E5LjQ0NCA5LjQ0NCAwIDAgMSAyLjQyNy00Ljc3NWwuMDA0LS4wMDMuMDIyLS4wMjIgNi4zMy02LjQ4MWEuMjE4LjIxOCAwIDAgMSAuMTU1LS4wN2MuMDUyIDAgLjEwOC4wMjIuMTU1LjA3bDYuMzMzIDYuNDg0LjAwMi4wMDJhOS40NDQgOS40NDQgMCAwIDEgMi40NDggNC43OTcgOS41OTggOS41OTggMCAwIDEtLjU1MyA1LjM5NmMtLjcxIDEuNzE1LTEuODgxIDMuMTYzLTMuMzYzIDQuMTY1YTguOTI2IDguOTI2IDAgMCAxLTUuMDIyIDEuNTUzIDguOTI2IDguOTI2IDAgMCAxLTUuMDIyLTEuNTUzYy0xLjQ4Mi0xLjAwMy0yLjY1NC0yLjQ1Mi0zLjM2My00LjE2N2E5LjU5OCA5LjU5OCAwIDAgMS0uNTUzLTUuMzk2WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+")
    }

    #muneer-action-low-saturation .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIiIGhlaWdodD0iMjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTS42NDggMTQuNDY3YzAtMS4zNzkuMjY1LTIuNjYuNzktMy44NDdhMTAuNTA2IDEwLjUwNiAwIDAgMSAyLjE5Ni0zLjE5OGw2LjEyMi02LjAyM2MuMTU2LS4xMzcuMzI2LS4yNDIuNTE0LS4zMTlsLS4xODctLjQ2LjE4Ny40NmMuMTktLjA3Ny4zNy0uMTEzLjU0Ni0uMTEzLjE3NCAwIC4zNTMuMDM1LjU0Mi4xMTMuMTg5LjA3Ny4zNi4xODMuNTE2LjMybDYuMTIgNi4wMi4wMDIuMDAyYy45MzQuOTM0IDEuNjY1IDEuOTk5IDIuMTk1IDMuMTk4LjUyNiAxLjE4OS43OSAyLjQ3Ljc5IDMuODQ3IDAgMi44LS45ODUgNS4xNi0yLjk2NiA3LjExLTEuOTgzIDEuOTUtNC4zNzMgMi45MjMtNy4yIDIuOTIzcy01LjIxNy0uOTcyLTcuMi0yLjkyM2MtMS45ODItMS45NS0yLjk2OC00LjMxLTIuOTY3LTcuMTFabTEuNzQgMS4yNzUuMDY0LjQyNWgxNi42NTNsLjA3LS40MThjLjI3OS0xLjY3NS4xMy0zLjE1NS0uNDk2LTQuNDA2LS41NzktMS4xNTktMS4xOTItMi4wNjktMS44NDgtMi43bC01LjY2NS01LjU5OS0uMzUxLS4zNDctLjM1Mi4zNDdMNC44IDguNjQyYy0uNjU1LjYzMS0xLjI3MyAxLjU0LTEuODYzIDIuNjk4LS42MzcgMS4yNDktLjgwNSAyLjcyNy0uNTQ5IDQuNDAyWiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIiIGhlaWdodD0iMjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTS42NDggMTQuNDY3YzAtMS4zNzkuMjY1LTIuNjYuNzktMy44NDdhMTAuNTA2IDEwLjUwNiAwIDAgMSAyLjE5Ni0zLjE5OGw2LjEyMi02LjAyM2MuMTU2LS4xMzcuMzI2LS4yNDIuNTE0LS4zMTlsLS4xODctLjQ2LjE4Ny40NmMuMTktLjA3Ny4zNy0uMTEzLjU0Ni0uMTEzLjE3NCAwIC4zNTMuMDM1LjU0Mi4xMTMuMTg5LjA3Ny4zNi4xODMuNTE2LjMybDYuMTIgNi4wMi4wMDIuMDAyYy45MzQuOTM0IDEuNjY1IDEuOTk5IDIuMTk1IDMuMTk4LjUyNiAxLjE4OS43OSAyLjQ3Ljc5IDMuODQ3IDAgMi44LS45ODUgNS4xNi0yLjk2NiA3LjExLTEuOTgzIDEuOTUtNC4zNzMgMi45MjMtNy4yIDIuOTIzcy01LjIxNy0uOTcyLTcuMi0yLjkyM2MtMS45ODItMS45NS0yLjk2OC00LjMxLTIuOTY3LTcuMTFabTEuNzQgMS4yNzUuMDY0LjQyNWgxNi42NTNsLjA3LS40MThjLjI3OS0xLjY3NS4xMy0zLjE1NS0uNDk2LTQuNDA2LS41NzktMS4xNTktMS4xOTItMi4wNjktMS44NDgtMi43bC01LjY2NS01LjU5OS0uMzUxLS4zNDctLjM1Mi4zNDdMNC44IDguNjQyYy0uNjU1LjYzMS0xLjI3MyAxLjU0LTEuODYzIDIuNjk4LS42MzcgMS4yNDktLjgwNSAyLjcyNy0uNTQ5IDQuNDAyWiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+")
    }

    #muneer-action-mute-sounds .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiI+PHBhdGggZmlsbD0iY3VycmVudENvbG9yIiBkPSJNOCAyLjc1djEwLjVhLjc1MS43NTEgMCAwIDEtMS4yMzguNTdMMy40NzMgMTFIMS43NUExLjc1IDEuNzUgMCAwIDEgMCA5LjI1di0yLjVDMCA1Ljc4NC43ODQgNSAxLjc1IDVoMS43MjJsMy4yOS0yLjgyQS43NS43NSAwIDAgMSA4IDIuNzVabTMuMjggMi40N0wxMyA2Ljk0bDEuNzItMS43MmEuNzUxLjc1MSAwIDAgMSAxLjA0Mi4wMTguNzUxLjc1MSAwIDAgMSAuMDE4IDEuMDQyTDE0LjA2IDhsMS43MiAxLjcyYS43NDkuNzQ5IDAgMCAxLS4zMjYgMS4yNzUuNzQ5Ljc0OSAwIDAgMS0uNzM0LS4yMTVMMTMgOS4wNmwtMS43MiAxLjcyYS43NDkuNzQ5IDAgMCAxLTEuMjc1LS4zMjYuNzQ5Ljc0OSAwIDAgMSAuMjE1LS43MzRMMTEuOTQgOGwtMS43Mi0xLjcyYS43NDkuNzQ5IDAgMCAxIC4zMjYtMS4yNzUuNzQ5Ljc0OSAwIDAgMSAuNzM0LjIxNVptLTcuMDQyIDEuMWEuNzUyLjc1MiAwIDAgMS0uNDg4LjE4aC0yYS4yNS4yNSAwIDAgMC0uMjUuMjV2Mi41YzAgLjEzOC4xMTIuMjUuMjUuMjVoMmMuMTc5IDAgLjM1Mi4wNjQuNDg4LjE4TDYuNSAxMS42MlY0LjM4WiIvPjwvc3ZnPg==");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiI+PHBhdGggZmlsbD0iY3VycmVudENvbG9yIiBkPSJNOCAyLjc1djEwLjVhLjc1MS43NTEgMCAwIDEtMS4yMzguNTdMMy40NzMgMTFIMS43NUExLjc1IDEuNzUgMCAwIDEgMCA5LjI1di0yLjVDMCA1Ljc4NC43ODQgNSAxLjc1IDVoMS43MjJsMy4yOS0yLjgyQS43NS43NSAwIDAgMSA4IDIuNzVabTMuMjggMi40N0wxMyA2Ljk0bDEuNzItMS43MmEuNzUxLjc1MSAwIDAgMSAxLjA0Mi4wMTguNzUxLjc1MSAwIDAgMSAuMDE4IDEuMDQyTDE0LjA2IDhsMS43MiAxLjcyYS43NDkuNzQ5IDAgMCAxLS4zMjYgMS4yNzUuNzQ5Ljc0OSAwIDAgMS0uNzM0LS4yMTVMMTMgOS4wNmwtMS43MiAxLjcyYS43NDkuNzQ5IDAgMCAxLTEuMjc1LS4zMjYuNzQ5Ljc0OSAwIDAgMSAuMjE1LS43MzRMMTEuOTQgOGwtMS43Mi0xLjcyYS43NDkuNzQ5IDAgMCAxIC4zMjYtMS4yNzUuNzQ5Ljc0OSAwIDAgMSAuNzM0LjIxNVptLTcuMDQyIDEuMWEuNzUyLjc1MiAwIDAgMS0uNDg4LjE4aC0yYS4yNS4yNSAwIDAgMC0uMjUuMjV2Mi41YzAgLjEzOC4xMTIuMjUuMjUuMjVoMmMuMTc5IDAgLjM1Mi4wNjQuNDg4LjE4TDYuNSAxMS42MlY0LjM4WiIvPjwvc3ZnPg==")
    }

    #muneer-action-hide-images .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI2LjE0OCA2LjE2N2gtMTQuNjZMOS44MjMgNC41aDE2LjMyNmMuNiAwIDEuMS4yMDggMS41My42MzguNDMuNDMuNjM4LjkzLjYzNyAxLjUyOHYxNi4zMjdsLTEuNjY3LTEuNjY3VjYuMTY2aC0uNVptMi42NjcuNXYxNi44MjZWNi42NjdabS0yMy41IDE4LjY2N1Y3LjUyNWwtLjE0Ny0uMTQ2LTEuMi0xLjJjLS4xNDEtLjE0Mi0uMjItLjMyMS0uMjItLjU4IDAtLjI1OS4wNzktLjQzOC4yMi0uNTguMTQyLS4xNDEuMzIxLS4yMi41OC0uMjIuMjU5IDAgLjQzOC4wNzkuNTguMjJsMjIuNjY3IDIyLjY2N2MuMTQxLjE0MS4yMi4zMjEuMjIuNTggMCAuMjU4LS4wNzkuNDM4LS4yMi41OC0uMTQyLjE0MS0uMzIxLjIyLS41OC4yMi0uMjU5IDAtLjQzOC0uMDc5LS41OC0uMjJsLTEuMi0xLjItLjE0Ni0uMTQ3SDcuNDhjLS41OTkgMC0xLjEtLjIwOC0xLjUzLS42MzgtLjQzLS40My0uNjM3LS45My0uNjM2LTEuNTI4Wm0xNC43NTMtMy4wMi0uMTQ2LS4xNDdoLTQuMTA3bC40LS41MzQgMS4xLTEuNDY2LjI2LS4zNDctLjMwNy0uMzA3LTkuNDMzLTkuNDMzLS44NTQtLjg1NHYxNi42MDdIMjMuNTlsLS44NTQtLjg1My0yLjY2Ny0yLjY2N1ptLTQuNjU0LS42ODEuNC41MzRoLTUuNjY2Yy0uMDU0IDAtLjA3OC0uMDEyLS4wOS0uMDE5YS4yMi4yMiAwIDAgMS0uMDctLjA4Ny4yMjUuMjI1IDAgMCAxLS4wMjQtLjA1NS4wNS4wNSAwIDAgMS0uMDAxLS4wMTdjMC0uMDAyLjAwMi0uMDI2LjA0Mi0uMDc3bC4wMDUtLjAwNi4wMDUtLjAwNyAyLjY2Ni0zLjU2NmMuMDQtLjA1Mi4wNy0uMDY2LjEzNC0uMDY2LjA2NSAwIC4wOTQuMDE0LjEzMy4wNjZsMi40NjYgMy4zWiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI2LjE0OCA2LjE2N2gtMTQuNjZMOS44MjMgNC41aDE2LjMyNmMuNiAwIDEuMS4yMDggMS41My42MzguNDMuNDMuNjM4LjkzLjYzNyAxLjUyOHYxNi4zMjdsLTEuNjY3LTEuNjY3VjYuMTY2aC0uNVptMi42NjcuNXYxNi44MjZWNi42NjdabS0yMy41IDE4LjY2N1Y3LjUyNWwtLjE0Ny0uMTQ2LTEuMi0xLjJjLS4xNDEtLjE0Mi0uMjItLjMyMS0uMjItLjU4IDAtLjI1OS4wNzktLjQzOC4yMi0uNTguMTQyLS4xNDEuMzIxLS4yMi41OC0uMjIuMjU5IDAgLjQzOC4wNzkuNTguMjJsMjIuNjY3IDIyLjY2N2MuMTQxLjE0MS4yMi4zMjEuMjIuNTggMCAuMjU4LS4wNzkuNDM4LS4yMi41OC0uMTQyLjE0MS0uMzIxLjIyLS41OC4yMi0uMjU5IDAtLjQzOC0uMDc5LS41OC0uMjJsLTEuMi0xLjItLjE0Ni0uMTQ3SDcuNDhjLS41OTkgMC0xLjEtLjIwOC0xLjUzLS42MzgtLjQzLS40My0uNjM3LS45My0uNjM2LTEuNTI4Wm0xNC43NTMtMy4wMi0uMTQ2LS4xNDdoLTQuMTA3bC40LS41MzQgMS4xLTEuNDY2LjI2LS4zNDctLjMwNy0uMzA3LTkuNDMzLTkuNDMzLS44NTQtLjg1NHYxNi42MDdIMjMuNTlsLS44NTQtLjg1My0yLjY2Ny0yLjY2N1ptLTQuNjU0LS42ODEuNC41MzRoLTUuNjY2Yy0uMDU0IDAtLjA3OC0uMDEyLS4wOS0uMDE5YS4yMi4yMiAwIDAgMS0uMDctLjA4Ny4yMjUuMjI1IDAgMCAxLS4wMjQtLjA1NS4wNS4wNSAwIDAgMS0uMDAxLS4wMTdjMC0uMDAyLjAwMi0uMDI2LjA0Mi0uMDc3bC4wMDUtLjAwNi4wMDUtLjAwNyAyLjY2Ni0zLjU2NmMuMDQtLjA1Mi4wNy0uMDY2LjEzNC0uMDY2LjA2NSAwIC4wOTQuMDE0LjEzMy4wNjZsMi40NjYgMy4zWiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+")
    }

    #muneer-action-image-alt .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCI+PHBhdGggZmlsbD0iY3VycmVudENvbG9yIiBkPSJNNSAyMXEtLjgyNSAwLTEuNDEzLS41ODhUMyAxOVY1cTAtLjgyNS41ODgtMS40MTNUNSAzaDE0cS44MjUgMCAxLjQxMy41ODhUMjEgNXYxNHEwIC44MjUtLjU4OCAxLjQxM1QxOSAyMUg1Wm0wLTJoMTRWNUg1djE0Wm0xLTJoMTJsLTMuNzUtNS0zIDRMOSAxM2wtMyA0Wm0tMSAyVjV2MTRaIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCI+PHBhdGggZmlsbD0iY3VycmVudENvbG9yIiBkPSJNNSAyMXEtLjgyNSAwLTEuNDEzLS41ODhUMyAxOVY1cTAtLjgyNS41ODgtMS40MTNUNSAzaDE0cS44MjUgMCAxLjQxMy41ODhUMjEgNXYxNHEwIC44MjUtLjU4OCAxLjQxM1QxOSAyMUg1Wm0wLTJoMTRWNUg1djE0Wm0xLTJoMTJsLTMuNzUtNS0zIDRMOSAxM2wtMyA0Wm0tMSAyVjV2MTRaIi8+PC9zdmc+")
    }

    #muneer-action-virtual-keyboard .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5LjYxNSA4LjAzM2gtMjUuNkExLjU2NyAxLjU2NyAwIDAgMCAyLjQ0OCA5LjZ2MTIuOGExLjU2NyAxLjU2NyAwIDAgMCAxLjU2NyAxLjU2N2gyNS42YTEuNTY3IDEuNTY3IDAgMCAwIDEuNTY3LTEuNTY3VjkuNmExLjU2NyAxLjU2NyAwIDAgMC0xLjU2Ny0xLjU2N1ptLTI3LjUxLS4zNDJhMi43IDIuNyAwIDAgMSAxLjkxLS43OTFoMjUuNmEyLjcgMi43IDAgMCAxIDIuNyAyLjd2MTIuOGEyLjcgMi43IDAgMCAxLTIuNyAyLjdoLTI1LjZhMi43IDIuNyAwIDAgMS0yLjctMi43VjkuNmEyLjcgMi43IDAgMCAxIC43OS0xLjkxWm0zLjQ3NiAzLjQ3NmgxLjEzNFYxMi4zSDUuNTh2LTEuMTMzWk05Ljg0OCAxMi4zdi0xLjEzM2gxLjEzM1YxMi4zSDkuODQ5Wm00LjI2Ny0xLjEzM2gxLjEzM1YxMi4zaC0xLjEzM3YtMS4xMzNaTTE4LjM4IDEyLjN2LTEuMTMzaDEuMTM0VjEyLjNIMTguMzhabTQuMjY3LTEuMTMzaDEuMTM0VjEyLjNoLTEuMTM0di0xLjEzM1ptNC4yNjcgMS4xMzN2LTEuMTMzaDEuMTMzVjEyLjNoLTEuMTMzWm0tMi4xMzMgMy4xMzNoMS4xMzN2MS4xMzRoLTEuMTMzdi0xLjEzNFptMi4xMzMgNS40VjE5LjdoMS4xMzN2MS4xMzNoLTEuMTMzWm0tNi40LTUuNGgxLjEzM3YxLjEzNGgtMS4xMzN2LTEuMTM0Wm0tNC4yNjcgMS4xMzR2LTEuMTM0aDEuMTMzdjEuMTM0aC0xLjEzM1ptLTQuMjY3LTEuMTM0aDEuMTM0djEuMTM0aC0xLjEzM3YtMS4xMzRabS00LjI2NiAxLjEzNHYtMS4xMzRoMS4xMzN2MS4xMzRINy43MTVaTTUuNTggMTkuN2gxLjEzNHYxLjEzM0g1LjU4VjE5LjdabTQuMjY3IDEuMTMzVjE5LjdoMTMuOTM0djEuMTMzSDkuODQ4WiIgZmlsbD0iIzFCMDA0NCIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5LjYxNSA4LjAzM2gtMjUuNkExLjU2NyAxLjU2NyAwIDAgMCAyLjQ0OCA5LjZ2MTIuOGExLjU2NyAxLjU2NyAwIDAgMCAxLjU2NyAxLjU2N2gyNS42YTEuNTY3IDEuNTY3IDAgMCAwIDEuNTY3LTEuNTY3VjkuNmExLjU2NyAxLjU2NyAwIDAgMC0xLjU2Ny0xLjU2N1ptLTI3LjUxLS4zNDJhMi43IDIuNyAwIDAgMSAxLjkxLS43OTFoMjUuNmEyLjcgMi43IDAgMCAxIDIuNyAyLjd2MTIuOGEyLjcgMi43IDAgMCAxLTIuNyAyLjdoLTI1LjZhMi43IDIuNyAwIDAgMS0yLjctMi43VjkuNmEyLjcgMi43IDAgMCAxIC43OS0xLjkxWm0zLjQ3NiAzLjQ3NmgxLjEzNFYxMi4zSDUuNTh2LTEuMTMzWk05Ljg0OCAxMi4zdi0xLjEzM2gxLjEzM1YxMi4zSDkuODQ5Wm00LjI2Ny0xLjEzM2gxLjEzM1YxMi4zaC0xLjEzM3YtMS4xMzNaTTE4LjM4IDEyLjN2LTEuMTMzaDEuMTM0VjEyLjNIMTguMzhabTQuMjY3LTEuMTMzaDEuMTM0VjEyLjNoLTEuMTM0di0xLjEzM1ptNC4yNjcgMS4xMzN2LTEuMTMzaDEuMTMzVjEyLjNoLTEuMTMzWm0tMi4xMzMgMy4xMzNoMS4xMzN2MS4xMzRoLTEuMTMzdi0xLjEzNFptMi4xMzMgNS40VjE5LjdoMS4xMzN2MS4xMzNoLTEuMTMzWm0tNi40LTUuNGgxLjEzM3YxLjEzNGgtMS4xMzN2LTEuMTM0Wm0tNC4yNjcgMS4xMzR2LTEuMTM0aDEuMTMzdjEuMTM0aC0xLjEzM1ptLTQuMjY3LTEuMTM0aDEuMTM0djEuMTM0aC0xLjEzM3YtMS4xMzRabS00LjI2NiAxLjEzNHYtMS4xMzRoMS4xMzN2MS4xMzRINy43MTVaTTUuNTggMTkuN2gxLjEzNHYxLjEzM0g1LjU4VjE5LjdabTQuMjY3IDEuMTMzVjE5LjdoMTMuOTM0djEuMTMzSDkuODQ4WiIgZmlsbD0iIzFCMDA0NCIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+")
    }

    #muneer-action-reading-guide .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTExLjAyMiA0Ljc5M2ExIDEgMCAwIDEgLjI5My43MDdWMTRhNS41IDUuNSAwIDAgMCAxMSAwVjUuNWExIDEgMCAwIDEgMiAwVjE0YTcuNSA3LjUgMCAwIDEtMTUgMFY1LjVhMSAxIDAgMCAxIDEuNzA3LS43MDdabS0zLjQxNCAyMWExIDEgMCAwIDEgLjcwNy0uMjkzaDE3YTEgMSAwIDAgMSAwIDJoLTE3YTEgMSAwIDAgMS0uNzA3LTEuNzA3WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTExLjAyMiA0Ljc5M2ExIDEgMCAwIDEgLjI5My43MDdWMTRhNS41IDUuNSAwIDAgMCAxMSAwVjUuNWExIDEgMCAwIDEgMiAwVjE0YTcuNSA3LjUgMCAwIDEtMTUgMFY1LjVhMSAxIDAgMCAxIDEuNzA3LS43MDdabS0zLjQxNCAyMWExIDEgMCAwIDEgLjcwNy0uMjkzaDE3YTEgMSAwIDAgMSAwIDJoLTE3YTEgMSAwIDAgMS0uNzA3LTEuNzA3WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+")
    }

    #muneer-action-stop-animations .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,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");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIwLjgxNSAxOS4xNjdjLjQyOCAwIC44NDEtLjAzNiAxLjI0LS4xMDhhNy43MDIgNy43MDIgMCAwIDAgMS4xOTMtLjMybC4yMjItLjA4LjA4LS4yMmMuNTIyLTEuNDIuNjA2LTIuODExLjIzLTQuMTU2LS4zNjUtMS4zMS0xLjAwMS0yLjQyNi0xLjkxMi0zLjMzNi0uOTEtLjkxLTIuMDI1LTEuNTQ2LTMuMzM0LTEuOTEyLTEuMzQ1LS4zNzctMi43MzctLjI5My00LjE1OC4yMjlsLS4yMjEuMDgxLS4wNzguMjIyYTcuNzUgNy43NSAwIDAgMC0uMzIgMS4xOTRBNi45MjIgNi45MjIgMCAwIDAgMTMuNjQ3IDEyYzAgLjk5Ni4xODUgMS45MzEuNTU4IDIuNzk4LjM3Ljg1Ny44ODkgMS42MSAxLjU1NSAyLjI1NWE2LjkxNSA2LjkxNSAwIDAgMCAyLjI1NiAxLjU1NyA2Ljk4IDYuOTggMCAwIDAgMi43OTguNTU3Wm0wIDB2LS41LjVaTTguNzc3IDEyLjExbC4xNDMtLjA3My4wNzMtLjE0M2E4LjUzMiA4LjUzMiAwIDAgMSAxLjU2LTIuMTU5IDguNTE0IDguNTE0IDAgMCAxIDIuMTU2LTEuNTU4bC4xNS0uMDc2LjA3Mi0uMTVjLjY5LTEuNDIxIDEuNzQ1LTIuNTczIDMuMTgtMy40NmE4Ljc3IDguNzcgMCAwIDEgNC43MDQtMS4zMjVjMS4yMjQgMCAyLjM2Ny4yMzEgMy40MzUuNjkyYTguOTM3IDguOTM3IDAgMCAxIDIuODExIDEuODk1IDguOTM3IDguOTM3IDAgMCAxIDEuODk1IDIuODFBOC41NzMgOC41NzMgMCAwIDEgMjkuNjQ4IDEyYzAgMS44MDMtLjQ0NCAzLjM3My0xLjMyIDQuNzI5YTguOTQ2IDguOTQ2IDAgMCAxLTMuNDc1IDMuMTZsLS4xNDMuMDczLS4wNzMuMTQzYTguNTMyIDguNTMyIDAgMCAxLTEuNTYgMi4xNTkgOC41MTUgOC41MTUgMCAwIDEtMi4xNTcgMS41NThsLS4xNDMuMDczLS4wNzMuMTQzYy0uNzM2IDEuNDI4LTEuODA2IDIuNTg0LTMuMjIxIDMuNDcyLTEuNDA4Ljg4Mi0yLjk2IDEuMzIzLTQuNjY4IDEuMzIzYTguNTc1IDguNTc1IDAgMCAxLTMuNDM1LS42OTIgOC45MzcgOC45MzcgMCAwIDEtMi44MTItMS44OTQgOC45MzcgOC45MzcgMCAwIDEtMS44OTQtMi44MTJBOC41NzQgOC41NzQgMCAwIDEgMy45ODIgMjBjMC0xLjcwOS40NDEtMy4yNiAxLjMyMy00LjY2OC44ODctMS40MTUgMi4wNDQtMi40ODYgMy40NzItMy4yMlpNMTcuMTIgMjUuNzNsMS4xNjMtLjg5NmgtMS40NjhhOC41NzQgOC41NzQgMCAwIDEtMy40MzUtLjY5MiA4LjkzNiA4LjkzNiAwIDAgMS0yLjgxMi0xLjg5NCA4LjkzNyA4LjkzNyAwIDAgMS0xLjg5NC0yLjgxMkE4LjU3NCA4LjU3NCAwIDAgMSA3Ljk4MiAxNnYtMS40N2wtLjg5NyAxLjE2NWE2Ljc3NiA2Ljc3NiAwIDAgMC0xLjA3OSAyLjAzM0E3LjI1MiA3LjI1MiAwIDAgMCA1LjY0OCAyMGMwIC45OTkuMTkgMS45MzUuNTc3IDIuODAzLjM4Ljg1NC44OTIgMS42MDYgMS41MzYgMi4yNWE3LjQwOCA3LjQwOCAwIDAgMCAyLjI1IDEuNTM3IDYuODI4IDYuODI4IDAgMCAwIDIuODA0LjU3N2MuNzg1IDAgMS41NDMtLjEyIDIuMjczLS4zNTlhNi43NiA2Ljc2IDAgMCAwIDIuMDMyLTEuMDc5Wm0tLjMwNi0yLjU2MmE3LjU3IDcuNTcgMCAwIDAgMi4zMDMtLjM1N2MuNzU1LS4yNCAxLjQ0Ni0uNiAyLjA3LTEuMDhsMS4xNjMtLjg5N0gyMC44OGMtMS4yNDUgMC0yLjM5OC0uMjM3LTMuNDY0LS43MDhhOS4xOTQgOS4xOTQgMCAwIDEtMi44MTUtMS45MTIgOS4xNjMgOS4xNjMgMCAwIDEtMS45MTItMi44MTVjLS40Ny0xLjA2Ni0uNzA4LTIuMjItLjcwOC0zLjQ2NWwtLjAwMi0xLjQ2OC0uODk1IDEuMTY0YTYuNjkxIDYuNjkxIDAgMCAwLTEuMDggMi4wN0E3LjU2NyA3LjU2NyAwIDAgMCA5LjY0OCAxNmMwIC45OTcuMTg1IDEuOTMxLjU1OCAyLjc5OC4zNy44NTguODg5IDEuNjEgMS41NTUgMi4yNTZhNi45IDYuOSAwIDAgMCAyLjI1NiAxLjU1NWMuODY3LjM3MiAxLjgwMS41NTcgMi43OTcuNTU4Wm04LjgzNC03LjF2MS40NzFsLjg5Ni0xLjE2NmMuNDgtLjYyNS44NDEtMS4zMTYgMS4wOC0yLjA3MUE3LjU3IDcuNTcgMCAwIDAgMjcuOTgzIDEyYzAtLjk5Ni0uMTg1LTEuOTMxLS41NTktMi43OThhNi45MjYgNi45MjYgMCAwIDAtMS41NTQtMi4yNTUgNi45IDYuOSAwIDAgMC0yLjI1Ny0xLjU1NiA3LjAyOCA3LjAyOCAwIDAgMC0yLjc5Ny0uNTU4Yy0uODI2IDAtMS42MDcuMTE4LTIuMzQuMzU5YTYuNzYgNi43NiAwIDAgMC0yLjAzMiAxLjA3OWwtMS4xNjMuODk2aDEuNDY4YzEuMjQ2IDAgMi4zOTkuMjM3IDMuNDY1LjcwOGE5LjE5NiA5LjE5NiAwIDAgMSAyLjgxNSAxLjkxMmMuOC43OTkgMS40MzcgMS43MzYgMS45MTMgMi44MTUuNDcxIDEuMDY2LjcwOCAyLjIxOS43MDcgMy40NjRaIiBmaWxsPSIjZmZmIiBzdHJva2U9IiNmZmYiLz48L3N2Zz4=")
    }

    #muneer-action-reading-mask .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNzkzIDMuMTEyYTEuNjMzIDEuNjMzIDAgMCAxIDEuMTU1LS40NzloMjcuNzM0YTEuNjMzIDEuNjMzIDAgMCAxIDEuNjMzIDEuNjM0djIzLjQ2NmExLjYzMyAxLjYzMyAwIDAgMS0xLjYzMyAxLjYzNEgyLjk0N2ExLjYzMyAxLjYzMyAwIDAgMS0xLjYzMy0xLjYzNFY0LjI2N2MwLS40MzMuMTcyLS44NDkuNDc4LTEuMTU1Wm0xNS4wMjIgMjAuMDU1YTcuMTY4IDcuMTY4IDAgMSAwIDAtMTQuMzM2IDcuMTY4IDcuMTY4IDAgMCAwIDAgMTQuMzM2WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNzkzIDMuMTEyYTEuNjMzIDEuNjMzIDAgMCAxIDEuMTU1LS40NzloMjcuNzM0YTEuNjMzIDEuNjMzIDAgMCAxIDEuNjMzIDEuNjM0djIzLjQ2NmExLjYzMyAxLjYzMyAwIDAgMS0xLjYzMyAxLjYzNEgyLjk0N2ExLjYzMyAxLjYzMyAwIDAgMS0xLjYzMy0xLjYzNFY0LjI2N2MwLS40MzMuMTcyLS44NDkuNDc4LTEuMTU1Wm0xNS4wMjIgMjAuMDU1YTcuMTY4IDcuMTY4IDAgMSAwIDAtMTQuMzM2IDcuMTY4IDcuMTY4IDAgMCAwIDAgMTQuMzM2WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+")
    }

    #muneer-action-highlight-hover .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMwLjMxNSAyMGEzLjUgMy41IDAgMCAxLTEuNjQ3IDIuOTcyIDMuNDk4IDMuNDk4IDAgMCAwLS4zNzctLjQ0N2wtOC04QTMuNSAzLjUgMCAwIDAgMTQuMzE0IDE3djYuNWgtNy41YTMuNSAzLjUgMCAwIDEtMy41LTMuNVYxMGEzLjUgMy41IDAgMCAxIDMuNS0zLjVoMjBhMy41IDMuNSAwIDAgMSAzLjUgMy41djEwWm0tMTIuMTQ2LTMuMzU1IDggOHYuMDAxYS41LjUgMCAwIDEtLjM1My44NTRoLTQuNzUxbC0uMTUuMi0yLjcgMy42YS41LjUgMCAwIDEtLjktLjNWMTdhLjUuNSAwIDAgMSAuODU0LS4zNTVaTTIxLjMxNSAyNmg0LjVhMSAxIDAgMCAwIC43MDgtMS43MDhsLTgtOGExIDEgMCAwIDAtMS43MDguNzA4bDQuNSA5WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMwLjMxNSAyMGEzLjUgMy41IDAgMCAxLTEuNjQ3IDIuOTcyIDMuNDk4IDMuNDk4IDAgMCAwLS4zNzctLjQ0N2wtOC04QTMuNSAzLjUgMCAwIDAgMTQuMzE0IDE3djYuNWgtNy41YTMuNSAzLjUgMCAwIDEtMy41LTMuNVYxMGEzLjUgMy41IDAgMCAxIDMuNS0zLjVoMjBhMy41IDMuNSAwIDAgMSAzLjUgMy41djEwWm0tMTIuMTQ2LTMuMzU1IDggOHYuMDAxYS41LjUgMCAwIDEtLjM1My44NTRoLTQuNzUxbC0uMTUuMi0yLjcgMy42YS41LjUgMCAwIDEtLjktLjNWMTdhLjUuNSAwIDAgMSAuODU0LS4zNTVaTTIxLjMxNSAyNmg0LjVhMSAxIDAgMCAwIC43MDgtMS43MDhsLTgtOGExIDEgMCAwIDAtMS43MDguNzA4bDQuNSA5WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+")
    }

    #muneer-action-highlight-focus .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2LjgxNSAxNi42NjdhLjY2Ny42NjcgMCAxIDAgMC0xLjMzNC42NjcuNjY3IDAgMCAwIDAgMS4zMzRaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PHBhdGggZD0iTTcuNDgxIDE2YTkuMzMzIDkuMzMzIDAgMCAwIDkuMzM0IDkuMzMzTTcuNDggMTZhOS4zMzMgOS4zMzMgMCAwIDEgOS4zMzQtOS4zMzNNNy40OCAxNkg0LjgxNW0xMiA5LjMzM0E5LjMzNCA5LjMzNCAwIDAgMCAyNi4xNDggMTZtLTkuMzMzIDkuMzMzVjI4bTkuMzMzLTEyYTkuMzM0IDkuMzM0IDAgMCAwLTkuMzMzLTkuMzMzTTI2LjE0OCAxNmgyLjY2N20tMTItOS4zMzNWNCIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2LjgxNSAxNi42NjdhLjY2Ny42NjcgMCAxIDAgMC0xLjMzNC42NjcuNjY3IDAgMCAwIDAgMS4zMzRaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PHBhdGggZD0iTTcuNDgxIDE2YTkuMzMzIDkuMzMzIDAgMCAwIDkuMzM0IDkuMzMzTTcuNDggMTZhOS4zMzMgOS4zMzMgMCAwIDEgOS4zMzQtOS4zMzNNNy40OCAxNkg0LjgxNW0xMiA5LjMzM0E5LjMzNCA5LjMzNCAwIDAgMCAyNi4xNDggMTZtLTkuMzMzIDkuMzMzVjI4bTkuMzMzLTEyYTkuMzM0IDkuMzM0IDAgMCAwLTkuMzMzLTkuMzMzTTI2LjE0OCAxNmgyLjY2N20tMTItOS4zMzNWNCIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==")
    }

    #muneer-action-big-black-cursor .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI3Ljg3NSAyNC4zNTRhMS41IDEuNSAwIDAgMSAwIDIuMTIxbC0uNTg1LjU4NWExLjUgMS41IDAgMCAxLTIuMTIyIDBsLTYuOTA2LTYuOTA1LS41Mi0uNTItLjI5Mi42NzQtMi43MzUgNi4yOTN2LjAwMmExLjQ4IDEuNDggMCAwIDEtMS4zNjYuODk2aC0uMDg4YTEuNDc4IDEuNDc4IDAgMCAxLTEuMzM2LTEuMDI3bC0uMDAxLS4wMDNMNS4zOSA2LjQ2YTEuNDk0IDEuNDk0IDAgMCAxIDEuODg0LTEuODg1bDIwLjAwNyA2LjUzM2guMDAyYTEuNDk5IDEuNDk5IDAgMCAxIC4xMyAyLjc5NGwtNi4yODkgMi43MzMtLjY3My4yOTMuNTE5LjUyIDYuOTA1IDYuOTA1WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI3Ljg3NSAyNC4zNTRhMS41IDEuNSAwIDAgMSAwIDIuMTIxbC0uNTg1LjU4NWExLjUgMS41IDAgMCAxLTIuMTIyIDBsLTYuOTA2LTYuOTA1LS41Mi0uNTItLjI5Mi42NzQtMi43MzUgNi4yOTN2LjAwMmExLjQ4IDEuNDggMCAwIDEtMS4zNjYuODk2aC0uMDg4YTEuNDc4IDEuNDc4IDAgMCAxLTEuMzM2LTEuMDI3bC0uMDAxLS4wMDNMNS4zOSA2LjQ2YTEuNDk0IDEuNDk0IDAgMCAxIDEuODg0LTEuODg1bDIwLjAwNyA2LjUzM2guMDAyYTEuNDk5IDEuNDk5IDAgMCAxIC4xMyAyLjc5NGwtNi4yODkgMi43MzMtLjY3My4yOTMuNTE5LjUyIDYuOTA1IDYuOTA1WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIi8+PC9zdmc+")
    }

    #muneer-action-big-white-cursor .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTIzLjI0NCAxNi4wMTctLjcyNC4yNzUuNTQ4LjU0NyA1LjE2IDUuMTU4YTIgMiAwIDAgMSAwIDIuODNsLTIuNTg0IDIuNTg0YTIgMiAwIDAgMS0yLjgzIDBsLTUuMTYtNS4xNi0uNTQ3LS41NDgtLjI3NS43MjQtMS42NCA0LjMyMy0uMDIyLjA1NmExLjk3NiAxLjk3NiAwIDAgMS0xLjgyIDEuMTkxaC0uMTE1YTEuOTc2IDEuOTc2IDAgMCAxLTEuNzg1LTEuMzdsLS4wMDEtLjAwMkw0LjkxNiA2LjYxN3YtLjAwMUEyIDIgMCAwIDEgNy40MyA0LjEwMWwyMC4wMDYgNi41MzJoLjAwMWEyIDIgMCAwIDEgLjE4MyAzLjcyM2wtLjA1My4wMjItNC4zMjQgMS42NFptLjYzMSA5LjYzLjM1NC4zNTMuMzUzLS4zNTQgMS44NzktMS44NzkuMzUzLS4zNTMtLjM1My0uMzU0LTUuMzU1LTUuMzU1YTIgMiAwIDAgMSAuNjEzLTMuMjQ2bC4wNTUtLjAyMyAzLjcwMi0xLjQwMyAxLjMyNi0uNTAzLTEuMzQ4LS40NEw3Ljc1NSA2LjMxbC0uOTM2LS4zMDYuMzA2LjkzNiA1Ljc4IDE3LjY5OS40NDEgMS4zNDguNTAzLTEuMzI2IDEuNDA0LTMuNzA1di0uMDAxbC4wMS0uMDI0LjAwMy0uMDA3LjAwMS0uMDAyLjAwNC0uMDA3LjAwMi0uMDA3YTEuOTk5IDEuOTk5IDAgMCAxIDMuMjQ3LS42MTdsNS4zNTUgNS4zNTVaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0ibTIzLjI0NCAxNi4wMTctLjcyNC4yNzUuNTQ4LjU0NyA1LjE2IDUuMTU4YTIgMiAwIDAgMSAwIDIuODNsLTIuNTg0IDIuNTg0YTIgMiAwIDAgMS0yLjgzIDBsLTUuMTYtNS4xNi0uNTQ3LS41NDgtLjI3NS43MjQtMS42NCA0LjMyMy0uMDIyLjA1NmExLjk3NiAxLjk3NiAwIDAgMS0xLjgyIDEuMTkxaC0uMTE1YTEuOTc2IDEuOTc2IDAgMCAxLTEuNzg1LTEuMzdsLS4wMDEtLjAwMkw0LjkxNiA2LjYxN3YtLjAwMUEyIDIgMCAwIDEgNy40MyA0LjEwMWwyMC4wMDYgNi41MzJoLjAwMWEyIDIgMCAwIDEgLjE4MyAzLjcyM2wtLjA1My4wMjItNC4zMjQgMS42NFptLjYzMSA5LjYzLjM1NC4zNTMuMzUzLS4zNTQgMS44NzktMS44NzkuMzUzLS4zNTMtLjM1My0uMzU0LTUuMzU1LTUuMzU1YTIgMiAwIDAgMSAuNjEzLTMuMjQ2bC4wNTUtLjAyMyAzLjcwMi0xLjQwMyAxLjMyNi0uNTAzLTEuMzQ4LS40NEw3Ljc1NSA2LjMxbC0uOTM2LS4zMDYuMzA2LjkzNiA1Ljc4IDE3LjY5OS40NDEgMS4zNDguNTAzLTEuMzI2IDEuNDA0LTMuNzA1di0uMDAxbC4wMS0uMDI0LjAwMy0uMDA3LjAwMS0uMDAyLjAwNC0uMDA3LjAwMi0uMDA3YTEuOTk5IDEuOTk5IDAgMCAxIDMuMjQ3LS42MTdsNS4zNTUgNS4zNTVaIiBmaWxsPSIjMDA2Q0YyIiBzdHJva2U9IiMwMDZDRjIiLz48L3N2Zz4=")
    }

    #muneer-action-text-to-speech .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,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");
      mask-image: url("data:image/svg+xml;base64,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")
    }

    #muneer-action-keyboard-navigation .muneer-icon {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI0LjQ4MiAxN2g0LjY2NnYxMS4zMzNIMTcuODE1VjE3aDYuNjY2Wm0tOC42NjcgMHYxMS4zMzNINC40ODJWMTdoMTEuMzMzWm0tNC42NjctMlYzLjY2N2gxMS4zMzRWMTVIMTEuMTQ4WiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiLz48cGF0aCBkPSJNMTYuODE1IDE2SDMuNDgydjEzLjMzM2gxMy4zMzNtMC0xMy4zMzN2MTMuMzMzbTAtMTMuMzMzaDEzLjMzM3YxMy4zMzNIMTYuODE1bTAtMjIuNjY2VjEybS02LjY2Ny05LjMzM2gxMy4zMzRWMTZIMTAuMTQ4VjIuNjY3WiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjxwYXRoIGQ9Im0xNC4xNDggOS4zMzMgMi42NjctMi42NjYgMi42NjYgMi42NjZtLTEyIDEzLjMzNGg1LjMzNG0tNS4zMzMgMCAyLjY2NiAyLjY2Nm0tMi42NjYtMi42NjZMMTAuMTQ4IDIwbTE2IDIuNjY3aC01LjMzM201LjMzMyAwTDIzLjQ4MiAyMG0yLjY2NiAyLjY2Ny0yLjY2NiAyLjY2NiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzMiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI0LjQ4MiAxN2g0LjY2NnYxMS4zMzNIMTcuODE1VjE3aDYuNjY2Wm0tOC42NjcgMHYxMS4zMzNINC40ODJWMTdoMTEuMzMzWm0tNC42NjctMlYzLjY2N2gxMS4zMzRWMTVIMTEuMTQ4WiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiLz48cGF0aCBkPSJNMTYuODE1IDE2SDMuNDgydjEzLjMzM2gxMy4zMzNtMC0xMy4zMzN2MTMuMzMzbTAtMTMuMzMzaDEzLjMzM3YxMy4zMzNIMTYuODE1bTAtMjIuNjY2VjEybS02LjY2Ny05LjMzM2gxMy4zMzRWMTZIMTAuMTQ4VjIuNjY3WiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjxwYXRoIGQ9Im0xNC4xNDggOS4zMzMgMi42NjctMi42NjYgMi42NjYgMi42NjZtLTEyIDEzLjMzNGg1LjMzNG0tNS4zMzMgMCAyLjY2NiAyLjY2Nm0tMi42NjYtMi42NjZMMTAuMTQ4IDIwbTE2IDIuNjY3aC01LjMzM201LjMzMyAwTDIzLjQ4MiAyMG0yLjY2NiAyLjY2Ny0yLjY2NiAyLjY2NiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==")
    }

    #muneer-accessibility-mode-epilepsy .muneer-mode-title:before {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PG1hc2sgaWQ9ImEiIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0uNzUgMTEuNjI1YTUuMjYgNS4yNiAwIDAgMSAzLTQuNzQ1di0uMTNBNC41IDQuNSAwIDAgMSAxMiA0LjI2N2E0LjUgNC41IDAgMCAxIDguMjUgMi40ODN2LjEzYTUuMjUgNS4yNSAwIDAgMSAwIDkuNDg4di4xMzJBNC41IDQuNSAwIDAgMSAxMiAxOC45ODMgNC41IDQuNSAwIDAgMSAzLjc1IDE2LjV2LS4xMzJhNS4yNTggNS4yNTggMCAwIDEtMy00Ljc0M1ptMTUgNy44NzVhMyAzIDAgMCAwIDIuOTgyLTIuNjc3IDUuMjQ1IDUuMjQ1IDAgMCAxLS43MzIuMDUyaC0uNzVhLjc1Ljc1IDAgMCAxIDAtMS41SDE4YTMuNzUgMy43NSAwIDAgMCAxLjI1LTcuMjg3Ljc1Ljc1IDAgMCAxLS41LS43MDdWNi43NWEzIDMgMCAxIDAtNiAwdjYuNGE0LjQ4MSA0LjQ4MSAwIDAgMSAzLTEuMTUuNzUuNzUgMCAxIDEgMCAxLjUgMyAzIDAgMCAwIDAgNlpNNiAxNS4zNzVoLjc1YS43NS43NSAwIDEgMSAwIDEuNUg2Yy0uMjQ1IDAtLjQ5LS4wMTgtLjczMi0uMDUyQTMgMyAwIDEgMCA4LjI1IDEzLjVhLjc1Ljc1IDAgMSAxIDAtMS41YzEuMTA4IDAgMi4xNzYuNDA5IDMgMS4xNXYtNi40YTMgMyAwIDEgMC02IDB2LjYzYS43NS43NSAwIDAgMS0uNS43MDhBMy43NSAzLjc1IDAgMCAwIDYgMTUuMzc1Wk00LjUgMTAuNWEuNzUuNzUgMCAwIDAgLjc1Ljc1aC4zNzVBMy4zNzUgMy4zNzUgMCAwIDAgOSA3Ljg3NVY3LjVhLjc1Ljc1IDAgMCAwLTEuNSAwdi4zNzVBMS44NzUgMS44NzUgMCAwIDEgNS42MjUgOS43NUg1LjI1YS43NS43NSAwIDAgMC0uNzUuNzVabTEzLjg3NS43NWguMzc1YS43NS43NSAwIDEgMCAwLTEuNWgtLjM3NUExLjg3NSAxLjg3NSAwIDAgMSAxNi41IDcuODc1VjcuNWEuNzUuNzUgMCAxIDAtMS41IDB2LjM3NWEzLjM3NSAzLjM3NSAwIDAgMCAzLjM3NSAzLjM3NVoiLz48L21hc2s+PHBhdGggZD0iTS43NSAxMS42MjVhNS4yNiA1LjI2IDAgMCAxIDMtNC43NDV2LS4xM0E0LjUgNC41IDAgMCAxIDEyIDQuMjY3YTQuNSA0LjUgMCAwIDEgOC4yNSAyLjQ4M3YuMTNhNS4yNSA1LjI1IDAgMCAxIDAgOS40ODh2LjEzMkE0LjUgNC41IDAgMCAxIDEyIDE4Ljk4MyA0LjUgNC41IDAgMCAxIDMuNzUgMTYuNXYtLjEzMmE1LjI1OCA1LjI1OCAwIDAgMS0zLTQuNzQzWm0xNSA3Ljg3NWEzIDMgMCAwIDAgMi45ODItMi42NzcgNS4yNDUgNS4yNDUgMCAwIDEtLjczMi4wNTJoLS43NWEuNzUuNzUgMCAwIDEgMC0xLjVIMThhMy43NSAzLjc1IDAgMCAwIDEuMjUtNy4yODcuNzUuNzUgMCAwIDEtLjUtLjcwN1Y2Ljc1YTMgMyAwIDEgMC02IDB2Ni40YTQuNDgxIDQuNDgxIDAgMCAxIDMtMS4xNS43NS43NSAwIDEgMSAwIDEuNSAzIDMgMCAwIDAgMCA2Wk02IDE1LjM3NWguNzVhLjc1Ljc1IDAgMSAxIDAgMS41SDZjLS4yNDUgMC0uNDktLjAxOC0uNzMyLS4wNTJBMyAzIDAgMSAwIDguMjUgMTMuNWEuNzUuNzUgMCAxIDEgMC0xLjVjMS4xMDggMCAyLjE3Ni40MDkgMyAxLjE1di02LjRhMyAzIDAgMSAwLTYgMHYuNjNhLjc1Ljc1IDAgMCAxLS41LjcwOEEzLjc1IDMuNzUgMCAwIDAgNiAxNS4zNzVaTTQuNSAxMC41YS43NS43NSAwIDAgMCAuNzUuNzVoLjM3NUEzLjM3NSAzLjM3NSAwIDAgMCA5IDcuODc1VjcuNWEuNzUuNzUgMCAwIDAtMS41IDB2LjM3NUExLjg3NSAxLjg3NSAwIDAgMSA1LjYyNSA5Ljc1SDUuMjVhLjc1Ljc1IDAgMCAwLS43NS43NVptMTMuODc1Ljc1aC4zNzVhLjc1Ljc1IDAgMSAwIDAtMS41aC0uMzc1QTEuODc1IDEuODc1IDAgMCAxIDE2LjUgNy44NzVWNy41YS43NS43NSAwIDEgMC0xLjUgMHYuMzc1YTMuMzc1IDMuMzc1IDAgMCAwIDMuMzc1IDMuMzc1WiIgZmlsbD0iIzAwNkNGMiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIG1hc2s9InVybCgjYSkiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,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")
    }

    #muneer-accessibility-mode-visually-impaired .muneer-mode-title:before {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE1IDEyYTMgMyAwIDEgMS02IDAgMyAzIDAgMCAxIDYgMFoiIHN0cm9rZT0iIzAwNkNGMiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48cGF0aCBkPSJNMiAxMmMxLjYtNC4wOTcgNS4zMzYtNyAxMC03czguNCAyLjkwMyAxMCA3Yy0xLjYgNC4wOTctNS4zMzYgNy0xMCA3cy04LjQtMi45MDMtMTAtN1oiIHN0cm9rZT0iIzAwNkNGMiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE1IDEyYTMgMyAwIDEgMS02IDAgMyAzIDAgMCAxIDYgMFoiIHN0cm9rZT0iIzAwNkNGMiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48cGF0aCBkPSJNMiAxMmMxLjYtNC4wOTcgNS4zMzYtNyAxMC03czguNCAyLjkwMyAxMCA3Yy0xLjYgNC4wOTctNS4zMzYgNy0xMCA3cy04LjQtMi45MDMtMTAtN1oiIHN0cm9rZT0iIzAwNkNGMiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48L3N2Zz4=")
    }

    #muneer-accessibility-mode-cognitive-disability .muneer-mode-title:before {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2Ljc1IDZIMjBhMSAxIDAgMCAxIDEgMXYzLjI1YS43NS43NSAwIDAgMS0uNzUuNzVIMjBhMiAyIDAgMCAwIDAgNGguMjVhLjc1Ljc1IDAgMCAxIC43NS43NVYyMGExIDEgMCAwIDEtMSAxaC0zLjI1YS43NS43NSAwIDAgMS0uNzUtLjc1VjIwYTIgMiAwIDAgMC00IDB2LjI1YS43NS43NSAwIDAgMS0uNzUuNzVIN2ExIDEgMCAwIDEtMS0xdi00LjI1YS43NS43NSAwIDAgMC0uNzUtLjc1SDVhMiAyIDAgMSAxIDAtNGguMjVhLjc1Ljc1IDAgMCAwIC43NS0uNzVWN2ExIDEgMCAwIDEgMS0xaDQuMjVhLjc1Ljc1IDAgMCAwIC43NS0uNzVWNWEyIDIgMCAxIDEgNCAwdi4yNWMwIC40MTQuMzM2Ljc1Ljc1Ljc1WiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2Ljc1IDZIMjBhMSAxIDAgMCAxIDEgMXYzLjI1YS43NS43NSAwIDAgMS0uNzUuNzVIMjBhMiAyIDAgMCAwIDAgNGguMjVhLjc1Ljc1IDAgMCAxIC43NS43NVYyMGExIDEgMCAwIDEtMSAxaC0zLjI1YS43NS43NSAwIDAgMS0uNzUtLjc1VjIwYTIgMiAwIDAgMC00IDB2LjI1YS43NS43NSAwIDAgMS0uNzUuNzVIN2ExIDEgMCAwIDEtMS0xdi00LjI1YS43NS43NSAwIDAgMC0uNzUtLjc1SDVhMiAyIDAgMSAxIDAtNGguMjVhLjc1Ljc1IDAgMCAwIC43NS0uNzVWN2ExIDEgMCAwIDEgMS0xaDQuMjVhLjc1Ljc1IDAgMCAwIC43NS0uNzVWNWEyIDIgMCAxIDEgNCAwdi4yNWMwIC40MTQuMzM2Ljc1Ljc1Ljc1WiIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==")
    }

    #muneer-accessibility-mode-adhd-friendly .muneer-mode-title:before {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjYiIGhlaWdodD0iMjIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIxLjIxOSAxMS41YTcuNzE5IDcuNzE5IDAgMSAwLTE1LjQzOCAwIDcuNzE5IDcuNzE5IDAgMCAwIDE1LjQzOCAwWk00IDExLjVhOS41IDkuNSAwIDEgMSAxOSAwIDkuNSA5LjUgMCAwIDEtMTkgMFptOS41LTMuNTYzYTMuNTYzIDMuNTYzIDAgMSAxIDAgNy4xMjYgMy41NjMgMy41NjMgMCAwIDEgMC03LjEyNloiIGZpbGw9IiMwMDZDRjIiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjYiIGhlaWdodD0iMjIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIxLjIxOSAxMS41YTcuNzE5IDcuNzE5IDAgMSAwLTE1LjQzOCAwIDcuNzE5IDcuNzE5IDAgMCAwIDE1LjQzOCAwWk00IDExLjVhOS41IDkuNSAwIDEgMSAxOSAwIDkuNSA5LjUgMCAwIDEtMTkgMFptOS41LTMuNTYzYTMuNTYzIDMuNTYzIDAgMSAxIDAgNy4xMjYgMy41NjMgMy41NjMgMCAwIDEgMC03LjEyNloiIGZpbGw9IiMwMDZDRjIiLz48L3N2Zz4=")
    }

    #muneer-accessibility-mode-blind-users .muneer-mode-title:before {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjYiIGhlaWdodD0iMjIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PG1hc2sgaWQ9ImEiIGZpbGw9IiNmZmYiPjxwYXRoIGQ9Ik0xOC44MTMuMDMxYS45NS45NSAwIDAgMC0uNzUuOTY5djE5YS45NS45NSAwIDEgMCAxLjg3NSAwVjFhLjk1MS45NTEgMCAwIDAtMS4wMzItLjk2OS45NS45NSAwIDAgMC0uMDkzIDBabS0xMiAxYS45NS45NSAwIDAgMC0uNzUuOTY5djE3YS45NS45NSAwIDEgMCAxLjg3NSAwVjJhLjk1Ljk1IDAgMCAwLTEuMDMyLS45NjkuOTU1Ljk1NSAwIDAgMC0uMDkzIDBabTkgM2EuOTUuOTUgMCAwIDAtLjc1Ljk2OXYxMWEuOTUuOTUgMCAxIDAgMS44NzQgMFY1YS45NTIuOTUyIDAgMCAwLTEuMDMtLjk2OS45MzMuOTMzIDAgMCAwLS4wOTQgMFptLTEyIDFhLjk1Ljk1IDAgMCAwLS43NS45Njl2OWEuOTUuOTUgMCAxIDAgMS44NzQgMFY2YS45NS45NSAwIDAgMC0xLjAzLS45NjkuOTMzLjkzMyAwIDAgMC0uMDk0IDBabTYgMWEuOTUuOTUgMCAwIDAtLjc1Ljk2OXY3YS45NS45NSAwIDEgMCAxLjg3NCAwVjdhLjk1Mi45NTIgMCAwIDAtMS4wMy0uOTY5LjkzMy45MzMgMCAwIDAtLjA5NCAwWm0xMiAwYS45NS45NSAwIDAgMC0uNzUuOTY5djdhLjk1Ljk1IDAgMSAwIDEuODc1IDBWN2EuOTUxLjk1MSAwIDAgMC0xLjAzMi0uOTY5Ljk1NS45NTUgMCAwIDAtLjA5MyAwWm0tMjEgMkEuOTUuOTUgMCAwIDAgLjA2MyA5djNhLjk1Ljk1IDAgMSAwIDEuODc1IDBWOWEuOTUuOTUgMCAwIDAtMS4wMzItLjk2OS45NS45NSAwIDAgMC0uMDk0IDBoLjAwMVptMTIgMGEuOTUuOTUgMCAwIDAtLjc1Ljk2OXYzYS45NS45NSAwIDEgMCAxLjg3NCAwVjlhLjk1Mi45NTIgMCAwIDAtMS4wMy0uOTY5LjkzMy45MzMgMCAwIDAtLjA5NCAwWm0xMiAwYS45NS45NSAwIDAgMC0uNzUuOTY5djNhLjk1Ljk1IDAgMSAwIDEuODc1IDBWOWEuOTUxLjk1MSAwIDAgMC0xLjAzMi0uOTY5Ljk1NS45NTUgMCAwIDAtLjA5MyAwWiIvPjwvbWFzaz48cGF0aCBkPSJNMTguODEzLjAzMWEuOTUuOTUgMCAwIDAtLjc1Ljk2OXYxOWEuOTUuOTUgMCAxIDAgMS44NzUgMFYxYS45NTEuOTUxIDAgMCAwLTEuMDMyLS45NjkuOTUuOTUgMCAwIDAtLjA5MyAwWm0tMTIgMWEuOTUuOTUgMCAwIDAtLjc1Ljk2OXYxN2EuOTUuOTUgMCAxIDAgMS44NzUgMFYyYS45NS45NSAwIDAgMC0xLjAzMi0uOTY5Ljk1NS45NTUgMCAwIDAtLjA5MyAwWm05IDNhLjk1Ljk1IDAgMCAwLS43NS45Njl2MTFhLjk1Ljk1IDAgMSAwIDEuODc0IDBWNWEuOTUyLjk1MiAwIDAgMC0xLjAzLS45NjkuOTMzLjkzMyAwIDAgMC0uMDk0IDBabS0xMiAxYS45NS45NSAwIDAgMC0uNzUuOTY5djlhLjk1Ljk1IDAgMSAwIDEuODc0IDBWNmEuOTUuOTUgMCAwIDAtMS4wMy0uOTY5LjkzMy45MzMgMCAwIDAtLjA5NCAwWm02IDFhLjk1Ljk1IDAgMCAwLS43NS45Njl2N2EuOTUuOTUgMCAxIDAgMS44NzQgMFY3YS45NTIuOTUyIDAgMCAwLTEuMDMtLjk2OS45MzMuOTMzIDAgMCAwLS4wOTQgMFptMTIgMGEuOTUuOTUgMCAwIDAtLjc1Ljk2OXY3YS45NS45NSAwIDEgMCAxLjg3NSAwVjdhLjk1MS45NTEgMCAwIDAtMS4wMzItLjk2OS45NTUuOTU1IDAgMCAwLS4wOTMgMFptLTIxIDJBLjk1Ljk1IDAgMCAwIC4wNjMgOXYzYS45NS45NSAwIDEgMCAxLjg3NSAwVjlhLjk1Ljk1IDAgMCAwLTEuMDMyLS45NjkuOTUuOTUgMCAwIDAtLjA5NCAwaC4wMDFabTEyIDBhLjk1Ljk1IDAgMCAwLS43NS45Njl2M2EuOTUuOTUgMCAxIDAgMS44NzQgMFY5YS45NTIuOTUyIDAgMCAwLTEuMDMtLjk2OS45MzMuOTMzIDAgMCAwLS4wOTQgMFptMTIgMGEuOTUuOTUgMCAwIDAtLjc1Ljk2OXYzYS45NS45NSAwIDEgMCAxLjg3NSAwVjlhLjk1MS45NTEgMCAwIDAtMS4wMzItLjk2OS45NTUuOTU1IDAgMCAwLS4wOTMgMFoiIGZpbGw9IiMwMDZDRjIiIHN0cm9rZT0iIzAwNkNGMiIgc3Ryb2tlLXdpZHRoPSIyIiBtYXNrPSJ1cmwoI2EpIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,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")
    }

    #muneer-accessibility-mode-motor-impaired .muneer-mode-title:before {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE0LjgxMSAxNy41QTYuMDAyIDYuMDAyIDAgMCAxIDMgMTZhNi4wMDEgNi4wMDEgMCAwIDEgMy41LTUuNDU2IiBzdHJva2U9IiMwMDZDRjIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PHBhdGggZD0ibTkgNiAxIDkgNy41LS41TDE5IDIwaDEuNSIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjxwYXRoIGQ9Ik0xMi41IDEwaDRNMTEgNGEyIDIgMCAxIDEtNCAwIDIgMiAwIDAgMSA0IDBaIiBzdHJva2U9IiMwMDZDRjIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE0LjgxMSAxNy41QTYuMDAyIDYuMDAyIDAgMCAxIDMgMTZhNi4wMDEgNi4wMDEgMCAwIDEgMy41LTUuNDU2IiBzdHJva2U9IiMwMDZDRjIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PHBhdGggZD0ibTkgNiAxIDkgNy41LS41TDE5IDIwaDEuNSIgc3Ryb2tlPSIjMDA2Q0YyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjxwYXRoIGQ9Ik0xMi41IDEwaDRNMTEgNGEyIDIgMCAxIDEtNCAwIDIgMiAwIDAgMSA0IDBaIiBzdHJva2U9IiMwMDZDRjIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+")
    }

    #muneer-accessibility-mode-colorblind .muneer-mode-title:before {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDIyYy0yLjQ5NCAwLTQuNjE3LS44NzUtNi4zNy0yLjYyNUMzLjg3NSAxNy42MjUgMyAxNS41MDUgMyAxMy4wMTJjMC0xLjI1NS4yMzQtMi40Mi43MDMtMy40OTZhOS4zOTQgOS4zOTQgMCAwIDEgMS45NC0yLjg2N2w1LjE3Ni01LjE2NGExLjk0IDEuOTQgMCAwIDEgLjU2Mi0uMzU2Yy4yMDctLjA4NS40MTMtLjEyOC42MTktLjEyOS4yMDYgMCAuNDEyLjA0My42MTkuMTI5LjIwNi4wODYuMzkzLjIwNS41NjIuMzU2bDUuMTc1IDUuMTY0YTkuNDEgOS40MSAwIDAgMSAxLjk0IDIuODY5Yy40NyAxLjA3NS43MDQgMi4yNC43MDQgMy40OTMgMCAyLjQ5Mi0uODc2IDQuNjEzLTIuNjMgNi4zNjMtMS43NTIgMS43NS0zLjg3NiAyLjYyNS02LjM3IDIuNjI2Wm0wLTIuMjgzVjMuNTExTDcuMjE5IDguMzA0YTYuMjEgNi4yMSAwIDAgMC0xLjQ3NyAyLjE0IDYuNjcxIDYuNjcxIDAgMCAwLS40OTIgMi41NjhjMCAxLjg0NS42NTYgMy40MjQgMS45NjkgNC43MzdDOC41MyAxOS4wNiAxMC4xMjUgMTkuNzE3IDEyIDE5LjcxN1oiIGZpbGw9IiMwMDZDRjIiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDIyYy0yLjQ5NCAwLTQuNjE3LS44NzUtNi4zNy0yLjYyNUMzLjg3NSAxNy42MjUgMyAxNS41MDUgMyAxMy4wMTJjMC0xLjI1NS4yMzQtMi40Mi43MDMtMy40OTZhOS4zOTQgOS4zOTQgMCAwIDEgMS45NC0yLjg2N2w1LjE3Ni01LjE2NGExLjk0IDEuOTQgMCAwIDEgLjU2Mi0uMzU2Yy4yMDctLjA4NS40MTMtLjEyOC42MTktLjEyOS4yMDYgMCAuNDEyLjA0My42MTkuMTI5LjIwNi4wODYuMzkzLjIwNS41NjIuMzU2bDUuMTc1IDUuMTY0YTkuNDEgOS40MSAwIDAgMSAxLjk0IDIuODY5Yy40NyAxLjA3NS43MDQgMi4yNC43MDQgMy40OTMgMCAyLjQ5Mi0uODc2IDQuNjEzLTIuNjMgNi4zNjMtMS43NTIgMS43NS0zLjg3NiAyLjYyNS02LjM3IDIuNjI2Wm0wLTIuMjgzVjMuNTExTDcuMjE5IDguMzA0YTYuMjEgNi4yMSAwIDAgMC0xLjQ3NyAyLjE0IDYuNjcxIDYuNjcxIDAgMCAwLS40OTIgMi41NjhjMCAxLjg0NS42NTYgMy40MjQgMS45NjkgNC43MzdDOC41MyAxOS4wNiAxMC4xMjUgMTkuNzE3IDEyIDE5LjcxN1oiIGZpbGw9IiMwMDZDRjIiLz48L3N2Zz4=")
    }

    #muneer-accessibility-mode-dyslexia-friendly .muneer-mode-title:before {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIgMjEgNy43NiA2LjQyaDEuODZMMTUuMzYgMjFoLTIuODJsLS41Ni0yLjM4SDUuNDJMNC42OCAyMUgyWm05LjM4LTQuNy0yLjctOC4wOEw2IDE2LjNoNS4zOFpNMjUuNTMgMTQuNzZWMjFoLTEuOHYtMS41NmMtLjgyIDEuMi0yIDEuODQtMy43OCAxLjg0LTIuMjIgMC0zLjY2LTEuMzYtMy42Ni0zLjQ2IDAtMi40NiAxLjY2LTMuNzIgNC45Mi0zLjcyaDIuNTJ2LS4xOGMwLTEuNjYtMS0yLjY2LTMuMS0yLjgtMS4yNCAwLTIuNDQuMy0zLjU0Ljl2LTEuNDZjMS4zMi0uNSAyLjU2LS43NiAzLjcyLS43NiAzLjE4IDAgNC43MiAxLjYyIDQuNzIgNC45NlptLTIuOC42NmgtMS42MmMtMi42MiAwLTMuMy41Ni0zLjMyIDEuOS4yIDEuMTIgMS4xNCAxLjIyIDIuNDQgMS4yMiAxLjggMCAyLjUtLjM4IDIuNS0yLjcydi0uNFoiIGZpbGw9IiMwMDZDRjIiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIgMjEgNy43NiA2LjQyaDEuODZMMTUuMzYgMjFoLTIuODJsLS41Ni0yLjM4SDUuNDJMNC42OCAyMUgyWm05LjM4LTQuNy0yLjctOC4wOEw2IDE2LjNoNS4zOFpNMjUuNTMgMTQuNzZWMjFoLTEuOHYtMS41NmMtLjgyIDEuMi0yIDEuODQtMy43OCAxLjg0LTIuMjIgMC0zLjY2LTEuMzYtMy42Ni0zLjQ2IDAtMi40NiAxLjY2LTMuNzIgNC45Mi0zLjcyaDIuNTJ2LS4xOGMwLTEuNjYtMS0yLjY2LTMuMS0yLjgtMS4yNCAwLTIuNDQuMy0zLjU0Ljl2LTEuNDZjMS4zMi0uNSAyLjU2LS43NiAzLjcyLS43NiAzLjE4IDAgNC43MiAxLjYyIDQuNzIgNC45NlptLTIuOC42NmgtMS42MmMtMi42MiAwLTMuMy41Ni0zLjMyIDEuOS4yIDEuMTIgMS4xNCAxLjIyIDIuNDQgMS4yMiAxLjggMCAyLjUtLjM4IDIuNS0yLjcydi0uNFoiIGZpbGw9IiMwMDZDRjIiLz48L3N2Zz4=")
    }

    @keyframes muneer-bounce {

      20%,
      40%,
      60%,
      80%,
      from,
      to {
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
      }

      0% {
        opacity: 0;
        transform: scale3d(.3, .3, .3)
      }

      20% {
        transform: scale3d(1.1, 1.1, 1.1)
      }

      40% {
        transform: scale3d(.9, .9, .9)
      }

      60% {
        opacity: 1;
        transform: scale3d(1.03, 1.03, 1.03)
      }

      80% {
        transform: scale3d(.97, .97, .97)
      }

      to {
        opacity: 1;
        transform: scale3d(1, 1, 1)
      }
    }

    @keyframes muneer-fade {
      from {
        opacity: 0
      }

      to {
        opacity: 1
      }
    }

    @keyframes muneer-fade-out {
      from {
        opacity: 1
      }

      to {
        opacity: 0
      }
    }

    @keyframes muneer-scale {
      0% {
        transform: scale(0);
        opacity: 0
      }

      100% {
        transform: scale(1);
        opacity: 1
      }
    }

    @keyframes muneer-slide-tr {
      from {
        transform: translateX(-100%);
        opacity: 0
      }

      to {
        transform: translateX(0);
        opacity: 1
      }
    }

    @keyframes muneer-slide-tl {
      from {
        transform: translateX(100%);
        opacity: 0
      }

      to {
        transform: translateX(0);
        opacity: 1
      }
    }

    @keyframes muneer-slide-tt {
      from {
        transform: translateY(100%);
        opacity: 0
      }

      to {
        transform: translateY(0);
        opacity: 1
      }
    }

    @keyframes muneer-slide-tt {
      from {
        transform: translateY(100%);
        opacity: 0
      }

      to {
        transform: translateY(0);
        opacity: 1
      }
    }

    @keyframes muneer-slide-tb {
      from {
        transform: translateY(-100%);
        opacity: 0
      }

      to {
        transform: translateY(0);
        opacity: 1
      }
    }

    @keyframes muneer-flip-x {
      from {
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        animation-timing-function: ease-in;
        opacity: 0
      }

      40% {
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        animation-timing-function: ease-in
      }

      60% {
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1
      }

      80% {
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg)
      }

      to {
        transform: perspective(400px)
      }
    }

    @keyframes muneer-flip-y {
      from {
        transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
        animation-timing-function: ease-in;
        opacity: 0
      }

      40% {
        transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
        animation-timing-function: ease-in
      }

      60% {
        transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
        opacity: 1
      }

      80% {
        transform: perspective(400px) rotate3d(0, 1, 0, -5deg)
      }

      to {
        transform: perspective(400px)
      }
    }

    @keyframes muneer-rotate {
      from {
        transform-origin: center;
        transform: rotate3d(0, 0, 1, -200deg);
        opacity: 0
      }

      to {
        transform-origin: center;
        transform: translate3d(0, 0, 0);
        opacity: 1
      }
    }

    @keyframes muneer-wobble {
      from {
        transform: scale3d(1, 1, 1)
      }

      10%,
      20% {
        transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg)
      }

      30%,
      50%,
      70%,
      90% {
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg)
      }

      40%,
      60%,
      80% {
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg)
      }

      to {
        transform: scale3d(1, 1, 1)
      }
    }

    @keyframes muneer-recognition {
      from {
        opacity: 1
      }

      50% {
        opacity: 0
      }

      to {
        opacity: 1
      }
    }

    #muneer-popup-box.muneer-modal-animation-bounce #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-bounce #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-bounce #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-bounce #muneer-voice-nav-popup {
      animation-name: muneer-bounce
    }

    #muneer-popup-box.muneer-modal-animation-fade #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-fade #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-fade #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-fade #muneer-voice-nav-popup {
      animation-name: muneer-fade
    }

    #muneer-popup-box.muneer-modal-animation-flip-x #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-flip-x #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-flip-x #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-flip-x #muneer-voice-nav-popup {
      animation-name: muneer-flip-x;
      backface-visibility: visible !important
    }

    #muneer-popup-box.muneer-modal-animation-flip-y #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-flip-y #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-flip-y #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-flip-y #muneer-voice-nav-popup {
      animation-name: muneer-flip-y;
      backface-visibility: visible !important
    }

    #muneer-popup-box.muneer-modal-animation-scale #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-scale #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-scale #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-scale #muneer-voice-nav-popup {
      animation-name: muneer-scale
    }

    #muneer-popup-box.muneer-modal-animation-slide-tr #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-slide-tr #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-slide-tr #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-slide-tr #muneer-voice-nav-popup {
      animation-name: muneer-slide-tr
    }

    #muneer-popup-box.muneer-modal-animation-slide-tl #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-slide-tl #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-slide-tl #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-slide-tl #muneer-voice-nav-popup {
      animation-name: muneer-slide-tl
    }

    #muneer-popup-box.muneer-modal-animation-slide-tt #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-slide-tt #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-slide-tt #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-slide-tt #muneer-voice-nav-popup {
      animation-name: muneer-slide-tt
    }

    #muneer-popup-box.muneer-modal-animation-slide-tb #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-slide-tb #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-slide-tb #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-slide-tb #muneer-voice-nav-popup {
      animation-name: muneer-slide-tb
    }

    #muneer-popup-box.muneer-modal-animation-rotate #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-rotate #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-rotate #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-rotate #muneer-voice-nav-popup {
      animation-name: muneer-rotate
    }

    #muneer-popup-box.muneer-modal-animation-wobble #muneer-popup,
    #muneer-popup-box.muneer-modal-animation-wobble #muneer-voice-nav-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-wobble #muneer-popup,
    #muneer-voice-nav-popup-box.muneer-modal-animation-wobble #muneer-voice-nav-popup {
      animation-name: muneer-wobble;
      backface-visibility: visible !important
    }

    #muneer-popup-box {
      font-family: var(--muneer-font-family);
      display: none
    }

    #muneer-popup-box.is-open {
      display: block
    }

    #muneer-popup-box.muneer-modal-absolute #muneer-popup {
      position: absolute
    }

    #muneer-popup-box.muneer-modal-fixed #muneer-popup {
      position: fixed
    }

    #muneer-popup-box.muneer-modal-shadow #muneer-popup {
      box-shadow: 0 2px 4px rgba(0, 0, 0, .1), 0 4px 8px rgba(0, 0, 0, .1), 0 8px 16px rgba(0, 0, 0, .05), 0 16px 32px rgba(0, 0, 0, .05)
    }

    #muneer-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 99998;
      background-color: transparent
    }

    #muneer-popup {
      top: 5vh;
      z-index: 99999;
      display: block;
      width: 25vw;
      max-height: 95vh;
      max-width: 450px;
      box-sizing: border-box;
      overflow: hidden;
      border-radius: var(--muneer-border-radius);
      animation-duration: var(--muneer-animate);
      animation-iteration-count: 1;
      animation-direction: normal;
      animation-fill-mode: both
    }

    #muneer-popup[data-start=right] {
      right: 0
    }

    #muneer-popup[data-start=left] {
      left: 0
    }

    #muneer-popup[data-start=center] {
      right: 0;
      left: 0;
      margin: 0 auto
    }

    .muneer-subheader {
      padding: 20px 0 0 0;
      margin: 0;
      display: flex;
      justify-content: space-between;
      align-items: center
    }

    .muneer-subheader.muneer-subheader--accordion {
      padding-bottom: 24px
    }

    .muneer-subheader:nth-child(1) {
      margin-top: 0
    }

    .muneer-subheader h4 {
      font-size: 16px;
      padding: 0;
      margin: 0;
      font-weight: 500
    }

    .muneer-subheader h4 sup {
      font-size: 10px;
      font-weight: 400;
      background: #f9ae40;
      color: #000;
      padding: 2px 4px;
      border-radius: 25px;
      margin: 0 4px
    }

    .muneer-subheader h4 sup.no-one {
      display: none
    }

    #muneer-popup-main {
      overflow: auto;
      box-sizing: border-box;
      padding: 8px 0;
      margin: 0
    }

    #muneer-popup-main::-webkit-scrollbar-track {
      border-radius: 6px
    }

    #muneer-popup-main::-webkit-scrollbar {
      width: 4px
    }

    #muneer-popup-main::-webkit-scrollbar-thumb {
      border-radius: 8px
    }

    #muneer-popup-header {
      display: flex;
      flex-direction: column;
      width: 100%;
      box-sizing: border-box;
      padding-top: 16px
    }

    #muneer-popup-header #muneer-popup-header--settings {
      display: flex;
      justify-content: space-between;
      align-items: center;
      -moz-column-gap: 20px;
      column-gap: 20px
    }

    #muneer-popup-header #muneer-popup-header--title {
      display: flex;
      align-items: center
    }

    #muneer-popup-header #muneer-popup-header--title h1,
    #muneer-popup-header #muneer-popup-header--title h2,
    #muneer-popup-header #muneer-popup-header--title h3,
    #muneer-popup-header #muneer-popup-header--title h4,
    #muneer-popup-header #muneer-popup-header--title h5,
    #muneer-popup-header #muneer-popup-header--title h6 {
      padding: 0;
      margin: 18px 0 0 0;
      font-size: 20px;
      line-height: 32px;
      display: flex;
      -moz-column-gap: 16px;
      column-gap: 16px;
      font-weight: 500
    }

    #muneer-popup-header #muneer-popup-header--title h1:before:not(.popup-title-whitelabel),
    #muneer-popup-header #muneer-popup-header--title h2:before:not(.popup-title-whitelabel),
    #muneer-popup-header #muneer-popup-header--title h3:before:not(.popup-title-whitelabel),
    #muneer-popup-header #muneer-popup-header--title h4:before:not(.popup-title-whitelabel),
    #muneer-popup-header #muneer-popup-header--title h5:before:not(.popup-title-whitelabel),
    #muneer-popup-header #muneer-popup-header--title h6:before:not(.popup-title-whitelabel) {
      content: "";
      width: 32px;
      height: 32px;
      display: inline-flex;
      background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDA4LjY0IDEwNDYuOTUiPjxjaXJjbGUgY3g9IjU0OC4wMyIgY3k9IjE0Ny44OSIgcj0iMTQ3Ljg5IiBzdHlsZT0iZmlsbDojZjlhZTQwIi8+PHBhdGggZD0iTTEwMDguNjQgMTQ5LjE0YzAgMTg0LjU4LTEwOS4xNSAzNDQuMTgtMjY2LjM2IDQxNy41OS00Ny44Mi00MS4zNi0xMDQuODUtNzMuNTItMTY5LjI4LTkyLjY1LTExMi4wMi0zMy4zNS0yMjYuOS0yMi0zMjUuNDEgMjMuOTEtOTguMDMtODQuNTItMTYwLjItMjA5LjU3LTE2MC4yLTM0OC44NWgxOTIuMjRjMCAxNDggMTIwLjM5IDI2OC4zOSAyNjguMzkgMjY4LjM5czI2OC4zOS0xMjAuMzkgMjY4LjM5LTI2OC4zOWgxOTIuMjNaIiBzdHlsZT0iZmlsbDojNTdjZWZiIi8+PHBhdGggZD0ibTg4Mi44NyAxMDQ2Ljk1LTE4NC4yMy01NC44N2M0Mi4zMi0xNDEuNzktMzguNzMtMjkxLjU4LTE4MC41Mi0zMzMuNzgtMTQxLjkxLTQyLjMyLTI5MS41OCAzOC43My0zMzMuOSAxODAuNTJMMCA3ODMuOTVjMzkuMjEtMTMxLjM4IDEzMi4xLTIzMi40IDI0Ny41OS0yODUuOTYgODAuNyA2OS43IDE4NS43OCAxMTEuNzggMzAwLjQzIDExMS43OCA2OS4zNCAwIDEzNS4yMS0xNS40MiAxOTQuMjctNDMuMDQgMTMyLjk0IDExNC44OSAxOTMuOTEgMzAxLjI2IDE0MC41OSA0ODAuMjNaIiBzdHlsZT0iZmlsbDojMDA2Y2YyIi8+PC9zdmc+") no-repeat center center;
      background-size: cover
    }

    .choices-wrapper .choices {
      position: relative;
      overflow: hidden;
      margin-bottom: 24px;
      font-size: 16px
    }

    .choices-wrapper .choices:focus {
      outline: 0
    }

    .choices-wrapper .choices:last-child {
      margin-bottom: 0
    }

    .choices-wrapper .choices.is-open {
      overflow: visible
    }

    .choices-wrapper .choices.is-disabled .choices__inner,
    .choices-wrapper .choices.is-disabled .choices__input {
      background-color: #eaeaea;
      cursor: not-allowed;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none
    }

    .choices-wrapper .choices.is-disabled .choices__item {
      cursor: not-allowed
    }

    .choices-wrapper .choices [hidden] {
      display: none !important
    }

    .choices-wrapper .choices[data-type*=select-one] {
      cursor: pointer
    }

    .choices-wrapper .choices[data-type*=select-one] .choices__inner {
      padding-bottom: 7.5px
    }

    .choices-wrapper .choices[data-type*=select-one] .choices__input {
      display: block;
      width: 100%;
      padding: 10px;
      border-bottom: 1px solid #ddd;
      background-color: #fff;
      margin: 0
    }

    .choices-wrapper .choices[data-type*=select-one] .choices__button {
      background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==");
      padding: 0;
      background-size: 8px;
      position: absolute;
      top: 50%;
      right: 0;
      margin-top: -10px;
      margin-right: 25px;
      height: 20px;
      width: 20px;
      border-radius: 10em;
      opacity: .25
    }

    .choices-wrapper .choices[data-type*=select-one] .choices__button:focus,
    .choices-wrapper .choices[data-type*=select-one] .choices__button:hover {
      opacity: 1
    }

    .choices-wrapper .choices[data-type*=select-one] .choices__button:focus {
      box-shadow: 0 0 0 2px #00bcd4
    }

    .choices-wrapper .choices[data-type*=select-one] .choices__item[data-value=""] .choices__button {
      display: none
    }

    .choices-wrapper .choices[data-type*=select-one]::after {
      content: "";
      height: 0;
      width: 0;
      border-style: solid;
      border-color: #333 transparent transparent transparent;
      border-width: 5px;
      position: absolute;
      right: 11.5px;
      top: 50%;
      margin-top: -2.5px;
      pointer-events: none
    }

    .choices-wrapper .choices[data-type*=select-one].is-open::after {
      border-color: transparent transparent #333 transparent;
      margin-top: -7.5px
    }

    .choices-wrapper .choices[data-type*=select-one][dir=rtl]::after {
      left: 11.5px;
      right: auto
    }

    .choices-wrapper .choices[data-type*=select-one][dir=rtl] .choices__button {
      right: auto;
      left: 0;
      margin-left: 25px;
      margin-right: 0
    }

    .choices-wrapper .choices[data-type*=select-multiple] .choices__inner,
    .choices-wrapper .choices[data-type*=text] .choices__inner {
      cursor: text
    }

    .choices-wrapper .choices[data-type*=select-multiple] .choices__button,
    .choices-wrapper .choices[data-type*=text] .choices__button {
      position: relative;
      display: inline-block;
      margin-top: 0;
      margin-right: -4px;
      margin-bottom: 0;
      margin-left: 8px;
      padding-left: 16px;
      border-left: 1px solid #008fa1;
      background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==");
      background-size: 8px;
      width: 8px;
      line-height: 1;
      opacity: .75;
      border-radius: 0
    }

    .choices-wrapper .choices[data-type*=select-multiple] .choices__button:focus,
    .choices-wrapper .choices[data-type*=select-multiple] .choices__button:hover,
    .choices-wrapper .choices[data-type*=text] .choices__button:focus,
    .choices-wrapper .choices[data-type*=text] .choices__button:hover {
      opacity: 1
    }

    .choices-wrapper .choices__inner {
      display: inline-block;
      vertical-align: top;
      width: 100%;
      background-color: #f9f9f9;
      padding: 7.5px 7.5px 3.75px;
      border: 1px solid #ddd;
      border-radius: 2.5px;
      font-size: 14px;
      min-height: 44px;
      overflow: hidden
    }

    .is-focused .choices-wrapper .choices__inner,
    .is-open .choices-wrapper .choices__inner {
      border-color: #b7b7b7
    }

    .is-open .choices-wrapper .choices__inner {
      border-radius: 2.5px 2.5px 0 0
    }

    .is-flipped.is-open .choices-wrapper .choices__inner {
      border-radius: 0 0 2.5px 2.5px
    }

    .choices-wrapper .choices__list {
      margin: 0;
      padding-left: 0;
      list-style: none
    }

    .choices-wrapper .choices__list--single {
      display: inline-block;
      padding: 4px 16px 4px 4px;
      width: 100%
    }

    [dir=rtl] .choices-wrapper .choices__list--single {
      padding-right: 4px;
      padding-left: 16px
    }

    .choices-wrapper .choices__list--single .choices__item {
      width: 100%
    }

    .choices-wrapper .choices__list--multiple {
      display: inline
    }

    .choices-wrapper .choices__list--multiple .choices__item {
      display: inline-block;
      vertical-align: middle;
      border-radius: 20px;
      padding: 4px 10px;
      font-size: 12px;
      font-weight: 500;
      margin-right: 3.75px;
      margin-bottom: 3.75px;
      background-color: #00bcd4;
      border: 1px solid #00a5bb;
      color: #fff;
      word-break: break-all;
      box-sizing: border-box
    }

    .choices-wrapper .choices__list--multiple .choices__item[data-deletable] {
      padding-right: 5px
    }

    [dir=rtl] .choices-wrapper .choices__list--multiple .choices__item {
      margin-right: 0;
      margin-left: 3.75px
    }

    .choices-wrapper .choices__list--multiple .choices__item.is-highlighted {
      background-color: #00a5bb;
      border: 1px solid #008fa1
    }

    .is-disabled .choices-wrapper .choices__list--multiple .choices__item {
      background-color: #aaa;
      border: 1px solid #919191
    }

    .choices-wrapper .choices__list--dropdown,
    .choices-wrapper .choices__list[aria-expanded] {
      visibility: hidden;
      z-index: 1;
      position: absolute;
      width: 100%;
      background-color: #fff;
      border: 1px solid #ddd;
      top: 100%;
      margin-top: -1px;
      border-bottom-left-radius: 2.5px;
      border-bottom-right-radius: 2.5px;
      overflow: hidden;
      word-break: break-all;
      will-change: visibility
    }

    .choices-wrapper .is-active.choices__list--dropdown,
    .choices-wrapper .is-active.choices__list[aria-expanded] {
      visibility: visible
    }

    .is-open .choices-wrapper .choices__list--dropdown,
    .is-open .choices-wrapper .choices__list[aria-expanded] {
      border-color: #b7b7b7
    }

    .is-flipped .choices-wrapper .choices__list--dropdown,
    .is-flipped .choices-wrapper .choices__list[aria-expanded] {
      top: auto;
      bottom: 100%;
      margin-top: 0;
      margin-bottom: -1px;
      border-radius: .25rem .25rem 0 0
    }

    .choices-wrapper .choices__list--dropdown .choices__list,
    .choices-wrapper .choices__list[aria-expanded] .choices__list {
      position: relative;
      max-height: 300px;
      overflow: auto;
      -webkit-overflow-scrolling: touch;
      will-change: scroll-position
    }

    .choices-wrapper .choices__list--dropdown .choices__item,
    .choices-wrapper .choices__list[aria-expanded] .choices__item {
      position: relative;
      padding: 10px;
      font-size: 14px
    }

    [dir=rtl] .choices-wrapper .choices__list--dropdown .choices__item,
    [dir=rtl] .choices-wrapper .choices__list[aria-expanded] .choices__item {
      text-align: right
    }

    @media (min-width:640px) {

      .choices-wrapper .choices__list--dropdown .choices__item--selectable,
      .choices-wrapper .choices__list[aria-expanded] .choices__item--selectable {
        padding-right: 100px
      }

      .choices-wrapper .choices__list--dropdown .choices__item--selectable::after,
      .choices-wrapper .choices__list[aria-expanded] .choices__item--selectable::after {
        content: attr(data-select-text);
        font-size: 12px;
        opacity: 0;
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%)
      }

      [dir=rtl] .choices-wrapper .choices__list--dropdown .choices__item--selectable,
      [dir=rtl] .choices-wrapper .choices__list[aria-expanded] .choices__item--selectable {
        text-align: right;
        padding-left: 100px;
        padding-right: 10px
      }

      [dir=rtl] .choices-wrapper .choices__list--dropdown .choices__item--selectable::after,
      [dir=rtl] .choices-wrapper .choices__list[aria-expanded] .choices__item--selectable::after {
        right: auto;
        left: 10px
      }
    }

    .choices-wrapper .choices__list--dropdown .choices__item--selectable.is-highlighted,
    .choices-wrapper .choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
      background-color: #f2f2f2
    }

    .choices-wrapper .choices__list--dropdown .choices__item--selectable.is-highlighted::after,
    .choices-wrapper .choices__list[aria-expanded] .choices__item--selectable.is-highlighted::after {
      opacity: .5
    }

    .choices-wrapper .choices__item {
      cursor: default
    }

    .choices-wrapper .choices__item--selectable {
      cursor: pointer
    }

    .choices-wrapper .choices__item--disabled {
      cursor: not-allowed;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none;
      opacity: .5
    }

    .choices-wrapper .choices__heading {
      font-weight: 600;
      font-size: 12px;
      padding: 10px;
      border-bottom: 1px solid #f7f7f7;
      color: gray
    }

    .choices-wrapper .choices__button {
      text-indent: -9999px;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      border: 0;
      background-color: transparent;
      background-repeat: no-repeat;
      background-position: center;
      cursor: pointer
    }

    .choices-wrapper .choices__button:focus {
      outline: 0
    }

    .choices-wrapper .choices__input {
      display: inline-block;
      vertical-align: baseline;
      background-color: #f9f9f9;
      font-size: 14px;
      margin-bottom: 5px;
      border: 0;
      border-radius: 0;
      max-width: 100%;
      padding: 4px 0 4px 2px
    }

    .choices-wrapper .choices__input:focus {
      outline: 0
    }

    .choices-wrapper .choices__input::-webkit-search-cancel-button,
    .choices-wrapper .choices__input::-webkit-search-decoration,
    .choices-wrapper .choices__input::-webkit-search-results-button,
    .choices-wrapper .choices__input::-webkit-search-results-decoration {
      display: none
    }

    .choices-wrapper .choices__input::-ms-clear,
    .choices-wrapper .choices__input::-ms-reveal {
      display: none;
      width: 0;
      height: 0
    }

    [dir=rtl] .choices-wrapper .choices__input {
      padding-right: 2px;
      padding-left: 0
    }

    .choices-wrapper .choices__placeholder {
      opacity: .5
    }

    #muneer-language-switcher {
      flex-grow: 2
    }

    #muneer-language-switcher #muneer-language-switcher--select {
      display: none !important
    }

    #muneer-language-switcher .choices,
    #muneer-language-switcher .choices__inner {
      padding: 0;
      margin: 0
    }

    #muneer-language-switcher .choices__inner {
      border: none;
      background: 0 0;
      border-radius: 0;
      min-height: 36px;
      display: flex;
      align-items: center
    }

    #muneer-language-switcher .choices.is-focused .choices__list.choices__list--single .muneer-language-switcher--lang-flag {
      transition: .4s ease-in-out all
    }

    #muneer-language-switcher .choices.is-focused .choices__list.choices__list--single .muneer-language-switcher--lang-name {
      transition: .4s ease-in-out all
    }

    #muneer-language-switcher .choices__list.choices__list--single {
      padding-left: 2px;
      font-weight: 500
    }

    #muneer-language-switcher .choices[data-type*=select-one]::after {
      border: none
    }

    #muneer-language-switcher .choices__list.choices__list--dropdown {
      box-shadow: 0 0 2.2px rgba(0, 0, 0, .034), 0 0 5.3px rgba(0, 0, 0, .048), 0 0 10px rgba(0, 0, 0, .06), 0 0 17.9px rgba(0, 0, 0, .072), 0 0 33.4px rgba(0, 0, 0, .086);
      border-radius: 8px;
      border: none
    }

    #muneer-language-switcher .muneer-language-switcher--lang {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      -moz-column-gap: 10px;
      column-gap: 10px
    }

    #muneer-language-switcher .muneer-language-switcher--lang-flag {
      width: 28px;
      height: 28px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      border: 2px transparent solid;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center center;
      background-clip: content-box
    }

    #muneer-language-switcher .muneer-language-switcher--lang-flag img {
      height: 100%
    }

    #muneer-popup-close {
      background: 0 0;
      padding: 2px;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      border: 2px solid var(--muneer-color-transparent);
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center
    }

    #muneer-popup-close svg {
      width: 16px;
      height: 16px;
      fill: currentColor;
      display: flex;
      padding: 0;
      margin: 0;
      border: none
    }

    #muneer-focus-holder {
      width: 1px;
      opacity: 0
    }

    #muneer-popup-buttons {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0;
      box-sizing: border-box;
      -moz-column-gap: 20px;
      column-gap: 20px;
      row-gap: 20px
    }

    #muneer-popup-buttons button {
      font-size: 14px;
      border-radius: 10px;
      min-height: 32px;
      padding: 16px;
      flex-grow: 1;
      transition: .2s;
      font-weight: 400;
      text-transform: none;
      letter-spacing: normal;
      cursor: pointer;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      -moz-column-gap: 8px;
      column-gap: 8px
    }

    #muneer-popup-buttons button svg {
      width: 16px;
      height: 16px;
      min-width: 16px;
      min-height: 16px;
      display: inline-flex
    }

    #muneer-popup-buttons button span {
      display: inline-flex
    }

    #muneer-popup-buttons button:focus {
      outline: 0
    }

    #muneer-popup-buttons button:first-child {
      margin-left: 0
    }

    #muneer-popup-buttons button:last-child {
      margin-right: 0
    }

    #muneer-popup-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      margin: 0;
      padding: 16px 32px;
      box-sizing: border-box;
      background: #edf5f8
    }

    #muneer-popup-footer p {
      flex-grow: 2;
      font-size: 11px;
      line-height: 1.25;
      padding: 0;
      margin: 0
    }

    #muneer-popup-footer p a {
      text-decoration: none;
      cursor: pointer;
      border-radius: 4px;
      font-size: 13px
    }

    #muneer-popup-footer img {
      height: 24px;
      max-height: 24px;
      display: inline-flex
    }

    #muneer-popup-box.muneer-non-draggable #muneer-popup-header {
      cursor: default
    }

    #muneer-popup #muneer-popup-header {
      cursor: move
    }

    #muneer-popup #muneer-popup-main {
      max-height: 60vh
    }

    #muneer-sidebar {
      font-family: var(--muneer-font-family);
      height: 100%;
      width: 25vw;
      position: fixed;
      z-index: 99999;
      box-sizing: border-box;
      top: 0;
      overflow-x: hidden;
      transition: .2s;
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      max-width: 450px
    }

    #muneer-sidebar #muneer-popup-header {
      padding-top: 32px;
      padding-bottom: 22px
    }

    #muneer-sidebar.muneer-sidebar-left {
      left: -150vw
    }

    #muneer-sidebar.muneer-sidebar-right {
      right: -150vw
    }

    .muneer-off-canvas-is-open-left {
      margin-left: 25vw;
      transition: margin-left .2s
    }

    .muneer-off-canvas-is-open-right {
      margin-right: 25vw;
      transition: margin-left .2s
    }

    .muneer-aside-is-open #muneer-sidebar.muneer-sidebar-shadow,
    .muneer-off-canvas-is-open-left #muneer-sidebar.muneer-sidebar-shadow,
    .muneer-off-canvas-is-open-right #muneer-sidebar.muneer-sidebar-shadow {
      box-shadow: 0 0 2.2px rgba(0, 0, 0, .034), 0 0 5.3px rgba(0, 0, 0, .048), 0 0 10px rgba(0, 0, 0, .06), 0 0 17.9px rgba(0, 0, 0, .072), 0 0 33.4px rgba(0, 0, 0, .086)
    }

    .muneer-aside-is-open #muneer-sidebar.muneer-sidebar-right,
    .muneer-off-canvas-is-open-right #muneer-sidebar.muneer-sidebar-right {
      right: 0
    }

    .muneer-aside-is-open #muneer-sidebar.muneer-sidebar-left,
    .muneer-off-canvas-is-open-left #muneer-sidebar.muneer-sidebar-left {
      left: 0
    }

    #muneer-action-voice-navigation {
      position: relative
    }

    #muneer-action-voice-navigation #muneer-voice-navigation-auto-hide-popup {
      position: absolute;
      top: 12px;
      right: 12px;
      font-size: 2em;
      display: none
    }

    #muneer-action-voice-navigation #muneer-voice-navigation-auto-hide-popup:checked+label {
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1NzYgNTEyIj48IS0tISBGb250IEF3ZXNvbWUgUHJvIDYuNC4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlIChDb21tZXJjaWFsIExpY2Vuc2UpIENvcHlyaWdodCAyMDIzIEZvbnRpY29ucywgSW5jLiAtLT48cGF0aCBkPSJNMjg4IDgwYy02NS4yIDAtMTE4LjggMjkuNi0xNTkuOSA2Ny43Qzg5LjYgMTgzLjUgNjMgMjI2IDQ5LjQgMjU2YzEzLjYgMzAgNDAuMiA3Mi41IDc4LjYgMTA4LjNDMTY5LjIgNDAyLjQgMjIyLjggNDMyIDI4OCA0MzJzMTE4LjgtMjkuNiAxNTkuOS02Ny43QzQ4Ni40IDMyOC41IDUxMyAyODYgNTI2LjYgMjU2Yy0xMy42LTMwLTQwLjItNzIuNS03OC42LTEwOC4zQzQwNi44IDEwOS42IDM1My4yIDgwIDI4OCA4MHpNOTUuNCAxMTIuNkMxNDIuNSA2OC44IDIwNy4yIDMyIDI4OCAzMnMxNDUuNSAzNi44IDE5Mi42IDgwLjZjNDYuOCA0My41IDc4LjEgOTUuNCA5MyAxMzEuMWMzLjMgNy45IDMuMyAxNi43IDAgMjQuNmMtMTQuOSAzNS43LTQ2LjIgODcuNy05MyAxMzEuMUM0MzMuNSA0NDMuMiAzNjguOCA0ODAgMjg4IDQ4MHMtMTQ1LjUtMzYuOC0xOTIuNi04MC42QzQ4LjYgMzU2IDE3LjMgMzA0IDIuNSAyNjguM2MtMy4zLTcuOS0zLjMtMTYuNyAwLTI0LjZDMTcuMyAyMDggNDguNiAxNTYgOTUuNCAxMTIuNnpNMjg4IDMzNmM0NC4yIDAgODAtMzUuOCA4MC04MHMtMzUuOC04MC04MC04MGMtLjcgMC0xLjMgMC0yIDBjMS4zIDUuMSAyIDEwLjUgMiAxNmMwIDM1LjMtMjguNyA2NC02NCA2NGMtNS41IDAtMTAuOS0uNy0xNi0yYzAgLjcgMCAxLjMgMCAyYzAgNDQuMiAzNS44IDgwIDgwIDgwem0wLTIwOGExMjggMTI4IDAgMSAxIDAgMjU2IDEyOCAxMjggMCAxIDEgMC0yNTZ6Ii8+PC9zdmc+");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1NzYgNTEyIj48IS0tISBGb250IEF3ZXNvbWUgUHJvIDYuNC4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlIChDb21tZXJjaWFsIExpY2Vuc2UpIENvcHlyaWdodCAyMDIzIEZvbnRpY29ucywgSW5jLiAtLT48cGF0aCBkPSJNMjg4IDgwYy02NS4yIDAtMTE4LjggMjkuNi0xNTkuOSA2Ny43Qzg5LjYgMTgzLjUgNjMgMjI2IDQ5LjQgMjU2YzEzLjYgMzAgNDAuMiA3Mi41IDc4LjYgMTA4LjNDMTY5LjIgNDAyLjQgMjIyLjggNDMyIDI4OCA0MzJzMTE4LjgtMjkuNiAxNTkuOS02Ny43QzQ4Ni40IDMyOC41IDUxMyAyODYgNTI2LjYgMjU2Yy0xMy42LTMwLTQwLjItNzIuNS03OC42LTEwOC4zQzQwNi44IDEwOS42IDM1My4yIDgwIDI4OCA4MHpNOTUuNCAxMTIuNkMxNDIuNSA2OC44IDIwNy4yIDMyIDI4OCAzMnMxNDUuNSAzNi44IDE5Mi42IDgwLjZjNDYuOCA0My41IDc4LjEgOTUuNCA5MyAxMzEuMWMzLjMgNy45IDMuMyAxNi43IDAgMjQuNmMtMTQuOSAzNS43LTQ2LjIgODcuNy05MyAxMzEuMUM0MzMuNSA0NDMuMiAzNjguOCA0ODAgMjg4IDQ4MHMtMTQ1LjUtMzYuOC0xOTIuNi04MC42QzQ4LjYgMzU2IDE3LjMgMzA0IDIuNSAyNjguM2MtMy4zLTcuOS0zLjMtMTYuNyAwLTI0LjZDMTcuMyAyMDggNDguNiAxNTYgOTUuNCAxMTIuNnpNMjg4IDMzNmM0NC4yIDAgODAtMzUuOCA4MC04MHMtMzUuOC04MC04MC04MGMtLjcgMC0xLjMgMC0yIDBjMS4zIDUuMSAyIDEwLjUgMiAxNmMwIDM1LjMtMjguNyA2NC02NCA2NGMtNS41IDAtMTAuOS0uNy0xNi0yYzAgLjcgMCAxLjMgMCAyYzAgNDQuMiAzNS44IDgwIDgwIDgwem0wLTIwOGExMjggMTI4IDAgMSAxIDAgMjU2IDEyOCAxMjggMCAxIDEgMC0yNTZ6Ii8+PC9zdmc+")
    }

    #muneer-action-voice-navigation label[for=muneer-voice-navigation-auto-hide-popup] {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 24px;
      height: 24px;
      -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2NDAgNTEyIj48IS0tISBGb250IEF3ZXNvbWUgUHJvIDYuNC4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlIChDb21tZXJjaWFsIExpY2Vuc2UpIENvcHlyaWdodCAyMDIzIEZvbnRpY29ucywgSW5jLiAtLT48cGF0aCBkPSJNMzguOCA1LjFDMjguNC0zLjEgMTMuMy0xLjIgNS4xIDkuMlMtMS4yIDM0LjcgOS4yIDQyLjlsNTkyIDQ2NGMxMC40IDguMiAyNS41IDYuMyAzMy43LTQuMXM2LjMtMjUuNS00LjEtMzMuN0w1MjUuNiAzODYuN2MzOS42LTQwLjYgNjYuNC04Ni4xIDc5LjktMTE4LjRjMy4zLTcuOSAzLjMtMTYuNyAwLTI0LjZjLTE0LjktMzUuNy00Ni4yLTg3LjctOTMtMTMxLjFDNDY1LjUgNjguOCA0MDAuOCAzMiAzMjAgMzJjLTY4LjIgMC0xMjUgMjYuMy0xNjkuMyA2MC44TDM4LjggNS4xem0xNTEgMTE4LjNDMjI2IDk3LjcgMjY5LjUgODAgMzIwIDgwYzY1LjIgMCAxMTguOCAyOS42IDE1OS45IDY3LjdDNTE4LjQgMTgzLjUgNTQ1IDIyNiA1NTguNiAyNTZjLTEyLjYgMjgtMzYuNiA2Ni44LTcwLjkgMTAwLjlsLTUzLjgtNDIuMmM5LjEtMTcuNiAxNC4yLTM3LjUgMTQuMi01OC43YzAtNzAuNy01Ny4zLTEyOC0xMjgtMTI4Yy0zMi4yIDAtNjEuNyAxMS45LTg0LjIgMzEuNWwtNDYuMS0zNi4xek0zOTQuOSAyODQuMmwtODEuNS02My45YzQuMi04LjUgNi42LTE4LjIgNi42LTI4LjNjMC01LjUtLjctMTAuOS0yLTE2Yy43IDAgMS4zIDAgMiAwYzQ0LjIgMCA4MCAzNS44IDgwIDgwYzAgOS45LTEuOCAxOS40LTUuMSAyOC4yem05LjQgMTMwLjNDMzc4LjggNDI1LjQgMzUwLjcgNDMyIDMyMCA0MzJjLTY1LjIgMC0xMTguOC0yOS42LTE1OS45LTY3LjdDMTIxLjYgMzI4LjUgOTUgMjg2IDgxLjQgMjU2YzguMy0xOC40IDIxLjUtNDEuNSAzOS40LTY0LjhMODMuMSAxNjEuNUM2MC4zIDE5MS4yIDQ0IDIyMC44IDM0LjUgMjQzLjdjLTMuMyA3LjktMy4zIDE2LjcgMCAyNC42YzE0LjkgMzUuNyA0Ni4yIDg3LjcgOTMgMTMxLjFDMTc0LjUgNDQzLjIgMjM5LjIgNDgwIDMyMCA0ODBjNDcuOCAwIDg5LjktMTIuOSAxMjYuMi0zMi41bC00MS45LTMzek0xOTIgMjU2YzAgNzAuNyA1Ny4zIDEyOCAxMjggMTI4YzEzLjMgMCAyNi4xLTIgMzguMi01LjhMMzAyIDMzNGMtMjMuNS01LjQtNDMuMS0yMS4yLTUzLjctNDIuM2wtNTYuMS00NC4yYy0uMiAyLjgtLjMgNS42LS4zIDguNXoiLz48L3N2Zz4=");
      mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2NDAgNTEyIj48IS0tISBGb250IEF3ZXNvbWUgUHJvIDYuNC4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlIChDb21tZXJjaWFsIExpY2Vuc2UpIENvcHlyaWdodCAyMDIzIEZvbnRpY29ucywgSW5jLiAtLT48cGF0aCBkPSJNMzguOCA1LjFDMjguNC0zLjEgMTMuMy0xLjIgNS4xIDkuMlMtMS4yIDM0LjcgOS4yIDQyLjlsNTkyIDQ2NGMxMC40IDguMiAyNS41IDYuMyAzMy43LTQuMXM2LjMtMjUuNS00LjEtMzMuN0w1MjUuNiAzODYuN2MzOS42LTQwLjYgNjYuNC04Ni4xIDc5LjktMTE4LjRjMy4zLTcuOSAzLjMtMTYuNyAwLTI0LjZjLTE0LjktMzUuNy00Ni4yLTg3LjctOTMtMTMxLjFDNDY1LjUgNjguOCA0MDAuOCAzMiAzMjAgMzJjLTY4LjIgMC0xMjUgMjYuMy0xNjkuMyA2MC44TDM4LjggNS4xem0xNTEgMTE4LjNDMjI2IDk3LjcgMjY5LjUgODAgMzIwIDgwYzY1LjIgMCAxMTguOCAyOS42IDE1OS45IDY3LjdDNTE4LjQgMTgzLjUgNTQ1IDIyNiA1NTguNiAyNTZjLTEyLjYgMjgtMzYuNiA2Ni44LTcwLjkgMTAwLjlsLTUzLjgtNDIuMmM5LjEtMTcuNiAxNC4yLTM3LjUgMTQuMi01OC43YzAtNzAuNy01Ny4zLTEyOC0xMjgtMTI4Yy0zMi4yIDAtNjEuNyAxMS45LTg0LjIgMzEuNWwtNDYuMS0zNi4xek0zOTQuOSAyODQuMmwtODEuNS02My45YzQuMi04LjUgNi42LTE4LjIgNi42LTI4LjNjMC01LjUtLjctMTAuOS0yLTE2Yy43IDAgMS4zIDAgMiAwYzQ0LjIgMCA4MCAzNS44IDgwIDgwYzAgOS45LTEuOCAxOS40LTUuMSAyOC4yem05LjQgMTMwLjNDMzc4LjggNDI1LjQgMzUwLjcgNDMyIDMyMCA0MzJjLTY1LjIgMC0xMTguOC0yOS42LTE1OS45LTY3LjdDMTIxLjYgMzI4LjUgOTUgMjg2IDgxLjQgMjU2YzguMy0xOC40IDIxLjUtNDEuNSAzOS40LTY0LjhMODMuMSAxNjEuNUM2MC4zIDE5MS4yIDQ0IDIyMC44IDM0LjUgMjQzLjdjLTMuMyA3LjktMy4zIDE2LjcgMCAyNC42YzE0LjkgMzUuNyA0Ni4yIDg3LjcgOTMgMTMxLjFDMTc0LjUgNDQzLjIgMjM5LjIgNDgwIDMyMCA0ODBjNDcuOCAwIDg5LjktMTIuOSAxMjYuMi0zMi41bC00MS45LTMzek0xOTIgMjU2YzAgNzAuNyA1Ny4zIDEyOCAxMjggMTI4YzEzLjMgMCAyNi4xLTIgMzguMi01LjhMMzAyIDMzNGMtMjMuNS01LjQtNDMuMS0yMS4yLTUzLjctNDIuM2wtNTYuMS00NC4yYy0uMiAyLjgtLjMgNS42LS4zIDguNXoiLz48L3N2Zz4=");
      -webkit-mask-position: center;
      mask-position: center;
      -webkit-mask-repeat: no-repeat;
      mask-repeat: no-repeat;
      -webkit-mask-size: contain;
      mask-size: contain;
      background-color: var(--muneer-color)
    }

    #muneer-action-voice-navigation label[for=muneer-voice-navigation-auto-hide-popup]:focus {
      transform: scale(1.2);
      transition-duration: .2s
    }

    #muneer-action-voice-navigation:focus label[for=muneer-voice-navigation-auto-hide-popup],
    #muneer-action-voice-navigation:hover label[for=muneer-voice-navigation-auto-hide-popup] {
      background-color: var(--muneer-text)
    }

    #muneer-action-voice-navigation.active label[for=muneer-voice-navigation-auto-hide-popup] {
      background-color: var(--muneer-bg) !important
    }

    .muneer-invisible-label {
      font-size: 0;
      line-height: 0;
      opacity: 0;
      display: block
    }

    @media screen and (max-width:1400px) {
      #muneer-sidebar {
        width: 40vw
      }

      .muneer-off-canvas-is-open-left {
        margin-left: 40vw
      }

      .muneer-off-canvas-is-open-right {
        margin-right: 40vw
      }
    }

    @media screen and (max-width:1000px) {
      #muneer-sidebar {
        width: 50vw
      }

      .muneer-off-canvas-is-open-left {
        margin-left: 50vw
      }

      .muneer-off-canvas-is-open-right {
        margin-right: 50vw
      }
    }

    @media screen and (max-width:680px) {
      #muneer-sidebar {
        width: 85vw
      }
    }

    @media screen and (max-width:480px) {
      #muneer-popup-box.muneer-modal-shadow #muneer-popup {
        box-shadow: 0 0 2.2px rgba(0, 0, 0, .034), 0 0 5.3px rgba(0, 0, 0, .048), 0 0 10px rgba(0, 0, 0, .06)
      }

      #muneer-popup {
        width: 100%;
        max-width: 100%;
        min-width: 100%;
        max-height: 80vh;
        left: 0 !important;
        bottom: 0;
        top: unset !important
      }

      #muneer-popup #muneer-popup-main {
        max-height: 40vh
      }

      #muneer-popup-tools-box {
        display: none !important
      }

      #muneer-sidebar {
        width: 100%;
        max-width: 100%
      }

      #muneer-popup #muneer-easy-orientation-box,
      #muneer-popup #muneer-readable-experience-box,
      #muneer-popup #muneer-visually-pleasing-box,
      #muneer-sidebar #muneer-easy-orientation-box,
      #muneer-sidebar #muneer-readable-experience-box,
      #muneer-sidebar #muneer-visually-pleasing-box {
        grid-template-columns: 1fr 1fr
      }

      #muneer-popup .muneer-mode-description,
      #muneer-sidebar .muneer-mode-description {
        display: none
      }

      #muneer-popup .muneer-action-box,
      #muneer-sidebar .muneer-action-box {
        width: 100%
      }

      #muneer-popup .muneer-action-box.muneer-palette-box,
      #muneer-sidebar .muneer-action-box.muneer-palette-box {
        width: unset
      }

      #muneer-popup #muneer-popup-close:focus,
      #muneer-sidebar #muneer-popup-close:focus {
        outline: 0 !important
      }

      #muneer-popup #muneer-popup-buttons button,
      #muneer-sidebar #muneer-popup-buttons button {
        width: 100%;
        margin: 0
      }

      #muneer-popup .muneer-title,
      #muneer-sidebar .muneer-title {
        font-size: 14px
      }

      #muneer-popup .muneer-mode-short,
      #muneer-sidebar .muneer-mode-short {
        display: none
      }

      #muneer-voice-nav-popup-box #muneer-voice-nav-popup {
        width: 100vw !important
      }
    }

    [dir=rtl] #muneer-popup {
      font-family: var(--muneer-font-family-rtl) !important
    }

    [dir=rtl] #muneer-useful-links {
      padding-right: 35px
    }

    #muneer-popup select,
    #muneer-sidebar select {
      display: inline-flex !important
    }

    .muneer-trigger-button-box {
      position: fixed;
      z-index: 99998;
      margin: var(--muneer-btn-margin)
    }

    .muneer-trigger-button-box.top-left {
      top: 0;
      left: 0
    }

    .muneer-trigger-button-box.top-right {
      top: 0;
      right: 0
    }

    .muneer-trigger-button-box.left-center {
      top: 50%;
      left: 0;
      transform: translate(0, -50%)
    }

    .muneer-trigger-button-box.right-center {
      top: 50%;
      right: 0;
      transform: translate(0, -50%)
    }

    .muneer-trigger-button-box.bottom-left {
      bottom: 0;
      left: 0
    }

    .muneer-trigger-button-box.bottom-center {
      bottom: 0;
      left: 50%;
      transform: translate(-50%, 0)
    }

    .muneer-trigger-button-box.bottom-right {
      bottom: 0;
      right: 0
    }

    .muneer-trigger-button-box.entrance-bounce {
      animation-fill-mode: both;
      animation-name: muneer-bounce;
      animation-duration: .6s;
      animation-delay: var(--muneer-btn-delay)
    }

    .muneer-trigger-button-box.entrance-fade {
      animation-fill-mode: both;
      animation-name: muneer-fade;
      animation-duration: .6s;
      animation-delay: var(--muneer-btn-delay)
    }

    .muneer-trigger-button-box.entrance-flip-x {
      animation-fill-mode: both;
      animation-name: muneer-flip-x;
      animation-duration: .8s;
      animation-delay: var(--muneer-btn-delay);
      backface-visibility: visible !important
    }

    .muneer-trigger-button-box.entrance-flip-y {
      animation-fill-mode: both;
      animation-name: muneer-flip-y;
      animation-duration: .8s;
      animation-delay: var(--muneer-btn-delay);
      backface-visibility: visible !important
    }

    .muneer-trigger-button-box.entrance-scale {
      animation-fill-mode: both;
      animation-name: muneer-scale;
      animation-duration: .4s;
      animation-delay: var(--muneer-btn-delay)
    }

    .muneer-trigger-button-box.entrance-wobble {
      animation-fill-mode: both;
      animation-name: muneer-wobble;
      animation-duration: .5s;
      animation-delay: var(--muneer-btn-delay);
      backface-visibility: visible !important
    }

    .muneer-trigger-button-box.entrance-rotate {
      animation-fill-mode: both;
      animation-name: muneer-rotate;
      animation-duration: .8s;
      animation-delay: var(--muneer-btn-delay)
    }

    .muneer-trigger-button-box.hover-bounce button:hover span,
    .muneer-trigger-button-box.hover-fade button:hover span,
    .muneer-trigger-button-box.hover-flip-x button:hover span,
    .muneer-trigger-button-box.hover-flip-y button:hover span,
    .muneer-trigger-button-box.hover-rotate button:hover span,
    .muneer-trigger-button-box.hover-scale button:hover span,
    .muneer-trigger-button-box.hover-wobble button:hover span {
      animation-fill-mode: both
    }

    .muneer-trigger-button-box.hover-bounce button:hover span:nth-child(2),
    .muneer-trigger-button-box.hover-fade button:hover span:nth-child(2),
    .muneer-trigger-button-box.hover-flip-x button:hover span:nth-child(2),
    .muneer-trigger-button-box.hover-flip-y button:hover span:nth-child(2),
    .muneer-trigger-button-box.hover-rotate button:hover span:nth-child(2),
    .muneer-trigger-button-box.hover-scale button:hover span:nth-child(2),
    .muneer-trigger-button-box.hover-wobble button:hover span:nth-child(2) {
      animation-delay: .1s
    }

    .muneer-trigger-button-box.hover-bounce button:hover span {
      animation-name: muneer-bounce;
      animation-duration: .6s
    }

    .muneer-trigger-button-box.hover-fade button:hover span {
      animation-name: muneer-fade;
      animation-duration: .6s
    }

    .muneer-trigger-button-box.hover-flip-x button:hover span {
      animation-name: muneer-flip-x;
      animation-duration: .8s;
      backface-visibility: visible !important
    }

    .muneer-trigger-button-box.hover-flip-y button:hover span {
      animation-name: muneer-flip-y;
      animation-duration: .8s;
      backface-visibility: visible !important
    }

    .muneer-trigger-button-box.hover-scale button:hover span {
      animation-name: muneer-scale;
      animation-duration: .4s
    }

    .muneer-trigger-button-box.hover-rotate button:hover span {
      animation-name: muneer-rotate;
      animation-duration: .5s
    }

    .muneer-trigger-button-box.hover-wobble button:hover span {
      animation-name: muneer-wobble;
      animation-duration: .5s;
      backface-visibility: visible !important
    }

    .muneer-trigger-button-box button {
      display: flex;
      align-items: center;
      outline: 0;
      padding: var(--muneer-btn-padding);
      border-radius: var(--muneer-btn-radius);
      color: var(--muneer-btn-color);
      background: var(--muneer-btn-bg);
      font-size: var(--muneer-btn-size);
      transition: .4s ease-in-out all;
      border: none;
      cursor: pointer
    }

    .muneer-trigger-button-box button:active,
    .muneer-trigger-button-box button:focus,
    .muneer-trigger-button-box button:focus-visible,
    .muneer-trigger-button-box button:hover {
      color: var(--muneer-btn-color-hover);
      background: var(--muneer-btn-bg-hover)
    }

    .muneer-trigger-button-box button:active .muneer-trigger-button-icon svg,
    .muneer-trigger-button-box button:focus .muneer-trigger-button-icon svg,
    .muneer-trigger-button-box button:focus-visible .muneer-trigger-button-icon svg,
    .muneer-trigger-button-box button:hover .muneer-trigger-button-icon svg {
      fill: var(--muneer-btn-color-hover)
    }

    .muneer-trigger-button-box button#muneer-trigger-button span:nth-child(2) {
      padding: calc(var(--muneer-btn-padding)/ 2)
    }

    .muneer-trigger-button-box button#muneer-trigger-button:focus {
      outline: 0;
      color: var(--muneer-btn-color-hover);
      background: var(--muneer-btn-bg-hover);
      box-shadow: 0 0 0 3px var(--muneer-btn-color-hover)
    }

    .muneer-trigger-button-box button .muneer-trigger-button-icon svg {
      fill: var(--muneer-btn-color);
      width: var(--muneer-btn-size);
      height: var(--muneer-btn-size)
    }

    .muneer-trigger-button-box button:focus,
    .muneer-trigger-button-box button:hover {
      text-decoration: none
    }

    .muneer-trigger-button-box button .muneer-button-icon {
      display: flex;
      align-self: center
    }

    .muneer-trigger-button-box button.icon-position-before {
      flex-direction: row
    }

    .muneer-trigger-button-box button.icon-position-before span:nth-child(2) {
      padding-top: 0 !important;
      padding-right: 0 !important;
      padding-bottom: 0 !important
    }

    .muneer-trigger-button-box button.icon-position-after {
      flex-direction: row-reverse
    }

    .muneer-trigger-button-box button.icon-position-after span:nth-child(2) {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
      padding-left: 0 !important
    }

    .muneer-trigger-button-box button.icon-position-above {
      flex-direction: column
    }

    .muneer-trigger-button-box button.icon-position-above span:nth-child(2) {
      padding-right: 0 !important;
      padding-bottom: 0 !important;
      padding-left: 0 !important
    }

    .muneer-trigger-button-box button.icon-position-bellow {
      flex-direction: column-reverse
    }

    .muneer-trigger-button-box button.icon-position-bellow span:nth-child(2) {
      padding-top: 0 !important;
      padding-right: 0 !important;
      padding-left: 0 !important
    }

    .muneer-trigger-button-box button span {
      display: flex;
      transition: all .15s linear
    }

    .muneer-trigger-button-box button span path {
      transition: all .15s linear
    }

    .muneer-simple-keyboard {
      width: 100%;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none;
      box-sizing: border-box;
      overflow: hidden;
      touch-action: manipulation;
      font-family: HelveticaNeue-Light, "Helvetica Neue Light", "Helvetica Neue", Helvetica, Arial, "Lucida Grande", sans-serif;
      padding: 5px;
      border-radius: 5px
    }

    .muneer-simple-keyboard .hg-button span {
      pointer-events: none
    }

    .muneer-simple-keyboard button.hg-button {
      border-width: 0;
      outline: 0;
      font-size: inherit
    }

    .muneer-simple-keyboard .hg-button {
      display: inline-block;
      flex-grow: 1
    }

    .muneer-simple-keyboard .hg-row {
      display: flex
    }

    .muneer-simple-keyboard .hg-row:not(:last-child) {
      margin-bottom: 5px
    }

    .muneer-simple-keyboard .hg-row .hg-button-container,
    .muneer-simple-keyboard .hg-row .hg-button:not(:last-child) {
      margin-right: 5px
    }

    .muneer-simple-keyboard .hg-row>div:last-child {
      margin-right: 0
    }

    .muneer-simple-keyboard .hg-row .hg-button-container {
      display: flex
    }

    .muneer-simple-keyboard .hg-button {
      height: 40px;
      border-radius: 5px;
      box-sizing: border-box;
      padding: 5px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center
    }

    .muneer-simple-keyboard.hg-layout-numeric .hg-button {
      width: 33.3%;
      height: 60px;
      align-items: center;
      display: flex;
      justify-content: center
    }

    .muneer-simple-keyboard .hg-button.hg-button-numpadadd,
    .muneer-simple-keyboard .hg-button.hg-button-numpadenter {
      height: 85px
    }

    .muneer-simple-keyboard .hg-button.hg-button-numpad0 {
      width: 105px
    }

    .muneer-simple-keyboard .hg-button.hg-button-com {
      max-width: 85px
    }

    .muneer-simple-keyboard .hg-button.hg-standardBtn.hg-button-at {
      max-width: 45px
    }

    .muneer-simple-keyboard .hg-button.hg-standardBtn[data-skbtn=".com"] {
      max-width: 82px
    }

    .muneer-simple-keyboard .hg-button.hg-standardBtn[data-skbtn="@"] {
      max-width: 60px
    }

    #muneer-keyboard-box {
      display: none;
      flex-direction: column;
      align-items: end;
      position: fixed;
      width: 650px;
      bottom: 0;
      left: 0;
      padding: 35px 8px 8px;
      cursor: move;
      box-shadow: 0 0 2px #333;
      border-radius: 4px;
      z-index: 99999
    }

    #muneer-keyboard-box.obsessive-keyboard {
      padding-top: 8px
    }

    #muneer-keyboard-box[aria-hidden=true] {
      display: none !important
    }

    #muneer-keyboard-box[aria-hidden=false] {
      display: flex !important
    }

    #muneer-keyboard-close {
      height: 40px;
      width: 40px;
      border-radius: 5px;
      margin: 4px;
      box-sizing: border-box;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none !important
    }

    @media screen and (max-width:980px) {
      #muneer-keyboard-box {
        display: none !important
      }
    }

    #muneer-accessibility-statement-box {
      transition: opacity .4s ease-out;
      opacity: 0;
      height: 0;
      overflow: hidden
    }

    #muneer-accessibility-statement-box.muneer-accessibility-statement-iframe {
      display: none
    }

    #muneer-accessibility-statement-box.open {
      opacity: 1;
      height: auto;
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      padding: 32px;
      z-index: 999;
      overflow-y: scroll
    }

    #muneer-accessibility-statement-box.open.muneer-accessibility-statement-iframe {
      display: block;
      padding: 0;
      overflow: hidden
    }

    #muneer-accessibility-statement-box.open.muneer-accessibility-statement-iframe .muneer-statement-content {
      display: contents
    }

    #muneer-accessibility-statement-box.open.muneer-accessibility-statement-iframe iframe {
      border: none;
      width: 100%;
      height: 100%
    }

    #muneer-accessibility-statement-box.open::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .2);
      background-color: #f5f5f5
    }

    #muneer-accessibility-statement-box.open::-webkit-scrollbar {
      width: 12px;
      background-color: #f5f5f5
    }

    #muneer-accessibility-statement-box.open::-webkit-scrollbar-thumb {
      -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .2);
      background-color: #a7a7a7
    }

    #muneer-accessibility-statement-box.open #muneer-close-statement-btn {
      position: absolute;
      top: 32px;
      right: 32px;
      background: 0 0;
      border: 0;
      padding: 2px;
      border-radius: 50%
    }

    #muneer-accessibility-statement-box.open #muneer-close-statement-btn:before {
      content: "✕";
      width: 16px;
      height: 16px;
      display: flex;
      justify-content: center;
      align-items: center
    }

    button.muneer-subheader--trigger {
      width: 24px;
      height: 24px;
      min-width: 24px;
      min-height: 24px;
      max-height: 24px;
      max-width: 24px;
      overflow: hidden;
      background: var(--muneer-text);
      color: var(--muneer-background);
      border-radius: 50%;
      border: none;
      transition: all .25s ease-out;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      padding: 0;
      margin: 0
    }

    button.muneer-subheader--trigger svg {
      width: 16px;
      height: 16px;
      min-width: 16px;
      min-height: 16px;
      max-height: 16px;
      max-width: 16px;
      display: inline-flex
    }

    button.muneer-subheader--trigger[aria-expanded=true] {
      transform: rotate(180deg)
    }

    button.muneer-subheader--trigger[aria-expanded=false] {
      transform: rotate(0)
    }

    button.muneer-subheader--trigger:hover {
      background: var(--muneer-color-accent);
      color: var(--muneer-background);
      transition: all .25s ease-out
    }

    button.muneer-subheader--trigger:focus {
      outline: 2px solid var(--muneer-color-accent);
      outline-offset: 4px;
      transition: all .25s ease-out
    }

    #muneer-theme-switcher {
      padding: 0;
      margin: 0
    }

    #muneer-theme-switcher fieldset {
      padding: 0;
      margin: 0;
      border-radius: 40px
    }

    #muneer-theme-switcher legend {
      font-size: 0;
      line-height: 0;
      opacity: 0;
      padding: 0;
      margin: 0
    }

    #muneer-theme-switcher label {
      width: 32px;
      height: 32px;
      display: inline-flex;
      border-radius: 16px;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: background .3s ease-in-out
    }

    #muneer-theme-switcher svg {
      width: 16px;
      height: 16px;
      fill: currentColor;
      transition: fill .3s ease-in-out;
      display: flex;
      padding: 0;
      margin: 0;
      border: none
    }

    #muneer-theme-switcher input[type=radio] {
      opacity: 0;
      position: absolute;
      width: 0;
      height: 0
    }

    #muneer-theme-switcher input[type=radio]:checked+label {
      transition: background .3s ease-in-out
    }

    #muneer-theme-switcher input[type=radio]:checked:focus+label {
      z-index: 9;
      position: relative
    }

    #muneer-online-dictionary-box {
      position: relative
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-form {
      position: relative
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search {
      box-sizing: border-box;
      width: 100%;
      margin: 0;
      line-height: 16px;
      border-radius: 8px;
      transition: .25s ease-in-out all;
      min-height: 45px;
      border: none;
      padding: 0 48px;
      font-size: 16px;
      outline: 0
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search::-moz-placeholder {
      font-size: 14px
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search::placeholder {
      font-size: 14px
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search:focus {
      outline: 0
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-form label {
      color: var(--muneer-color);
      position: absolute;
      top: 0;
      left: 16px;
      height: 100%;
      display: flex;
      align-items: center;
      font-size: 0
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-form label svg {
      width: 20px;
      height: 20px;
      min-width: 16px;
      min-height: 16px
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul {
      list-style: none;
      padding: 0;
      margin: 0
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li {
      margin-top: 32px
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li p {
      margin: 0 0 6px 0
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li a {
      font-size: 14px
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-search-close {
      display: none;
      position: absolute;
      cursor: pointer;
      background: 0 0;
      border: none;
      outline: 0;
      padding: 0;
      right: 16px;
      top: 0;
      height: 100%;
      align-items: center;
      opacity: .3;
      color: var(--muneer-color)
    }

    #muneer-online-dictionary-box #muneer-online-dictionary-search-close:hover {
      opacity: 1
    }

    #muneer-popup-tools-box {
      display: flex
    }

    #muneer-popup-tools-box .muneer-row {
      padding: 0
    }

    #muneer-tool-oversize {
      width: 100%
    }

    .muneer-oversize #muneer-easy-orientation-box,
    .muneer-oversize #muneer-readable-experience-box,
    .muneer-oversize #muneer-visually-pleasing-box {
      grid-template-columns: 1fr 1fr
    }

    .muneer-oversize .muneer-action-box-content .muneer-icon {
      width: 48px;
      height: 48px
    }

    .muneer-oversize .muneer-action-box-content .muneer-title {
      font-size: 14px
    }

    #muneer-accessibility-modes-box {
      margin: -24px 0;
      display: flex;
      flex-direction: column
    }

    #muneer-accessibility-modes-box .muneer-accessibility-mode-item {
      cursor: pointer;
      width: 100%
    }

    #muneer-accessibility-modes-box .muneer-accessibility-mode-item:last-of-type {
      padding-bottom: 20px
    }

    #muneer-accessibility-modes-box .muneer-accessibility-mode-item:focus {
      outline: 0
    }

    #muneer-accessibility-modes-box .muneer-accessibility-mode-item .muneer-mode-description {
      max-height: 0;
      overflow: hidden;
      font-size: 14px;
      line-height: 1.4;
      opacity: 0;
      padding: 0 20px;
      transition: all .15s ease-out;
      border-radius: 12px
    }

    #muneer-accessibility-modes-box .muneer-accessibility-mode-item.active .muneer-mode-description {
      height: auto;
      margin: 0;
      opacity: 1;
      padding: 20px;
      transition: all .15s ease-out;
      max-height: 600px
    }

    #muneer-visually-pleasing-box {
      margin: 0;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      -moz-column-gap: 20px;
      column-gap: 20px;
      row-gap: 20px
    }

    #muneer-visually-pleasing-box #muneer-action-background-colors,
    #muneer-visually-pleasing-box #muneer-action-text-colors,
    #muneer-visually-pleasing-box #muneer-action-title-colors {
      width: 100%
    }

    #muneer-colorizing-box {
      margin: 0;
      display: flex;
      flex-direction: column;
      row-gap: 20px
    }

    #muneer-colorizing-box .muneer-action-box {
      border-radius: 12px
    }

    #muneer-readable-experience-box {
      margin: 0;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      -moz-column-gap: 20px;
      column-gap: 20px;
      row-gap: 20px
    }

    #muneer-easy-orientation-box {
      margin: 0;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      -moz-column-gap: 20px;
      column-gap: 20px;
      row-gap: 20px
    }

    #muneer-useful-links-box {
      margin: 0;
      display: flex
    }

    #muneer-useful-links-box .muneer-action-box {
      border-radius: 12px
    }

    #muneer-useful-links-box .muneer-action-box-content {
      padding: 0
    }

    #muneer-useful-links-box .muneer-action-box-content label {
      display: none
    }

    .muneer-useful-links-box .muneer-action-box-content {
      flex-direction: column;
      padding: 16px
    }

    .muneer-useful-links-box .muneer-select-box {
      width: 100%
    }

    .muneer-useful-links-box .muneer-select-box select {
      width: 100%;
      max-width: 100%;
      border-radius: 12px;
      margin: 0;
      font-size: 14px;
      line-height: 1;
      border: none;
      height: 45px;
      min-height: 45px;
      max-height: 45px;
      transition: all .2s ease-in-out;
      cursor: pointer;
      background-color: transparent;
      text-indent: 15px;
      -webkit-appearance: none;
      background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48c3ZnIGlkPSJiIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMjAuMTMgMTkyLjA3Ij48cGF0aCBkPSJtMTgyLjY4LDE4Mi43Yy0xMi41LDEyLjUtMzIuOCwxMi41LTQ1LjMsMEw5LjM4LDU0LjdDLjE4LDQ1LjUtMi41MiwzMS44LDIuNDgsMTkuOFMxOS4wOCwwLDMyLjA4LDBoMjU2YzEyLjksMCwyNC42LDcuOCwyOS42LDE5LjhzMi4yLDI1LjctNi45LDM0LjlsLTEyOCwxMjhoLS4xWiIgc3R5bGU9InN0cm9rZS13aWR0aDowcHg7Ii8+PC9zdmc+Cg==");
      background-repeat: no-repeat;
      background-position: right 15px top 50%;
      background-size: 10px auto
    }

    .muneer-useful-links-box .muneer-select-box select:focus,
    .muneer-useful-links-box .muneer-select-box select:hover {
      transition: all .2s ease-in-out;
      font-size: 16px
    }

    .muneer-useful-links-box .muneer-select-box select:focus {
      outline: 0
    }

    #muneer-action-useful-links {
      width: 100%
    }

    #muneer-action-useful-links i {
      display: none
    }

    [dir=ltr] .muneer-dyslexia-font,
    [dir=ltr].muneer-dyslexia-font,
    body[dir=ltr] .muneer-dyslexia-font,
    body[dir=ltr].muneer-dyslexia-font,
    html[dir=ltr] .muneer-dyslexia-font,
    html[dir=ltr].muneer-dyslexia-font {
      font-family: OpenDyslexicMuneer, serif !important
    }

    [dir=ltr] .muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    [dir=ltr] .muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    [dir=ltr] .muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr] .muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=ltr].muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr] .muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=ltr].muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr] .muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=ltr].muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    .muneer-dyslexia-font {
      font-family: OpenDyslexicMuneer, serif !important
    }

    .muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    .muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    .muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    [dir=rtl] .muneer-dyslexia-font,
    [dir=rtl].muneer-dyslexia-font,
    body[dir=rtl] .muneer-dyslexia-font,
    body[dir=rtl].muneer-dyslexia-font,
    html[dir=rtl] .muneer-dyslexia-font,
    html[dir=rtl].muneer-dyslexia-font {
      font-family: OpenDyslexicMuneer, serif !important
    }

    [dir=rtl] .muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    [dir=rtl] .muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    [dir=rtl] .muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl] .muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    [dir=rtl].muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl] .muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body[dir=rtl].muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl] .muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    html[dir=rtl].muneer-dyslexia-font textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    .muneer-dyslexia-font [dir=rtl] {
      font-family: OpenDyslexicMuneer, serif !important
    }

    .muneer-dyslexia-font [dir=rtl] h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    .muneer-dyslexia-font [dir=rtl] h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    .muneer-dyslexia-font [dir=rtl] a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    .muneer-dyslexia-font [dir=rtl] textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h1:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h2:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h3:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h4:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h5:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h6:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h1 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h2 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h3 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h4 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h5 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) h6 span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) blockquote:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) code:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) dd:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) dt:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) input:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) label:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) legend:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) li a:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) p:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) pre:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) select:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) span:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas),
    body.muneer-dyslexia-font :not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) textarea:not(.material-icons):not(.fa):not(.fab):not(.fad):not(.fal):not(.far):not(.fas) {
      font-family: OpenDyslexicMuneer, serif !important
    }

    .muneer-light-contrast .muneer-icon {
      background-color: var(--muneer-color) !important
    }

    .muneer-light-contrast .muneer-multi-toggle-box.active .muneer-icon,
    .muneer-light-contrast .muneer-toggle-box.active .muneer-icon {
      background-color: var(--muneer-bg) !important
    }

    .muneer-light-contrast .muneer-multi-toggle-box.active span,
    .muneer-light-contrast .muneer-toggle-box.active span {
      color: var(--muneer-text) !important
    }

    .muneer-dark-contrast .muneer-mode-title {
      background: #181818;
      color: #a2e6cc;
      padding: 0 4px;
      border-radius: 4px;
      display: inline
    }

    .muneer-dark-contrast .muneer-mode-short {
      background: #181818;
      color: #fff;
      padding: 0 4px;
      border-radius: 4px
    }

    .muneer-dark-contrast ::-moz-placeholder {
      color: #d1d1d1
    }

    .muneer-dark-contrast ::placeholder {
      color: #d1d1d1
    }

    .muneer-dark-contrast p,
    .muneer-light-contrast p {
      border-radius: 4px
    }

    .muneer-dark-contrast .muneer-action-box .muneer-action-box-content .muneer-title,
    .muneer-light-contrast .muneer-action-box .muneer-action-box-content .muneer-title {
      padding: 0 4px;
      border-radius: 4px
    }

    .muneer-dark-contrast label.switch,
    .muneer-light-contrast label.switch {
      border-radius: 1rem
    }

    .muneer-dark-contrast #muneer-popup-footer button span,
    .muneer-light-contrast #muneer-popup-footer button span {
      padding: 0 4px;
      border-radius: 4px
    }

    .muneer-dark-contrast #muneer-popup-footer .muneer-statement,
    .muneer-light-contrast #muneer-popup-footer .muneer-statement {
      text-align: justify;
      padding: 4px 8px !important;
      border-radius: 4px
    }

    .muneer-dark-contrast .muneer-subheader h4,
    .muneer-light-contrast .muneer-subheader h4 {
      padding: 0 8px !important;
      border-radius: 4px;
      display: inline-block
    }

    .muneer-dark-contrast #muneer-online-dictionary-box label,
    .muneer-light-contrast #muneer-online-dictionary-box label {
      display: inline-block;
      padding-right: 4px;
      border-radius: 4px
    }

    body.muneer-dark-contrast h1,
    body.muneer-dark-contrast h1 span,
    body.muneer-dark-contrast h2,
    body.muneer-dark-contrast h2 span,
    body.muneer-dark-contrast h3,
    body.muneer-dark-contrast h3 span,
    body.muneer-dark-contrast h4,
    body.muneer-dark-contrast h4 span,
    body.muneer-dark-contrast h5,
    body.muneer-dark-contrast h5 span,
    body.muneer-dark-contrast h6,
    body.muneer-dark-contrast h6 span {
      background-color: #181818 !important;
      color: #a2e6cc !important
    }

    body.muneer-dark-contrast,
    body.muneer-dark-contrast blockquote,
    body.muneer-dark-contrast code,
    body.muneer-dark-contrast dd,
    body.muneer-dark-contrast dt,
    body.muneer-dark-contrast input,
    body.muneer-dark-contrast label,
    body.muneer-dark-contrast legend,
    body.muneer-dark-contrast li,
    body.muneer-dark-contrast p,
    body.muneer-dark-contrast pre,
    body.muneer-dark-contrast select,
    body.muneer-dark-contrast span,
    body.muneer-dark-contrast textarea {
      color: #fff !important;
      background-color: #181818 !important
    }

    body.muneer-dark-contrast a,
    body.muneer-dark-contrast h1 a,
    body.muneer-dark-contrast h2 a,
    body.muneer-dark-contrast h3 a,
    body.muneer-dark-contrast h4 a,
    body.muneer-dark-contrast h5 a,
    body.muneer-dark-contrast h6 a {
      background-color: #181818 !important;
      color: #f7c79b !important
    }

    body.muneer-light-contrast h1,
    body.muneer-light-contrast h1 span,
    body.muneer-light-contrast h2,
    body.muneer-light-contrast h2 span,
    body.muneer-light-contrast h3,
    body.muneer-light-contrast h3 span,
    body.muneer-light-contrast h4,
    body.muneer-light-contrast h4 span,
    body.muneer-light-contrast h5,
    body.muneer-light-contrast h5 span,
    body.muneer-light-contrast h6,
    body.muneer-light-contrast h6 span {
      color: #075f39 !important;
      background-color: #fff !important
    }

    body.muneer-light-contrast,
    body.muneer-light-contrast blockquote,
    body.muneer-light-contrast code,
    body.muneer-light-contrast dd,
    body.muneer-light-contrast dt,
    body.muneer-light-contrast input,
    body.muneer-light-contrast label,
    body.muneer-light-contrast legend,
    body.muneer-light-contrast li,
    body.muneer-light-contrast p,
    body.muneer-light-contrast pre,
    body.muneer-light-contrast select,
    body.muneer-light-contrast span,
    body.muneer-light-contrast textarea {
      color: #27272d !important;
      background-color: #fff !important
    }

    body.muneer-light-contrast a,
    body.muneer-light-contrast h1 a,
    body.muneer-light-contrast h2 a,
    body.muneer-light-contrast h3 a,
    body.muneer-light-contrast h4 a,
    body.muneer-light-contrast h5 a,
    body.muneer-light-contrast h6 a {
      color: #074fbb !important;
      background-color: #fff !important
    }

    body.muneer-invert-colors {
      background: #000
    }

    body.muneer-invert-colors>* {
      filter: invert(1)
    }

    .muneer-high-saturation #muneer-popup,
    .muneer-high-saturation #muneer-sidebar,
    .muneer-high-saturation>:not(#muneer-popup-box):not(#boxed-wrapper) {
      filter: saturate(200%) !important
    }

    .muneer-low-saturation #muneer-popup,
    .muneer-low-saturation #muneer-sidebar,
    .muneer-low-saturation>:not(#muneer-popup-box):not(#boxed-wrapper) {
      filter: saturate(50%) !important
    }

    .muneer-high-contrast #muneer-popup,
    .muneer-high-contrast #muneer-sidebar,
    .muneer-high-contrast>:not(#muneer-popup-box):not(#boxed-wrapper) {
      filter: contrast(135%) !important
    }

    .muneer-monochrome #muneer-popup,
    .muneer-monochrome #muneer-sidebar,
    .muneer-monochrome>:not(#muneer-popup-box):not(#boxed-wrapper) {
      filter: grayscale(100%) !important
    }

    .muneer-alt {
      display: none
    }

    .muneer-image-alt .muneer-alt {
      display: flex;
      background: rgba(0, 0, 0, .75);
      color: #fff;
      padding: .5em;
      pointer-events: none;
      margin-top: 0
    }

    #muneer-voice-nav-popup-box {
      font-family: var(--muneer-font-family);
      display: none
    }

    #muneer-voice-nav-popup-box.is-open {
      display: block
    }

    #muneer-voice-nav-popup-box.is-open.muneer-voice-nav-auto-hide {
      display: none !important
    }

    #muneer-voice-nav-popup-box.muneer-modal-absolute #muneer-voice-nav-popup {
      position: absolute
    }

    #muneer-voice-nav-popup-box.muneer-modal-fixed #muneer-voice-nav-popup {
      position: fixed
    }

    #muneer-voice-nav-popup-box.muneer-modal-shadow #muneer-voice-nav-popup {
      box-shadow: 0 2.8px 2.2px rgba(0, 0, 0, .034), 0 6.7px 5.3px rgba(0, 0, 0, .048), 0 12.5px 10px rgba(0, 0, 0, .06), 0 22.3px 17.9px rgba(0, 0, 0, .072), 0 41.8px 33.4px rgba(0, 0, 0, .086), 0 20px 80px rgba(0, 0, 0, .12)
    }

    #muneer-voice-nav-popup-box #muneer-voice-nav-popup {
      bottom: 10px;
      z-index: 99999;
      display: block;
      width: 75vw;
      max-height: 50vh;
      box-sizing: border-box;
      overflow: hidden;
      border-radius: var(--muneer-border-radius);
      animation-duration: var(--muneer-animate);
      animation-iteration-count: 1;
      animation-direction: normal;
      animation-fill-mode: both;
      right: 0;
      left: 0;
      margin: 0 auto
    }

    #muneer-voice-form {
      display: flex;
      padding: 32px;
      -moz-column-gap: 12px;
      column-gap: 12px;
      align-items: center
    }

    #muneer-voice-form input#muneer-voice-commands-input {
      box-sizing: border-box;
      background: 0 0;
      width: 100%;
      margin: 0;
      line-height: 24px;
      transition: .25s ease-in-out all;
      height: 24px;
      border: none;
      outline: 0;
      position: relative;
      font-size: 16px
    }

    #muneer-voice-form button#muneer-voice-commands-rec-button {
      width: 16px;
      height: 16px;
      min-width: 16px;
      min-height: 16px;
      display: inline-flex;
      outline-offset: 5px;
      box-shadow: none;
      box-sizing: border-box;
      border: none;
      padding: 0;
      margin: 0
    }

    button#muneer-commands--stop,
    button#muneer-commands--trigger {
      width: 24px;
      height: 24px;
      min-width: 24px;
      min-height: 24px;
      background: 0 0;
      border-radius: 50%;
      border: none;
      transition: all .25s ease-out;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer
    }

    button#muneer-commands--stop svg,
    button#muneer-commands--trigger svg {
      width: 24px;
      height: 24px;
      min-width: 24px;
      min-height: 24px
    }

    button#muneer-commands--stop[aria-expanded=true],
    button#muneer-commands--trigger[aria-expanded=true] {
      transform: rotate(180deg)
    }

    button#muneer-commands--stop[aria-expanded=false],
    button#muneer-commands--trigger[aria-expanded=false] {
      transform: rotate(0)
    }

    button#muneer-commands--stop:hover,
    button#muneer-commands--trigger:hover {
      color: var(--muneer-color);
      transition: all .25s ease-out
    }

    button#muneer-commands--stop:focus,
    button#muneer-commands--trigger:focus {
      outline: 2px solid var(--muneer-color-accent);
      outline-offset: 4px;
      transition: all .25s ease-out
    }

    #muneer-voice-commands {
      overflow: auto;
      padding: 0 32px 32px 32px;
      max-height: 40vh
    }

    #muneer-voice-commands::-webkit-scrollbar-track {
      border-radius: 6px
    }

    #muneer-voice-commands::-webkit-scrollbar {
      width: 16px
    }

    #muneer-voice-commands::-webkit-scrollbar-thumb {
      border-radius: 8px
    }

    .muneer-voice-commands-row th {
      font-weight: 500;
      text-align: left;
      white-space: nowrap
    }

    .muneer-voice-commands-row td {
      display: flex;
      flex-wrap: wrap;
      -moz-column-gap: 12px;
      column-gap: 12px;
      row-gap: 12px;
      padding: 10px 0
    }

    .muneer-command {
      cursor: pointer;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 400;
      border-radius: 8px;
      border: none;
      font-size: 16px;
      white-space: nowrap;
      padding: 8px 12px
    }

    #muneer-voice-commands-last {
      text-align: right;
      white-space: nowrap;
      box-sizing: border-box;
      background: 0 0;
      margin: 0;
      line-height: 24px;
      transition: .25s ease-in-out all;
      height: 24px;
      border: none;
      font-size: 14px;
      opacity: .75;
      padding: 0
    }

    .muneer-recognition-clear #muneer-voice-commands-input {
      animation: muneer-fade-out 1s ease-in-out
    }

    .muneer-voice-recognition-start #muneer-voice-commands-rec-button {
      border-radius: 50%;
      animation: muneer-recognition 1s infinite;
      background: red;
      outline-color: red
    }

    .muneer-voice-recognition-end:not(.muneer-voice-recognition-error) #muneer-voice-commands-rec-button {
      border-radius: 4px;
      animation: none;
      background: 0 0;
      display: flex;
      justify-content: space-between;
      outline-color: var(--muneer-color-accent)
    }

    .muneer-voice-recognition-end:not(.muneer-voice-recognition-error) #muneer-voice-commands-rec-button:before {
      content: "";
      border-radius: 4px;
      width: 6px;
      height: 100%;
      background: var(--muneer-text)
    }

    .muneer-voice-recognition-end:not(.muneer-voice-recognition-error) #muneer-voice-commands-rec-button:after {
      content: "";
      border-radius: 4px;
      width: 6px;
      height: 100%;
      background: var(--muneer-text)
    }

    .muneer-voice-recognition-error #muneer-voice-commands-rec-button {
      border-radius: 4px;
      background: rgba(0, 0, 0, .5);
      animation: none;
      outline-color: rgba(0, 0, 0, .5)
    }

    @media (max-width:768px) {
      #muneer-voice-commands-last {
        display: none
      }
    }

    .muneer-text-magnifier #muneer-text-magnifier-tooltip {
      background-color: var(--muneer-text-magnifier-bg-color) !important;
      color: var(--muneer-text-magnifier-color) !important;
      font-size: var(--muneer-text-magnifier-font-size) !important
    }

    .muneer-text-magnifier #muneer-text-magnifier-tooltip {
      position: fixed;
      padding: 16px;
      border-radius: 4px;
      background: #333;
      color: #fff;
      font-size: 36px;
      line-height: 1.5;
      z-index: 999999;
      word-break: break-word;
      width: -moz-fit-content;
      width: fit-content
    }

    .muneer-tts {
      background: var(--muneer-tts-bg);
      border-radius: 50%;
      position: absolute;
      z-index: 99999;
      line-height: 0;
      box-shadow: 0 0 24px rgba(0, 0, 0, .25);
      transition: all .2s ease-in-out
    }

    .muneer-tts:hover {
      transform: scale(1.1) !important
    }

    .muneer-tts .muneer-tts-button {
      display: inline-block;
      margin: 14px;
      border-radius: 50%;
      cursor: pointer;
      transition: all .2s ease-in-out
    }

    .muneer-tts .muneer-tts-arrow {
      width: 0;
      height: 0;
      position: absolute;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 6px solid var(--muneer-tts-bg);
      bottom: -5px
    }

    .muneer-tts svg * {
      fill: var(--muneer-tts-color)
    }

    .muneer-text-magnifier #muneer-text-magnifier-tooltip {
      background-color: var(--muneer-text-magnifier-bg-color) !important;
      color: var(--muneer-text-magnifier-color) !important;
      font-size: var(--muneer-text-magnifier-font-size) !important
    }

    .muneer-text-magnifier #muneer-text-magnifier-tooltip {
      position: fixed;
      padding: 16px;
      border-radius: 4px;
      background: #333;
      color: #fff;
      font-size: 36px;
      line-height: 1.5;
      z-index: 999999;
      word-break: break-word;
      width: -moz-fit-content;
      width: fit-content
    }

    .muneer-highlight-titles [role=heading],
    .muneer-highlight-titles h1,
    .muneer-highlight-titles h2,
    .muneer-highlight-titles h3,
    .muneer-highlight-titles h4,
    .muneer-highlight-titles h5,
    .muneer-highlight-titles h6 {
      outline: var(--muneer-highlight-titles-color) var(--muneer-highlight-titles-style) var(--muneer-highlight-titles-width) !important;
      outline-offset: var(--muneer-highlight-titles-offset) !important
    }

    .muneer-highlight-links a,
    .muneer-highlight-links button,
    .muneer-highlight-links input[type=submit] {
      outline: var(--muneer-highlight-links-color) var(--muneer-highlight-links-style) var(--muneer-highlight-links-width) !important;
      outline-offset: var(--muneer-highlight-links-offset) !important
    }

    .muneer-highlight-focus :focus {
      outline: var(--muneer-highlight-focus-color) var(--muneer-highlight-focus-style) var(--muneer-highlight-focus-width) !important;
      outline-offset: var(--muneer-highlight-focus-offset) !important
    }

    .muneer-highlight-hover :hover {
      outline: var(--muneer-highlight-hover-color) var(--muneer-highlight-hover-style) var(--muneer-highlight-hover-width) !important;
      outline-offset: var(--muneer-highlight-hover-offset) !important
    }

    .muneer-reading-guide-element {
      display: none
    }

    .muneer-reading-guide .muneer-reading-guide-element {
      display: flex;
      justify-content: center;
      position: fixed;
      top: 0;
      left: auto;
      right: auto;
      margin: auto;
      max-width: 90%;
      pointer-events: none;
      z-index: 999999;
      width: var(--muneer-reading-guide-width);
      height: var(--muneer-reading-guide-height);
      border-radius: var(--muneer-reading-guide-radius);
      background-color: var(--muneer-reading-guide-bg);
      border: var(--muneer-reading-guide-border-width) solid var(--muneer-reading-guide-border-color)
    }

    .muneer-reading-guide .muneer-reading-guide-element:before {
      content: "";
      width: 0;
      height: 0;
      border-left: var(--muneer-reading-guide-arrow) solid transparent;
      border-right: var(--muneer-reading-guide-arrow) solid transparent;
      border-bottom: var(--muneer-reading-guide-arrow) solid var(--muneer-reading-guide-border-color);
      margin-top: var(--muneer-reading-guide-arrow-margin)
    }

    body.muneer-reading-mask .muneer-reading-mask-bottom,
    body.muneer-reading-mask .muneer-reading-mask-top {
      display: block;
      position: fixed;
      left: 0;
      right: 0;
      width: 100%;
      z-index: 999999;
      background-color: var(--muneer-reading-mask)
    }

    body.muneer-reading-mask .muneer-reading-mask-top {
      top: 0;
      bottom: auto
    }

    body.muneer-reading-mask .muneer-reading-mask-bottom {
      bottom: 0;
      top: auto
    }

    .muneer-multi-toggle-box,
    .muneer-toggle-box {
      cursor: pointer;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      height: 130px;
      max-height: 130px;
      border-radius: 12px;
      overflow: hidden
    }

    .muneer-multi-toggle-box .muneer-action-box-content,
    .muneer-toggle-box .muneer-action-box-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow: hidden;
      width: 100%
    }

    .muneer-multi-toggle-box .muneer-icon,
    .muneer-toggle-box .muneer-icon {
      -webkit-mask-position: center;
      mask-position: center;
      -webkit-mask-repeat: no-repeat;
      mask-repeat: no-repeat;
      -webkit-mask-size: contain;
      mask-size: contain;
      width: 32px;
      height: 32px;
      min-width: 32px;
      min-height: 32px;
      display: flex;
      margin: 0 0 8px 0
    }

    .muneer-multi-toggle-box .muneer-title,
    .muneer-toggle-box .muneer-title {
      padding: 0 10px;
      text-align: center;
      font-size: 12px;
      align-self: baseline;
      width: calc(100% - 20px);
      box-sizing: unset !important
    }

    .muneer-multi-toggle-box .muneer-indicator-box,
    .muneer-toggle-box .muneer-indicator-box {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 75%;
      -moz-column-gap: 8px;
      column-gap: 8px;
      margin-top: 8px
    }

    .muneer-multi-toggle-box .muneer-indicator-box .muneer-indicator,
    .muneer-toggle-box .muneer-indicator-box .muneer-indicator {
      background: rgba(255, 255, 255, .2);
      height: 4px;
      width: 100%;
      border-radius: 25px;
      transition: all .2s ease-in-out
    }

    .muneer-multi-toggle-box .muneer-indicator-box .muneer-indicator.active,
    .muneer-toggle-box .muneer-indicator-box .muneer-indicator.active {
      background: #fff
    }

    .muneer-multi-toggle-box.active,
    .muneer-toggle-box.active {
      color: var(--muneer-color)
    }

    .muneer-multi-toggle-box.active span,
    .muneer-toggle-box.active span {
      color: #fff !important
    }

    .muneer-multi-toggle-box.active span.muneer-icon,
    .muneer-toggle-box.active span.muneer-icon {
      background-color: #fff
    }

    .muneer-multi-toggle-box:focus-visible,
    .muneer-toggle-box:focus-visible {
      outline: 0
    }

    .muneer-multi-toggle-box[data-index="0"] .muneer-indicator-box {
      display: none
    }

    .muneer-row {
      padding: 12px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      -moz-column-gap: 20px;
      column-gap: 20px
    }

    .muneer-row .muneer-tool-title {
      font-size: 16px;
      font-weight: 500;
      margin: 0
    }

    .muneer-row .muneer-mode-title {
      font-size: 16px;
      font-weight: 500;
      margin: 0;
      display: flex;
      align-items: center;
      -moz-column-gap: 8px;
      column-gap: 8px
    }

    .muneer-row .muneer-mode-title:before {
      display: inline-flex;
      content: "";
      width: 24px;
      height: 24px;
      min-width: 24px;
      min-height: 24px;
      -webkit-mask-position: center;
      mask-position: center;
      -webkit-mask-repeat: no-repeat;
      mask-repeat: no-repeat;
      -webkit-mask-size: contain;
      mask-size: contain
    }

    .muneer-row .muneer-mode-short {
      font-size: 14px;
      line-height: 1.4;
      margin-top: 8px;
      text-align: justify
    }

    .muneer-switch-box {
      display: flex
    }

    .muneer-switch-box .switch {
      position: relative;
      display: inline-block;
      border-radius: 16px;
      width: 60px;
      height: 32px;
      font-size: 0;
      transition: .25s ease-out all
    }

    .muneer-switch-box .switch:focus {
      outline: 2px solid var(--muneer-color-accent);
      outline-offset: 4px
    }

    .muneer-switch-box .switch input {
      opacity: 0;
      width: 0;
      height: 0
    }

    .muneer-switch-box .switch .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 32px;
      transition: .4s;
      background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMTYiIHdpZHRoPSIxMiIgdmlld0JveD0iMCAwIDM4NCA1MTIiPjxwYXRoIG9wYWNpdHk9IjEiIGZpbGw9InJnYmEoMCwgMTA4LCAyNDIsIDEpIiBkPSJNMzQ1IDEzN2M5LjQtOS40IDkuNC0yNC42IDAtMzMuOXMtMjQuNi05LjQtMzMuOSAwbC0xMTkgMTE5TDczIDEwM2MtOS40LTkuNC0yNC42LTkuNC0zMy45IDBzLTkuNCAyNC42IDAgMzMuOWwxMTkgMTE5TDM5IDM3NWMtOS40IDkuNC05LjQgMjQuNiAwIDMzLjlzMjQuNiA5LjQgMzMuOSAwbDExOS0xMTlMMzExIDQwOWM5LjQgOS40IDI0LjYgOS40IDMzLjkgMHM5LjQtMjQuNiAwLTMzLjlsLTExOS0xMTlMMzQ1IDEzN3oiLz48L3N2Zz4K");
      background-repeat: no-repeat;
      background-position: 34px center;
      background-size: 14px
    }

    .muneer-switch-box .switch .slider:before {
      position: absolute;
      content: "";
      border-radius: 50%;
      height: 16px;
      width: 16px;
      left: 8px;
      bottom: 8px;
      transition: .4s
    }

    .muneer-switch-box .switch:hover .slider:before {
      height: 23px;
      width: 23px;
      left: 6px;
      bottom: 5px
    }

    .muneer-switch-box .switch input:checked+.slider {
      background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMTYiIHdpZHRoPSIxNCIgdmlld0JveD0iMCAwIDQ0OCA1MTIiPjxwYXRoIG9wYWNpdHk9IjEiIGZpbGw9IiNmZmZmZmYiIGQ9Ik00NDEgMTAzYzkuNCA5LjQgOS40IDI0LjYgMCAzMy45TDE3NyA0MDFjLTkuNCA5LjQtMjQuNiA5LjQtMzMuOSAwTDcgMjY1Yy05LjQtOS40LTkuNC0yNC42IDAtMzMuOXMyNC42LTkuNCAzMy45IDBsMTE5IDExOUw0MDcgMTAzYzkuNC05LjQgMjQuNi05LjQgMzMuOSAweiIvPjwvc3ZnPgo=");
      background-repeat: no-repeat;
      background-position: 12px center;
      background-size: 14px
    }

    .muneer-switch-box .switch input:checked+.slider:before {
      transform: translateX(26px)
    }

    .muneer-palette-box {
      padding: 16px
    }

    .muneer-palette-box .muneer-action-box-content {
      flex-direction: row;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      row-gap: 20px
    }

    .muneer-palette-box .muneer-color-box {
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none;
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: flex-start;
      -moz-column-gap: 10px;
      column-gap: 10px;
      row-gap: 10px;
      flex-grow: 2
    }

    .muneer-palette-box .muneer-color-box .muneer-color {
      display: flex;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      cursor: pointer;
      position: relative;
      transition: .15s;
      box-sizing: content-box;
      justify-content: center;
      align-items: center
    }

    .muneer-palette-box .muneer-color-box .muneer-color:hover {
      transform: scale(1.2)
    }

    .muneer-palette-box .muneer-color-box .muneer-color:focus {
      outline: 0
    }

    .muneer-palette-box .muneer-color-box .muneer-color.active::before {
      content: "";
      display: flex;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      border: 2px solid #fff !important;
      background: inherit !important;
      box-sizing: inherit !important
    }

    .muneer-palette-box .muneer-palette-expand {
      font-size: 14px;
      font-weight: 500;
      color: var(--muneer-text);
      display: inline-flex;
      align-items: center;
      -moz-column-gap: 10px;
      column-gap: 10px
    }

    .muneer-palette-box .muneer-palette-expand span {
      display: inline-flex
    }

    .muneer-palette-box .muneer-palette-expand .muneer-palette-indicator {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: var(--muneer-text)
    }

    .muneer-palette-box .muneer-palette-expand:hover {
      color: var(--muneer-color)
    }

    .muneer-palette-box .muneer-palette-reset {
      font-size: 14px;
      font-weight: 400;
      color: var(--muneer-color);
      display: inline-flex;
      align-items: center;
      -moz-column-gap: 8px;
      column-gap: 8px
    }

    .muneer-palette-box .muneer-palette-reset svg {
      width: 16px;
      height: 16px;
      border-radius: 50%
    }

    .muneer-palette-box .muneer-palette-expand,
    .muneer-palette-box .muneer-palette-reset {
      background: 0 0;
      cursor: pointer;
      border: none;
      padding: 0;
      margin: 0;
      border-radius: 8px
    }

    .muneer-palette-box .muneer-palette-expand:focus,
    .muneer-palette-box .muneer-palette-reset:focus {
      outline: 0
    }

    .muneer-color-box--collapse .muneer-color-box {
      display: none !important
    }

    #muneer-action-google-translate {
      border-radius: 12px;
      position: relative
    }

    #muneer-action-google-translate .muneer-action-google-translate-icon {
      position: absolute;
      top: 12px;
      left: 15px;
      width: 20px;
      height: 20px
    }

    #muneer-action-google-translate .muneer-action-google-translate-icon svg {
      width: 100%;
      height: 100%
    }

    #muneer-google-translate-element .goog-te-gadget {
      font-size: 0
    }

    #muneer-google-translate-element .goog-te-gadget span {
      display: none
    }

    #muneer-google-translate-element select {
      width: 100%;
      max-width: 100%;
      border-radius: 12px;
      margin: 0;
      font-size: 14px;
      line-height: 1;
      border: none;
      height: 45px;
      min-height: 45px;
      max-height: 45px;
      transition: all .2s ease-in-out;
      cursor: pointer;
      background-color: transparent;
      text-transform: capitalize;
      text-indent: 44px;
      -webkit-appearance: none;
      background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48c3ZnIGlkPSJiIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMjAuMTMgMTkyLjA3Ij48cGF0aCBkPSJtMTgyLjY4LDE4Mi43Yy0xMi41LDEyLjUtMzIuOCwxMi41LTQ1LjMsMEw5LjM4LDU0LjdDLjE4LDQ1LjUtMi41MiwzMS44LDIuNDgsMTkuOFMxOS4wOCwwLDMyLjA4LDBoMjU2YzEyLjksMCwyNC42LDcuOCwyOS42LDE5LjhzMi4yLDI1LjctNi45LDM0LjlsLTEyOCwxMjhoLS4xWiIgc3R5bGU9InN0cm9rZS13aWR0aDowcHg7Ii8+PC9zdmc+Cg==");
      background-repeat: no-repeat;
      background-position: right 15px top 50%;
      background-size: 10px auto
    }

    #muneer-google-translate-element select:focus {
      outline: 0
    }

    #muneer-google-translate-element:hover select {
      transition: all .2s ease-in-out;
      font-size: 16px
    }

    #muneer-popup-header {
      padding: 24px 32px
    }

    #muneer-popup-main {
      padding: 0 28px 0 32px
    }

    #muneer-popup-main>[id^=muneer-] {
      padding: 22px 0
    }

    #muneer-popup-buttons {
      padding: 16px 32px
    }

    #muneer-popup-footer {
      padding: 16px 32px
    }

    [dir=ltr] #muneer-popup-main {
      padding: 0 16px 0 32px
    }

    [dir=rtl] #muneer-popup-main {
      padding: 0 32px 0 28px
    }

    [dir=rtl] #muneer-popup-main select {
      background-position: left 15px top 50%
    }

    [dir=rtl] #muneer-online-dictionary-box #muneer-online-dictionary-form label {
      right: 16px
    }

    [dir=rtl] #muneer-voice-form:before {
      right: 32px
    }

    [dir=rtl] .muneer-voice-commands-row th {
      padding-left: 10px
    }

    [dir=rtl] .muneer-action-google-translate-icon {
      right: 15px;
      left: auto
    }

    @media (prefers-color-scheme:light) {
      #muneer-online-dictionary-search {
        background-color: var(--muneer-color-transparent)
      }

      #muneer-online-dictionary-search::-moz-placeholder {
        color: var(--muneer-text)
      }

      #muneer-online-dictionary-search::placeholder {
        color: var(--muneer-text)
      }

      .muneer-useful-links-box .muneer-select-box {
        color: var(--muneer-text)
      }

      .muneer-useful-links-box .muneer-select-box select {
        background-color: transparent
      }

      .muneer-useful-links-box .muneer-select-box select:focus,
      .muneer-useful-links-box .muneer-select-box select:hover {
        color: var(--muneer-color)
      }

      .muneer-useful-links-box .muneer-select-box select:focus {
        box-shadow: 0 0 0 4px var(--muneer-bg), 0 0 0 6px var(--muneer-color-accent)
      }

      .muneer-google-translate-box select {
        background-color: transparent;
        color: var(--muneer-text)
      }

      .muneer-google-translate-box select:focus,
      .muneer-google-translate-box select:hover {
        color: var(--muneer-color)
      }

      .muneer-google-translate-box select:focus {
        box-shadow: 0 0 0 4px var(--muneer-bg), 0 0 0 6px var(--muneer-color-accent)
      }

      .muneer-action-google-translate-icon {
        color: var(--muneer-color)
      }

      #muneer-popup-tools-box {
        border-top: 2px solid var(--muneer-color-transparent)
      }

      #muneer-popup,
      #muneer-sidebar,
      #muneer-voice-nav-popup {
        background-color: var(--muneer-bg);
        color: var(--muneer-text)
      }

      #muneer-popup h1,
      #muneer-popup h2,
      #muneer-popup h3,
      #muneer-popup h4,
      #muneer-popup h5,
      #muneer-popup h6,
      #muneer-sidebar h1,
      #muneer-sidebar h2,
      #muneer-sidebar h3,
      #muneer-sidebar h4,
      #muneer-sidebar h5,
      #muneer-sidebar h6,
      #muneer-voice-nav-popup h1,
      #muneer-voice-nav-popup h2,
      #muneer-voice-nav-popup h3,
      #muneer-voice-nav-popup h4,
      #muneer-voice-nav-popup h5,
      #muneer-voice-nav-popup h6 {
        color: var(--muneer-text)
      }

      #muneer-popup .muneer-subheader,
      #muneer-sidebar .muneer-subheader,
      #muneer-voice-nav-popup .muneer-subheader {
        border-top: 2px solid var(--muneer-color-transparent)
      }

      #muneer-popup #muneer-popup-main::-webkit-scrollbar-track,
      #muneer-popup #muneer-voice-commands::-webkit-scrollbar-track,
      #muneer-sidebar #muneer-popup-main::-webkit-scrollbar-track,
      #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar-track,
      #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar-track,
      #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar-track {
        background-color: var(--muneer-bg)
      }

      #muneer-popup #muneer-popup-main::-webkit-scrollbar,
      #muneer-popup #muneer-voice-commands::-webkit-scrollbar,
      #muneer-sidebar #muneer-popup-main::-webkit-scrollbar,
      #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar,
      #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar,
      #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar {
        background-color: var(--muneer-bg)
      }

      #muneer-popup #muneer-popup-main::-webkit-scrollbar-thumb,
      #muneer-popup #muneer-voice-commands::-webkit-scrollbar-thumb,
      #muneer-sidebar #muneer-popup-main::-webkit-scrollbar-thumb,
      #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar-thumb,
      #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar-thumb,
      #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar-thumb {
        background-color: var(--muneer-color)
      }

      #muneer-popup #muneer-popup-close,
      #muneer-sidebar #muneer-popup-close,
      #muneer-voice-nav-popup #muneer-popup-close {
        color: var(--muneer-text);
        border: 2px solid var(--muneer-color-transparent)
      }

      #muneer-popup #muneer-popup-close:before,
      #muneer-sidebar #muneer-popup-close:before,
      #muneer-voice-nav-popup #muneer-popup-close:before {
        color: var(--muneer-text)
      }

      #muneer-popup #muneer-popup-close:hover,
      #muneer-sidebar #muneer-popup-close:hover,
      #muneer-voice-nav-popup #muneer-popup-close:hover {
        color: var(--muneer-color)
      }

      #muneer-popup #muneer-popup-close:focus,
      #muneer-sidebar #muneer-popup-close:focus,
      #muneer-voice-nav-popup #muneer-popup-close:focus {
        outline: 2px solid var(--muneer-color-accent)
      }

      #muneer-popup #muneer-popup-buttons button,
      #muneer-sidebar #muneer-popup-buttons button,
      #muneer-voice-nav-popup #muneer-popup-buttons button {
        background: var(--muneer-color);
        color: var(--muneer-bg)
      }

      #muneer-popup #muneer-popup-buttons button:hover,
      #muneer-sidebar #muneer-popup-buttons button:hover,
      #muneer-voice-nav-popup #muneer-popup-buttons button:hover {
        background: var(--muneer-color-accent);
        color: var(--muneer-bg-dark)
      }

      #muneer-popup #muneer-popup-buttons button:focus,
      #muneer-sidebar #muneer-popup-buttons button:focus,
      #muneer-voice-nav-popup #muneer-popup-buttons button:focus {
        background: var(--muneer-color-accent);
        color: var(--muneer-bg-dark);
        box-shadow: 0 0 0 4px var(--muneer-bg), 0 0 0 6px var(--muneer-color-accent)
      }

      #muneer-popup #muneer-popup-footer,
      #muneer-sidebar #muneer-popup-footer,
      #muneer-voice-nav-popup #muneer-popup-footer {
        background: var(--muneer-color-transparent)
      }

      #muneer-popup #muneer-popup-footer .muneer-statement a,
      #muneer-sidebar #muneer-popup-footer .muneer-statement a,
      #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a {
        color: var(--muneer-text)
      }

      #muneer-popup #muneer-popup-footer .muneer-statement a:hover,
      #muneer-sidebar #muneer-popup-footer .muneer-statement a:hover,
      #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a:hover {
        color: var(--muneer-text)
      }

      #muneer-popup #muneer-popup-footer .muneer-statement a:focus,
      #muneer-sidebar #muneer-popup-footer .muneer-statement a:focus,
      #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a:focus {
        outline: 2px solid var(--muneer-color);
        outline-offset: 5px
      }

      #muneer-accessibility-modes-box .muneer-accessibility-mode-item.active .muneer-mode-description {
        background: var(--muneer-color-transparent)
      }

      #muneer-accessibility-modes-box .muneer-accessibility-mode-item:focus .switch {
        box-shadow: 0 0 0 6px var(--muneer-bg), 0 0 0 8px var(--muneer-color)
      }

      .muneer-action-box {
        background: var(--muneer-color-transparent);
        transition: all .2s ease-in-out
      }

      .muneer-action-box:focus,
      .muneer-action-box:hover {
        background-color: var(--muneer-color-transparent);
        transition: all .2s ease-in-out
      }

      .muneer-action-box:focus {
        box-shadow: 0 0 0 4px var(--muneer-bg), 0 0 0 6px var(--muneer-color-accent)
      }

      .muneer-multi-toggle-box .muneer-icon,
      .muneer-toggle-box .muneer-icon {
        background-color: var(--muneer-color)
      }

      .muneer-multi-toggle-box:hover,
      .muneer-toggle-box:hover {
        background: var(--muneer-color-accent);
        color: var(--muneer-text)
      }

      .muneer-multi-toggle-box:hover .muneer-icon,
      .muneer-toggle-box:hover .muneer-icon {
        background-color: var(--muneer-text)
      }

      .muneer-multi-toggle-box:focus,
      .muneer-toggle-box:focus {
        background: var(--muneer-color-accent);
        color: var(--muneer-text)
      }

      .muneer-multi-toggle-box:focus .muneer-icon,
      .muneer-toggle-box:focus .muneer-icon {
        background-color: var(--muneer-text)
      }

      .muneer-multi-toggle-box.active,
      .muneer-toggle-box.active {
        background: var(--muneer-color);
        color: var(--muneer-bg)
      }

      .muneer-multi-toggle-box.active .muneer-icon,
      .muneer-toggle-box.active .muneer-icon {
        background-color: var(--muneer-bg)
      }

      .muneer-palette-box .muneer-color-box .muneer-color:focus {
        box-shadow: 0 0 0 4px var(--muneer-color-transparent), 0 0 0 6px var(--muneer-color-accent)
      }

      .muneer-palette-box .muneer-palette-expand:focus .muneer-palette-indicator,
      .muneer-palette-box .muneer-palette-expand:focus svg,
      .muneer-palette-box .muneer-palette-reset:focus .muneer-palette-indicator,
      .muneer-palette-box .muneer-palette-reset:focus svg {
        outline: 2px solid var(--muneer-color-accent);
        outline-offset: 2px
      }

      #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search {
        color: var(--muneer-text);
        outline-color: var(--muneer-color-accent)
      }

      #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search:hover {
        color: var(--muneer-color)
      }

      #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search:focus {
        outline: 2px solid var(--muneer-color-accent);
        outline-offset: 4px
      }

      #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li a,
      #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li a.muneer-online-dictionary-link {
        color: var(--muneer-color)
      }

      #muneer-online-dictionary-box #muneer-online-dictionary-search-close {
        color: var(--muneer-color)
      }

      #muneer-accessibility-statement-box.open {
        background: var(--muneer-bg)
      }

      #muneer-accessibility-statement-box.open::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .2);
        background-color: #f5f5f5
      }

      #muneer-accessibility-statement-box.open::-webkit-scrollbar {
        background-color: #f5f5f5
      }

      #muneer-accessibility-statement-box.open::-webkit-scrollbar-thumb {
        -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .2);
        background-color: #a7a7a7
      }

      #muneer-accessibility-statement-box #muneer-close-statement-btn:before {
        color: var(--muneer-text)
      }

      #muneer-accessibility-statement-box #muneer-close-statement-btn:hover {
        color: var(--muneer-color)
      }

      #muneer-accessibility-statement-box #muneer-close-statement-btn:focus,
      #muneer-accessibility-statement-box #muneer-close-statement-btn:hover {
        outline: 2px solid var(--muneer-color-accent)
      }

      #muneer-keyboard-box {
        background-color: var(--muneer-keyboard-light-bg)
      }

      #muneer-keyboard-box .simple-keyboard {
        background-color: var(--muneer-keyboard-light-bg)
      }

      #muneer-keyboard-box .simple-keyboard .hg-button {
        background: var(--muneer-keyboard-light-key-bg);
        color: var(--muneer-keyboard-light-key);
        box-shadow: 0 0 3px -1px rgba(0, 0, 0, .3);
        border-bottom: 1px solid #b5b5b5;
        -webkit-tap-highlight-color: transparent
      }

      #muneer-keyboard-box .simple-keyboard .hg-button.hg-activeButton {
        filter: invert(1)
      }

      #muneer-keyboard-box .simple-keyboard .hg-button.hg-selectedButton {
        background: rgba(5, 25, 70, .53);
        color: #fff
      }

      #muneer-keyboard-close {
        background: var(--muneer-keyboard-light-key-bg);
        color: var(--muneer-keyboard-light-key);
        box-shadow: 0 0 3px -1px rgba(0, 0, 0, .3);
        border-bottom: 1px solid #b5b5b5;
        -webkit-tap-highlight-color: transparent
      }

      .switch input:checked+.slider {
        background-color: var(--muneer-color)
      }

      .switch .slider {
        background-color: var(--muneer-color-transparent)
      }

      .switch .slider:before {
        background-color: var(--muneer-color)
      }

      .switch:hover .slider {
        background-color: var(--muneer-color-accent)
      }

      .switch:hover .slider:before {
        background-color: var(--muneer-text)
      }

      .active .switch .slider:before {
        background-color: var(--muneer-color-transparent)
      }

      .muneer-mode-title:before {
        background-color: var(--muneer-color)
      }

      #muneer-voice-commands-input {
        color: var(--muneer-text)
      }

      .muneer-command {
        background: var(--muneer-color-transparent);
        transition: all .2s ease-in-out;
        color: var(--muneer-color);
        outline-color: var(--muneer-color-accent)
      }

      .muneer-command:focus,
      .muneer-command:hover {
        transition: all .2s ease-in-out
      }

      .muneer-command:hover {
        background: var(--muneer-color-accent);
        color: var(--muneer-text)
      }

      .muneer-command:focus {
        background: var(--muneer-color-accent);
        color: var(--muneer-text);
        outline: 2px solid var(--muneer-color-accent);
        outline-offset: 4px
      }

      .muneer-command.active {
        background: var(--muneer-color);
        color: var(--muneer-bg)
      }

      #muneer-language-switcher .choices.is-focused .choices__list.choices__list--single .muneer-language-switcher--lang-flag {
        box-shadow: 0 0 0 2px var(--muneer-color-accent);
        border: 2px solid var(--muneer-bg)
      }

      #muneer-language-switcher .choices.is-focused .choices__list.choices__list--single .muneer-language-switcher--lang-name {
        color: var(--muneer-color)
      }

      #muneer-language-switcher .choices__list.choices__list--dropdown {
        background: var(--muneer-bg)
      }

      #muneer-language-switcher.choices-wrapper .choices__list--dropdown .choices__item--selectable.is-highlighted,
      #muneer-language-switcher.choices-wrapper .choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
        background: var(--muneer-color-transparent)
      }

      #muneer-language-switcher.choices-wrapper .choices__list--dropdown .choices__item--selectable.is-highlighted .muneer-language-switcher--lang-name,
      #muneer-language-switcher.choices-wrapper .choices__list[aria-expanded] .choices__item--selectable.is-highlighted .muneer-language-switcher--lang-name {
        color: var(--muneer-color)
      }

      #muneer-theme-switcher fieldset {
        border: 2px solid var(--muneer-color-transparent)
      }

      #muneer-theme-switcher label {
        background: var(--muneer-bg);
        color: var(--muneer-text)
      }

      #muneer-theme-switcher label:focus,
      #muneer-theme-switcher label:hover {
        background: var(--muneer-color-transparent);
        color: var(--muneer-color)
      }

      #muneer-theme-switcher label:focus {
        outline: 2px solid var(--muneer-color-accent)
      }

      #muneer-theme-switcher input[type=radio]:checked+label {
        color: var(--muneer-bg);
        background: var(--muneer-color)
      }

      #muneer-theme-switcher input[type=radio]:checked:focus+label {
        outline: 2px solid var(--muneer-color-accent);
        outline-offset: 2px
      }
    }

    @media (prefers-color-scheme:dark) {

      .muneer-multi-toggle-box.active,
      .muneer-toggle-box.active {
        background: var(--muneer-color-dark);
        color: var(--muneer-bg-dark)
      }

      #muneer-online-dictionary-search {
        background-color: var(--muneer-color-transparent-dark);
        color: var(--muneer-text-dark)
      }

      #muneer-online-dictionary-search:focus,
      #muneer-online-dictionary-search:hover {
        color: var(--muneer-color-accent) !important
      }

      #muneer-online-dictionary-search::-moz-placeholder {
        color: var(--muneer-text-dark)
      }

      #muneer-online-dictionary-search::placeholder {
        color: var(--muneer-text-dark)
      }

      .muneer-useful-links-box .muneer-select-box {
        color: var(--muneer-text-dark)
      }

      .muneer-useful-links-box .muneer-select-box select {
        background-color: transparent;
        color: var(--muneer-text-dark)
      }

      .muneer-useful-links-box .muneer-select-box select:focus,
      .muneer-useful-links-box .muneer-select-box select:hover {
        color: var(--muneer-color-dark)
      }

      .muneer-useful-links-box .muneer-select-box select:focus {
        box-shadow: 0 0 0 4px var(--muneer-bg-dark), 0 0 0 6px var(--muneer-color-accent-dark)
      }

      .muneer-google-translate-box select {
        background-color: transparent;
        color: var(--muneer-text-dark)
      }

      .muneer-google-translate-box select:focus,
      .muneer-google-translate-box select:hover {
        color: var(--muneer-color-dark)
      }

      .muneer-google-translate-box select:focus {
        box-shadow: 0 0 0 4px var(--muneer-bg-dark), 0 0 0 6px var(--muneer-color-accent-dark)
      }

      .muneer-action-google-translate-icon {
        color: var(--muneer-color)
      }

      #muneer-popup,
      #muneer-sidebar,
      #muneer-voice-nav-popup {
        background-color: var(--muneer-bg-dark);
        color: var(--muneer-text-dark)
      }

      #muneer-popup h1,
      #muneer-popup h2,
      #muneer-popup h3,
      #muneer-popup h4,
      #muneer-popup h5,
      #muneer-popup h6,
      #muneer-sidebar h1,
      #muneer-sidebar h2,
      #muneer-sidebar h3,
      #muneer-sidebar h4,
      #muneer-sidebar h5,
      #muneer-sidebar h6,
      #muneer-voice-nav-popup h1,
      #muneer-voice-nav-popup h2,
      #muneer-voice-nav-popup h3,
      #muneer-voice-nav-popup h4,
      #muneer-voice-nav-popup h5,
      #muneer-voice-nav-popup h6 {
        color: var(--muneer-text-dark)
      }

      #muneer-popup .muneer-subheader,
      #muneer-sidebar .muneer-subheader,
      #muneer-voice-nav-popup .muneer-subheader {
        border-top: 2px solid var(--muneer-color-transparent-dark)
      }

      #muneer-popup #muneer-popup-main::-webkit-scrollbar-track,
      #muneer-popup #muneer-voice-commands::-webkit-scrollbar-track,
      #muneer-sidebar #muneer-popup-main::-webkit-scrollbar-track,
      #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar-track,
      #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar-track,
      #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar-track {
        background-color: var(--muneer-bg-dark)
      }

      #muneer-popup #muneer-popup-main::-webkit-scrollbar,
      #muneer-popup #muneer-voice-commands::-webkit-scrollbar,
      #muneer-sidebar #muneer-popup-main::-webkit-scrollbar,
      #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar,
      #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar,
      #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar {
        background-color: var(--muneer-bg-dark)
      }

      #muneer-popup #muneer-popup-main::-webkit-scrollbar-thumb,
      #muneer-popup #muneer-voice-commands::-webkit-scrollbar-thumb,
      #muneer-sidebar #muneer-popup-main::-webkit-scrollbar-thumb,
      #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar-thumb,
      #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar-thumb,
      #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar-thumb {
        background-color: var(--muneer-color-dark)
      }

      #muneer-popup #muneer-popup-close,
      #muneer-sidebar #muneer-popup-close,
      #muneer-voice-nav-popup #muneer-popup-close {
        color: var(--muneer-text-dark);
        border: 2px solid var(--muneer-color-transparent-dark)
      }

      #muneer-popup #muneer-popup-close:before,
      #muneer-sidebar #muneer-popup-close:before,
      #muneer-voice-nav-popup #muneer-popup-close:before {
        color: var(--muneer-text-dark)
      }

      #muneer-popup #muneer-popup-close:hover,
      #muneer-sidebar #muneer-popup-close:hover,
      #muneer-voice-nav-popup #muneer-popup-close:hover {
        color: var(--muneer-color-dark)
      }

      #muneer-popup #muneer-popup-close:focus,
      #muneer-sidebar #muneer-popup-close:focus,
      #muneer-voice-nav-popup #muneer-popup-close:focus {
        outline: 2px solid var(--muneer-color-accent)
      }

      #muneer-popup #muneer-popup-buttons button,
      #muneer-sidebar #muneer-popup-buttons button,
      #muneer-voice-nav-popup #muneer-popup-buttons button {
        background: var(--muneer-color-dark);
        color: var(--muneer-color-transparent)
      }

      #muneer-popup #muneer-popup-buttons button:hover,
      #muneer-sidebar #muneer-popup-buttons button:hover,
      #muneer-voice-nav-popup #muneer-popup-buttons button:hover {
        background: var(--muneer-color-accent);
        color: var(--muneer-bg-dark)
      }

      #muneer-popup #muneer-popup-buttons button:focus,
      #muneer-sidebar #muneer-popup-buttons button:focus,
      #muneer-voice-nav-popup #muneer-popup-buttons button:focus {
        background: var(--muneer-color-accent);
        color: var(--muneer-bg-dark);
        box-shadow: 0 0 0 4px var(--muneer-bg-dark), 0 0 0 6px var(--muneer-color-accent)
      }

      #muneer-popup #muneer-popup-footer,
      #muneer-sidebar #muneer-popup-footer,
      #muneer-voice-nav-popup #muneer-popup-footer {
        background: var(--muneer-color-transparent-dark)
      }

      #muneer-popup #muneer-popup-footer .muneer-statement a,
      #muneer-sidebar #muneer-popup-footer .muneer-statement a,
      #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a {
        color: var(--muneer-text-dark)
      }

      #muneer-popup #muneer-popup-footer .muneer-statement a:hover,
      #muneer-sidebar #muneer-popup-footer .muneer-statement a:hover,
      #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a:hover {
        color: var(--muneer-text-dark)
      }

      #muneer-popup #muneer-popup-footer .muneer-statement a:focus,
      #muneer-sidebar #muneer-popup-footer .muneer-statement a:focus,
      #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a:focus {
        outline: 2px solid var(--muneer-color-dark);
        outline-offset: 5px
      }

      #muneer-accessibility-modes-box .muneer-accessibility-mode-item.active .muneer-mode-description {
        background: var(--muneer-color-transparent-dark)
      }

      #muneer-accessibility-modes-box .muneer-accessibility-mode-item:focus .switch {
        box-shadow: 0 0 0 6px var(--muneer-bg-dark), 0 0 0 8px var(--muneer-color-dark)
      }

      .muneer-action-box {
        background: var(--muneer-color-transparent-dark)
      }

      .muneer-action-box:focus,
      .muneer-action-box:hover {
        background-color: var(--muneer-color-transparent-dark)
      }

      .muneer-action-box:focus {
        box-shadow: 0 0 0 4px var(--muneer-bg-dark), 0 0 0 6px var(--muneer-color-dark)
      }

      .muneer-multi-toggle-box .muneer-icon,
      .muneer-toggle-box .muneer-icon {
        background-color: var(--muneer-color-dark)
      }

      .muneer-multi-toggle-box:hover,
      .muneer-toggle-box:hover {
        color: var(--muneer-text-dark)
      }

      .muneer-multi-toggle-box.active,
      .muneer-toggle-box.active {
        background: var(--muneer-color-dark)
      }

      .muneer-palette-box .muneer-color-box .muneer-color {
        border: 4px solid var(--muneer-bg-dark)
      }

      .muneer-palette-box .muneer-color-box .muneer-color:focus {
        box-shadow: 0 0 0 4px var(--muneer-color-transparent-dark), 0 0 0 6px var(--muneer-color-accent)
      }

      .muneer-palette-box .muneer-palette-expand,
      .muneer-palette-box .muneer-palette-reset {
        color: var(--muneer-color-transparent)
      }

      .muneer-palette-box .muneer-palette-expand:focus .muneer-palette-indicator,
      .muneer-palette-box .muneer-palette-expand:focus svg,
      .muneer-palette-box .muneer-palette-reset:focus .muneer-palette-indicator,
      .muneer-palette-box .muneer-palette-reset:focus svg {
        outline: 2px solid var(--muneer-color-accent);
        outline-offset: 2px
      }

      #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search {
        color: var(--muneer-text-dark);
        outline-color: var(--muneer-color-accent)
      }

      #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search:hover {
        color: var(--muneer-color-dark)
      }

      #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search:focus {
        outline: 2px solid var(--muneer-color-accent);
        outline-offset: 4px
      }

      #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li a,
      #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li a.muneer-online-dictionary-link {
        color: var(--muneer-color-dark)
      }

      #muneer-online-dictionary-box #muneer-online-dictionary-search-close {
        color: var(--muneer-color-dark)
      }

      #muneer-accessibility-statement-box.open {
        background: var(--muneer-bg-dark)
      }

      #muneer-accessibility-statement-box.open::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .2);
        background-color: var(--muneer-bg-dark)
      }

      #muneer-accessibility-statement-box.open::-webkit-scrollbar {
        background-color: var(--muneer-bg-dark)
      }

      #muneer-accessibility-statement-box.open::-webkit-scrollbar-thumb {
        -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .2);
        background-color: var(--muneer-bg-dark)
      }

      #muneer-accessibility-statement-box.open #muneer-close-statement-btn:before {
        color: var(--muneer-text-dark)
      }

      #muneer-keyboard-box {
        background-color: var(--muneer-keyboard-dark-bg)
      }

      #muneer-keyboard-box .simple-keyboard {
        background-color: var(--muneer-keyboard-dark-bg)
      }

      #muneer-keyboard-box .simple-keyboard .hg-button {
        background: var(--muneer-keyboard-dark-key-bg);
        color: var(--muneer-keyboard-dark-key);
        box-shadow: 0 0 3px -1px rgba(0, 0, 0, .3);
        border-bottom: 1px solid #b5b5b5;
        -webkit-tap-highlight-color: transparent
      }

      #muneer-keyboard-box .simple-keyboard .hg-button.hg-activeButton {
        filter: invert(1)
      }

      #muneer-keyboard-box .simple-keyboard .hg-button.hg-selectedButton {
        background: rgba(5, 25, 70, .53);
        color: #fff
      }

      #muneer-keyboard-close {
        background: var(--muneer-keyboard-dark-key-bg);
        color: var(--muneer-keyboard-dark-key);
        box-shadow: 0 0 3px -1px rgba(0, 0, 0, .3);
        border-bottom: 1px solid #b5b5b5;
        -webkit-tap-highlight-color: transparent
      }

      .switch input:checked+.slider {
        background-color: var(--muneer-color-dark)
      }

      .switch .slider {
        background-color: var(--muneer-color-transparent-dark)
      }

      .switch .slider:before {
        background-color: var(--muneer-color-dark)
      }

      .switch:hover .slider {
        background-color: var(--muneer-color-accent)
      }

      .switch:hover .slider:before {
        background-color: var(--muneer-color-dark)
      }

      .active .switch .slider:before {
        background-color: var(--muneer-color-transparent-dark)
      }

      .muneer-mode-title:before {
        background-color: var(--muneer-color-dark)
      }

      #muneer-voice-commands-input {
        color: var(--muneer-text-dark)
      }

      #muneer-language-switcher .choices__list.choices__list--dropdown {
        border: 2px solid var(--muneer-color-transparent-dark)
      }

      #muneer-language-switcher .choices.is-focused .choices__list.choices__list--single .muneer-language-switcher--lang-flag {
        box-shadow: 0 0 0 2px var(--muneer-color-accent);
        border: 2px solid var(--muneer-bg-dark)
      }

      #muneer-language-switcher .choices.is-focused .choices__list.choices__list--single .muneer-language-switcher--lang-name {
        color: var(--muneer-color-dark)
      }

      #muneer-language-switcher .choices__list.choices__list--dropdown {
        background: var(--muneer-bg-dark)
      }

      #muneer-language-switcher.choices-wrapper .choices__list--dropdown .choices__item--selectable.is-highlighted,
      #muneer-language-switcher.choices-wrapper .choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
        background: var(--muneer-color-transparent-dark)
      }

      #muneer-language-switcher.choices-wrapper .choices__list--dropdown .choices__item--selectable.is-highlighted .muneer-language-switcher--lang-name,
      #muneer-language-switcher.choices-wrapper .choices__list[aria-expanded] .choices__item--selectable.is-highlighted .muneer-language-switcher--lang-name {
        color: var(--muneer-color-accent)
      }

      #muneer-language-switcher.choices-wrapper .choices[data-type*=select-one] .choices__input {
        background: var(--muneer-bg-dark);
        color: var(--muneer-text-dark);
        border-bottom-color: var(--muneer-color-transparent-dark)
      }

      #muneer-theme-switcher fieldset {
        border: 2px solid var(--muneer-color-transparent-dark)
      }

      #muneer-theme-switcher label {
        background: var(--muneer-bg-dark);
        color: var(--muneer-text-dark)
      }

      #muneer-theme-switcher label:focus,
      #muneer-theme-switcher label:hover {
        background: var(--muneer-color-transparent-dark);
        color: var(--muneer-color-dark)
      }

      #muneer-theme-switcher label:focus {
        outline: 2px solid var(--muneer-color-accent)
      }

      #muneer-theme-switcher input[type=radio]:checked+label {
        color: var(--muneer-bg-dark);
        background: var(--muneer-color-dark)
      }

      #muneer-theme-switcher input[type=radio]:checked:focus+label {
        outline: 2px solid var(--muneer-color-accent);
        outline-offset: 2px
      }
    }

    .muneer-theme-dark .muneer-multi-toggle-box.active,
    .muneer-theme-dark .muneer-toggle-box.active {
      background: var(--muneer-color-dark);
      color: var(--muneer-bg-dark)
    }

    .muneer-theme-dark #muneer-online-dictionary-search {
      background-color: var(--muneer-color-transparent-dark);
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark #muneer-online-dictionary-search:focus,
    .muneer-theme-dark #muneer-online-dictionary-search:hover {
      color: var(--muneer-color-accent) !important
    }

    .muneer-theme-dark #muneer-online-dictionary-search::-moz-placeholder {
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark #muneer-online-dictionary-search::placeholder {
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark .muneer-useful-links-box .muneer-select-box {
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark .muneer-useful-links-box .muneer-select-box select {
      background-color: transparent;
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark .muneer-useful-links-box .muneer-select-box select:focus,
    .muneer-theme-dark .muneer-useful-links-box .muneer-select-box select:hover {
      color: var(--muneer-color-dark)
    }

    .muneer-theme-dark .muneer-useful-links-box .muneer-select-box select:focus {
      box-shadow: 0 0 0 4px var(--muneer-bg-dark), 0 0 0 6px var(--muneer-color-accent-dark)
    }

    .muneer-theme-dark .muneer-google-translate-box select {
      background-color: transparent;
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark .muneer-google-translate-box select:focus,
    .muneer-theme-dark .muneer-google-translate-box select:hover {
      color: var(--muneer-color-dark)
    }

    .muneer-theme-dark .muneer-google-translate-box select:focus {
      box-shadow: 0 0 0 4px var(--muneer-bg-dark), 0 0 0 6px var(--muneer-color-accent-dark)
    }

    .muneer-theme-dark .muneer-action-google-translate-icon {
      color: var(--muneer-color)
    }

    .muneer-theme-dark #muneer-popup,
    .muneer-theme-dark #muneer-sidebar,
    .muneer-theme-dark #muneer-voice-nav-popup {
      background-color: var(--muneer-bg-dark);
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark #muneer-popup h1,
    .muneer-theme-dark #muneer-popup h2,
    .muneer-theme-dark #muneer-popup h3,
    .muneer-theme-dark #muneer-popup h4,
    .muneer-theme-dark #muneer-popup h5,
    .muneer-theme-dark #muneer-popup h6,
    .muneer-theme-dark #muneer-sidebar h1,
    .muneer-theme-dark #muneer-sidebar h2,
    .muneer-theme-dark #muneer-sidebar h3,
    .muneer-theme-dark #muneer-sidebar h4,
    .muneer-theme-dark #muneer-sidebar h5,
    .muneer-theme-dark #muneer-sidebar h6,
    .muneer-theme-dark #muneer-voice-nav-popup h1,
    .muneer-theme-dark #muneer-voice-nav-popup h2,
    .muneer-theme-dark #muneer-voice-nav-popup h3,
    .muneer-theme-dark #muneer-voice-nav-popup h4,
    .muneer-theme-dark #muneer-voice-nav-popup h5,
    .muneer-theme-dark #muneer-voice-nav-popup h6 {
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark #muneer-popup .muneer-subheader,
    .muneer-theme-dark #muneer-sidebar .muneer-subheader,
    .muneer-theme-dark #muneer-voice-nav-popup .muneer-subheader {
      border-top: 2px solid var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-main::-webkit-scrollbar-track,
    .muneer-theme-dark #muneer-popup #muneer-voice-commands::-webkit-scrollbar-track,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-main::-webkit-scrollbar-track,
    .muneer-theme-dark #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar-track,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar-track,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar-track {
      background-color: var(--muneer-bg-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-main::-webkit-scrollbar,
    .muneer-theme-dark #muneer-popup #muneer-voice-commands::-webkit-scrollbar,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-main::-webkit-scrollbar,
    .muneer-theme-dark #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar {
      background-color: var(--muneer-bg-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-main::-webkit-scrollbar-thumb,
    .muneer-theme-dark #muneer-popup #muneer-voice-commands::-webkit-scrollbar-thumb,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-main::-webkit-scrollbar-thumb,
    .muneer-theme-dark #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar-thumb,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar-thumb,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar-thumb {
      background-color: var(--muneer-color-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-close,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-close,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-close {
      color: var(--muneer-text-dark);
      border: 2px solid var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-close:before,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-close:before,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-close:before {
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-close:hover,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-close:hover,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-close:hover {
      color: var(--muneer-color-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-close:focus,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-close:focus,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-close:focus {
      outline: 2px solid var(--muneer-color-accent)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-buttons button,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-buttons button,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-buttons button {
      background: var(--muneer-color-dark);
      color: var(--muneer-color-transparent)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-buttons button:hover,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-buttons button:hover,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-buttons button:hover {
      background: var(--muneer-color-accent);
      color: var(--muneer-bg-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-buttons button:focus,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-buttons button:focus,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-buttons button:focus {
      background: var(--muneer-color-accent);
      color: var(--muneer-bg-dark);
      box-shadow: 0 0 0 4px var(--muneer-bg-dark), 0 0 0 6px var(--muneer-color-accent)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-footer,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-footer,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-footer {
      background: var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-footer .muneer-statement a,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-footer .muneer-statement a,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a {
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-footer .muneer-statement a:hover,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-footer .muneer-statement a:hover,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a:hover {
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark #muneer-popup #muneer-popup-footer .muneer-statement a:focus,
    .muneer-theme-dark #muneer-sidebar #muneer-popup-footer .muneer-statement a:focus,
    .muneer-theme-dark #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a:focus {
      outline: 2px solid var(--muneer-color-dark);
      outline-offset: 5px
    }

    .muneer-theme-dark #muneer-accessibility-modes-box .muneer-accessibility-mode-item.active .muneer-mode-description {
      background: var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark #muneer-accessibility-modes-box .muneer-accessibility-mode-item:focus .switch {
      box-shadow: 0 0 0 6px var(--muneer-bg-dark), 0 0 0 8px var(--muneer-color-dark)
    }

    .muneer-theme-dark .muneer-action-box {
      background: var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark .muneer-action-box:focus,
    .muneer-theme-dark .muneer-action-box:hover {
      background-color: var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark .muneer-action-box:focus {
      box-shadow: 0 0 0 4px var(--muneer-bg-dark), 0 0 0 6px var(--muneer-color-dark)
    }

    .muneer-theme-dark .muneer-multi-toggle-box .muneer-icon,
    .muneer-theme-dark .muneer-toggle-box .muneer-icon {
      background-color: var(--muneer-color-dark)
    }

    .muneer-theme-dark .muneer-multi-toggle-box:hover,
    .muneer-theme-dark .muneer-toggle-box:hover {
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark .muneer-multi-toggle-box.active,
    .muneer-theme-dark .muneer-toggle-box.active {
      background: var(--muneer-color-dark)
    }

    .muneer-theme-dark .muneer-palette-box .muneer-color-box .muneer-color {
      border: 4px solid var(--muneer-bg-dark)
    }

    .muneer-theme-dark .muneer-palette-box .muneer-color-box .muneer-color:focus {
      box-shadow: 0 0 0 4px var(--muneer-color-transparent-dark), 0 0 0 6px var(--muneer-color-accent)
    }

    .muneer-theme-dark .muneer-palette-box .muneer-palette-expand,
    .muneer-theme-dark .muneer-palette-box .muneer-palette-reset {
      color: var(--muneer-color-transparent)
    }

    .muneer-theme-dark .muneer-palette-box .muneer-palette-expand:focus .muneer-palette-indicator,
    .muneer-theme-dark .muneer-palette-box .muneer-palette-expand:focus svg,
    .muneer-theme-dark .muneer-palette-box .muneer-palette-reset:focus .muneer-palette-indicator,
    .muneer-theme-dark .muneer-palette-box .muneer-palette-reset:focus svg {
      outline: 2px solid var(--muneer-color-accent);
      outline-offset: 2px
    }

    .muneer-theme-dark #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search {
      color: var(--muneer-text-dark);
      outline-color: var(--muneer-color-accent)
    }

    .muneer-theme-dark #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search:hover {
      color: var(--muneer-color-dark)
    }

    .muneer-theme-dark #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search:focus {
      outline: 2px solid var(--muneer-color-accent);
      outline-offset: 4px
    }

    .muneer-theme-dark #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li a,
    .muneer-theme-dark #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li a.muneer-online-dictionary-link {
      color: var(--muneer-color-dark)
    }

    .muneer-theme-dark #muneer-online-dictionary-box #muneer-online-dictionary-search-close {
      color: var(--muneer-color-dark)
    }

    .muneer-theme-dark #muneer-accessibility-statement-box.open {
      background: var(--muneer-bg-dark)
    }

    .muneer-theme-dark #muneer-accessibility-statement-box.open::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .2);
      background-color: var(--muneer-bg-dark)
    }

    .muneer-theme-dark #muneer-accessibility-statement-box.open::-webkit-scrollbar {
      background-color: var(--muneer-bg-dark)
    }

    .muneer-theme-dark #muneer-accessibility-statement-box.open::-webkit-scrollbar-thumb {
      -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .2);
      background-color: var(--muneer-bg-dark)
    }

    .muneer-theme-dark #muneer-accessibility-statement-box.open #muneer-close-statement-btn:before {
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark #muneer-keyboard-box {
      background-color: var(--muneer-keyboard-dark-bg)
    }

    .muneer-theme-dark #muneer-keyboard-box .simple-keyboard {
      background-color: var(--muneer-keyboard-dark-bg)
    }

    .muneer-theme-dark #muneer-keyboard-box .simple-keyboard .hg-button {
      background: var(--muneer-keyboard-dark-key-bg);
      color: var(--muneer-keyboard-dark-key);
      box-shadow: 0 0 3px -1px rgba(0, 0, 0, .3);
      border-bottom: 1px solid #b5b5b5;
      -webkit-tap-highlight-color: transparent
    }

    .muneer-theme-dark #muneer-keyboard-box .simple-keyboard .hg-button.hg-activeButton {
      filter: invert(1)
    }

    .muneer-theme-dark #muneer-keyboard-box .simple-keyboard .hg-button.hg-selectedButton {
      background: rgba(5, 25, 70, .53);
      color: #fff
    }

    .muneer-theme-dark #muneer-keyboard-close {
      background: var(--muneer-keyboard-dark-key-bg);
      color: var(--muneer-keyboard-dark-key);
      box-shadow: 0 0 3px -1px rgba(0, 0, 0, .3);
      border-bottom: 1px solid #b5b5b5;
      -webkit-tap-highlight-color: transparent
    }

    .muneer-theme-dark .switch input:checked+.slider {
      background-color: var(--muneer-color-dark)
    }

    .muneer-theme-dark .switch .slider {
      background-color: var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark .switch .slider:before {
      background-color: var(--muneer-color-dark)
    }

    .muneer-theme-dark .switch:hover .slider {
      background-color: var(--muneer-color-accent)
    }

    .muneer-theme-dark .switch:hover .slider:before {
      background-color: var(--muneer-color-dark)
    }

    .muneer-theme-dark .active .switch .slider:before {
      background-color: var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark .muneer-mode-title:before {
      background-color: var(--muneer-color-dark)
    }

    .muneer-theme-dark #muneer-voice-commands-input {
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark #muneer-language-switcher .choices__list.choices__list--dropdown {
      border: 2px solid var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark #muneer-language-switcher .choices.is-focused .choices__list.choices__list--single .muneer-language-switcher--lang-flag {
      box-shadow: 0 0 0 2px var(--muneer-color-accent);
      border: 2px solid var(--muneer-bg-dark)
    }

    .muneer-theme-dark #muneer-language-switcher .choices.is-focused .choices__list.choices__list--single .muneer-language-switcher--lang-name {
      color: var(--muneer-color-dark)
    }

    .muneer-theme-dark #muneer-language-switcher .choices__list.choices__list--dropdown {
      background: var(--muneer-bg-dark)
    }

    .muneer-theme-dark #muneer-language-switcher.choices-wrapper .choices__list--dropdown .choices__item--selectable.is-highlighted,
    .muneer-theme-dark #muneer-language-switcher.choices-wrapper .choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
      background: var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark #muneer-language-switcher.choices-wrapper .choices__list--dropdown .choices__item--selectable.is-highlighted .muneer-language-switcher--lang-name,
    .muneer-theme-dark #muneer-language-switcher.choices-wrapper .choices__list[aria-expanded] .choices__item--selectable.is-highlighted .muneer-language-switcher--lang-name {
      color: var(--muneer-color-accent)
    }

    .muneer-theme-dark #muneer-language-switcher.choices-wrapper .choices[data-type*=select-one] .choices__input {
      background: var(--muneer-bg-dark);
      color: var(--muneer-text-dark);
      border-bottom-color: var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark #muneer-theme-switcher fieldset {
      border: 2px solid var(--muneer-color-transparent-dark)
    }

    .muneer-theme-dark #muneer-theme-switcher label {
      background: var(--muneer-bg-dark);
      color: var(--muneer-text-dark)
    }

    .muneer-theme-dark #muneer-theme-switcher label:focus,
    .muneer-theme-dark #muneer-theme-switcher label:hover {
      background: var(--muneer-color-transparent-dark);
      color: var(--muneer-color-dark)
    }

    .muneer-theme-dark #muneer-theme-switcher label:focus {
      outline: 2px solid var(--muneer-color-accent)
    }

    .muneer-theme-dark #muneer-theme-switcher input[type=radio]:checked+label {
      color: var(--muneer-bg-dark);
      background: var(--muneer-color-dark)
    }

    .muneer-theme-dark #muneer-theme-switcher input[type=radio]:checked:focus+label {
      outline: 2px solid var(--muneer-color-accent);
      outline-offset: 2px
    }

    .muneer-theme-light #muneer-online-dictionary-search {
      background-color: var(--muneer-color-transparent)
    }

    .muneer-theme-light #muneer-online-dictionary-search::-moz-placeholder {
      color: var(--muneer-text)
    }

    .muneer-theme-light #muneer-online-dictionary-search::placeholder {
      color: var(--muneer-text)
    }

    .muneer-theme-light .muneer-useful-links-box .muneer-select-box {
      color: var(--muneer-text)
    }

    .muneer-theme-light .muneer-useful-links-box .muneer-select-box select {
      background-color: transparent
    }

    .muneer-theme-light .muneer-useful-links-box .muneer-select-box select:focus,
    .muneer-theme-light .muneer-useful-links-box .muneer-select-box select:hover {
      color: var(--muneer-color)
    }

    .muneer-theme-light .muneer-useful-links-box .muneer-select-box select:focus {
      box-shadow: 0 0 0 4px var(--muneer-bg), 0 0 0 6px var(--muneer-color-accent)
    }

    .muneer-theme-light .muneer-google-translate-box select {
      background-color: transparent;
      color: var(--muneer-text)
    }

    .muneer-theme-light .muneer-google-translate-box select:focus,
    .muneer-theme-light .muneer-google-translate-box select:hover {
      color: var(--muneer-color)
    }

    .muneer-theme-light .muneer-google-translate-box select:focus {
      box-shadow: 0 0 0 4px var(--muneer-bg), 0 0 0 6px var(--muneer-color-accent)
    }

    .muneer-theme-light .muneer-action-google-translate-icon {
      color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-popup-tools-box {
      border-top: 2px solid var(--muneer-color-transparent)
    }

    .muneer-theme-light #muneer-popup,
    .muneer-theme-light #muneer-sidebar,
    .muneer-theme-light #muneer-voice-nav-popup {
      background-color: var(--muneer-bg);
      color: var(--muneer-text)
    }

    .muneer-theme-light #muneer-popup h1,
    .muneer-theme-light #muneer-popup h2,
    .muneer-theme-light #muneer-popup h3,
    .muneer-theme-light #muneer-popup h4,
    .muneer-theme-light #muneer-popup h5,
    .muneer-theme-light #muneer-popup h6,
    .muneer-theme-light #muneer-sidebar h1,
    .muneer-theme-light #muneer-sidebar h2,
    .muneer-theme-light #muneer-sidebar h3,
    .muneer-theme-light #muneer-sidebar h4,
    .muneer-theme-light #muneer-sidebar h5,
    .muneer-theme-light #muneer-sidebar h6,
    .muneer-theme-light #muneer-voice-nav-popup h1,
    .muneer-theme-light #muneer-voice-nav-popup h2,
    .muneer-theme-light #muneer-voice-nav-popup h3,
    .muneer-theme-light #muneer-voice-nav-popup h4,
    .muneer-theme-light #muneer-voice-nav-popup h5,
    .muneer-theme-light #muneer-voice-nav-popup h6 {
      color: var(--muneer-text)
    }

    .muneer-theme-light #muneer-popup .muneer-subheader,
    .muneer-theme-light #muneer-sidebar .muneer-subheader,
    .muneer-theme-light #muneer-voice-nav-popup .muneer-subheader {
      border-top: 2px solid var(--muneer-color-transparent)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-main::-webkit-scrollbar-track,
    .muneer-theme-light #muneer-popup #muneer-voice-commands::-webkit-scrollbar-track,
    .muneer-theme-light #muneer-sidebar #muneer-popup-main::-webkit-scrollbar-track,
    .muneer-theme-light #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar-track,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar-track,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar-track {
      background-color: var(--muneer-bg)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-main::-webkit-scrollbar,
    .muneer-theme-light #muneer-popup #muneer-voice-commands::-webkit-scrollbar,
    .muneer-theme-light #muneer-sidebar #muneer-popup-main::-webkit-scrollbar,
    .muneer-theme-light #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar {
      background-color: var(--muneer-bg)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-main::-webkit-scrollbar-thumb,
    .muneer-theme-light #muneer-popup #muneer-voice-commands::-webkit-scrollbar-thumb,
    .muneer-theme-light #muneer-sidebar #muneer-popup-main::-webkit-scrollbar-thumb,
    .muneer-theme-light #muneer-sidebar #muneer-voice-commands::-webkit-scrollbar-thumb,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-main::-webkit-scrollbar-thumb,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-voice-commands::-webkit-scrollbar-thumb {
      background-color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-close,
    .muneer-theme-light #muneer-sidebar #muneer-popup-close,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-close {
      color: var(--muneer-text);
      border: 2px solid var(--muneer-color-transparent)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-close:before,
    .muneer-theme-light #muneer-sidebar #muneer-popup-close:before,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-close:before {
      color: var(--muneer-text)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-close:hover,
    .muneer-theme-light #muneer-sidebar #muneer-popup-close:hover,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-close:hover {
      color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-close:focus,
    .muneer-theme-light #muneer-sidebar #muneer-popup-close:focus,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-close:focus {
      outline: 2px solid var(--muneer-color-accent)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-buttons button,
    .muneer-theme-light #muneer-sidebar #muneer-popup-buttons button,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-buttons button {
      background: var(--muneer-color);
      color: var(--muneer-bg)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-buttons button:hover,
    .muneer-theme-light #muneer-sidebar #muneer-popup-buttons button:hover,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-buttons button:hover {
      background: var(--muneer-color-accent);
      color: var(--muneer-bg-dark)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-buttons button:focus,
    .muneer-theme-light #muneer-sidebar #muneer-popup-buttons button:focus,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-buttons button:focus {
      background: var(--muneer-color-accent);
      color: var(--muneer-bg-dark);
      box-shadow: 0 0 0 4px var(--muneer-bg), 0 0 0 6px var(--muneer-color-accent)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-footer,
    .muneer-theme-light #muneer-sidebar #muneer-popup-footer,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-footer {
      background: var(--muneer-color-transparent)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-footer .muneer-statement a,
    .muneer-theme-light #muneer-sidebar #muneer-popup-footer .muneer-statement a,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a {
      color: var(--muneer-text)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-footer .muneer-statement a:hover,
    .muneer-theme-light #muneer-sidebar #muneer-popup-footer .muneer-statement a:hover,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a:hover {
      color: var(--muneer-text)
    }

    .muneer-theme-light #muneer-popup #muneer-popup-footer .muneer-statement a:focus,
    .muneer-theme-light #muneer-sidebar #muneer-popup-footer .muneer-statement a:focus,
    .muneer-theme-light #muneer-voice-nav-popup #muneer-popup-footer .muneer-statement a:focus {
      outline: 2px solid var(--muneer-color);
      outline-offset: 5px
    }

    .muneer-theme-light #muneer-accessibility-modes-box .muneer-accessibility-mode-item.active .muneer-mode-description {
      background: var(--muneer-color-transparent)
    }

    .muneer-theme-light #muneer-accessibility-modes-box .muneer-accessibility-mode-item:focus .switch {
      box-shadow: 0 0 0 6px var(--muneer-bg), 0 0 0 8px var(--muneer-color)
    }

    .muneer-theme-light .muneer-action-box {
      background: var(--muneer-color-transparent);
      transition: all .2s ease-in-out
    }

    .muneer-theme-light .muneer-action-box:focus,
    .muneer-theme-light .muneer-action-box:hover {
      background-color: var(--muneer-color-transparent);
      transition: all .2s ease-in-out
    }

    .muneer-theme-light .muneer-action-box:focus {
      box-shadow: 0 0 0 4px var(--muneer-bg), 0 0 0 6px var(--muneer-color-accent)
    }

    .muneer-theme-light .muneer-multi-toggle-box .muneer-icon,
    .muneer-theme-light .muneer-toggle-box .muneer-icon {
      background-color: var(--muneer-color)
    }

    .muneer-theme-light .muneer-multi-toggle-box:hover,
    .muneer-theme-light .muneer-toggle-box:hover {
      background: var(--muneer-color-accent);
      color: var(--muneer-text)
    }

    .muneer-theme-light .muneer-multi-toggle-box:hover .muneer-icon,
    .muneer-theme-light .muneer-toggle-box:hover .muneer-icon {
      background-color: var(--muneer-text)
    }

    .muneer-theme-light .muneer-multi-toggle-box:focus,
    .muneer-theme-light .muneer-toggle-box:focus {
      background: var(--muneer-color-accent);
      color: var(--muneer-text)
    }

    .muneer-theme-light .muneer-multi-toggle-box:focus .muneer-icon,
    .muneer-theme-light .muneer-toggle-box:focus .muneer-icon {
      background-color: var(--muneer-text)
    }

    .muneer-theme-light .muneer-multi-toggle-box.active,
    .muneer-theme-light .muneer-toggle-box.active {
      background: var(--muneer-color);
      color: var(--muneer-bg)
    }

    .muneer-theme-light .muneer-multi-toggle-box.active .muneer-icon,
    .muneer-theme-light .muneer-toggle-box.active .muneer-icon {
      background-color: var(--muneer-bg)
    }

    .muneer-theme-light .muneer-palette-box .muneer-color-box .muneer-color:focus {
      box-shadow: 0 0 0 4px var(--muneer-color-transparent), 0 0 0 6px var(--muneer-color-accent)
    }

    .muneer-theme-light .muneer-palette-box .muneer-palette-expand:focus .muneer-palette-indicator,
    .muneer-theme-light .muneer-palette-box .muneer-palette-expand:focus svg,
    .muneer-theme-light .muneer-palette-box .muneer-palette-reset:focus .muneer-palette-indicator,
    .muneer-theme-light .muneer-palette-box .muneer-palette-reset:focus svg {
      outline: 2px solid var(--muneer-color-accent);
      outline-offset: 2px
    }

    .muneer-theme-light #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search {
      color: var(--muneer-text);
      outline-color: var(--muneer-color-accent)
    }

    .muneer-theme-light #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search:hover {
      color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-online-dictionary-box #muneer-online-dictionary-form #muneer-online-dictionary-search:focus {
      outline: 2px solid var(--muneer-color-accent);
      outline-offset: 4px
    }

    .muneer-theme-light #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li a,
    .muneer-theme-light #muneer-online-dictionary-box #muneer-online-dictionary-search-results-box ul li a.muneer-online-dictionary-link {
      color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-online-dictionary-box #muneer-online-dictionary-search-close {
      color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-accessibility-statement-box.open {
      background: var(--muneer-bg)
    }

    .muneer-theme-light #muneer-accessibility-statement-box.open::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .2);
      background-color: #f5f5f5
    }

    .muneer-theme-light #muneer-accessibility-statement-box.open::-webkit-scrollbar {
      background-color: #f5f5f5
    }

    .muneer-theme-light #muneer-accessibility-statement-box.open::-webkit-scrollbar-thumb {
      -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, .2);
      background-color: #a7a7a7
    }

    .muneer-theme-light #muneer-accessibility-statement-box #muneer-close-statement-btn:before {
      color: var(--muneer-text)
    }

    .muneer-theme-light #muneer-accessibility-statement-box #muneer-close-statement-btn:hover {
      color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-accessibility-statement-box #muneer-close-statement-btn:focus,
    .muneer-theme-light #muneer-accessibility-statement-box #muneer-close-statement-btn:hover {
      outline: 2px solid var(--muneer-color-accent)
    }

    .muneer-theme-light #muneer-keyboard-box {
      background-color: var(--muneer-keyboard-light-bg)
    }

    .muneer-theme-light #muneer-keyboard-box .simple-keyboard {
      background-color: var(--muneer-keyboard-light-bg)
    }

    .muneer-theme-light #muneer-keyboard-box .simple-keyboard .hg-button {
      background: var(--muneer-keyboard-light-key-bg);
      color: var(--muneer-keyboard-light-key);
      box-shadow: 0 0 3px -1px rgba(0, 0, 0, .3);
      border-bottom: 1px solid #b5b5b5;
      -webkit-tap-highlight-color: transparent
    }

    .muneer-theme-light #muneer-keyboard-box .simple-keyboard .hg-button.hg-activeButton {
      filter: invert(1)
    }

    .muneer-theme-light #muneer-keyboard-box .simple-keyboard .hg-button.hg-selectedButton {
      background: rgba(5, 25, 70, .53);
      color: #fff
    }

    .muneer-theme-light #muneer-keyboard-close {
      background: var(--muneer-keyboard-light-key-bg);
      color: var(--muneer-keyboard-light-key);
      box-shadow: 0 0 3px -1px rgba(0, 0, 0, .3);
      border-bottom: 1px solid #b5b5b5;
      -webkit-tap-highlight-color: transparent
    }

    .muneer-theme-light .switch input:checked+.slider {
      background-color: var(--muneer-color)
    }

    .muneer-theme-light .switch .slider {
      background-color: var(--muneer-color-transparent)
    }

    .muneer-theme-light .switch .slider:before {
      background-color: var(--muneer-color)
    }

    .muneer-theme-light .switch:hover .slider {
      background-color: var(--muneer-color-accent)
    }

    .muneer-theme-light .switch:hover .slider:before {
      background-color: var(--muneer-text)
    }

    .muneer-theme-light .active .switch .slider:before {
      background-color: var(--muneer-color-transparent)
    }

    .muneer-theme-light .muneer-mode-title:before {
      background-color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-voice-commands-input {
      color: var(--muneer-text)
    }

    .muneer-theme-light .muneer-command {
      background: var(--muneer-color-transparent);
      transition: all .2s ease-in-out;
      color: var(--muneer-color);
      outline-color: var(--muneer-color-accent)
    }

    .muneer-theme-light .muneer-command:focus,
    .muneer-theme-light .muneer-command:hover {
      transition: all .2s ease-in-out
    }

    .muneer-theme-light .muneer-command:hover {
      background: var(--muneer-color-accent);
      color: var(--muneer-text)
    }

    .muneer-theme-light .muneer-command:focus {
      background: var(--muneer-color-accent);
      color: var(--muneer-text);
      outline: 2px solid var(--muneer-color-accent);
      outline-offset: 4px
    }

    .muneer-theme-light .muneer-command.active {
      background: var(--muneer-color);
      color: var(--muneer-bg)
    }

    .muneer-theme-light #muneer-language-switcher .choices.is-focused .choices__list.choices__list--single .muneer-language-switcher--lang-flag {
      box-shadow: 0 0 0 2px var(--muneer-color-accent);
      border: 2px solid var(--muneer-bg)
    }

    .muneer-theme-light #muneer-language-switcher .choices.is-focused .choices__list.choices__list--single .muneer-language-switcher--lang-name {
      color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-language-switcher .choices__list.choices__list--dropdown {
      background: var(--muneer-bg)
    }

    .muneer-theme-light #muneer-language-switcher.choices-wrapper .choices__list--dropdown .choices__item--selectable.is-highlighted,
    .muneer-theme-light #muneer-language-switcher.choices-wrapper .choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
      background: var(--muneer-color-transparent)
    }

    .muneer-theme-light #muneer-language-switcher.choices-wrapper .choices__list--dropdown .choices__item--selectable.is-highlighted .muneer-language-switcher--lang-name,
    .muneer-theme-light #muneer-language-switcher.choices-wrapper .choices__list[aria-expanded] .choices__item--selectable.is-highlighted .muneer-language-switcher--lang-name {
      color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-theme-switcher fieldset {
      border: 2px solid var(--muneer-color-transparent)
    }

    .muneer-theme-light #muneer-theme-switcher label {
      background: var(--muneer-bg);
      color: var(--muneer-text)
    }

    .muneer-theme-light #muneer-theme-switcher label:focus,
    .muneer-theme-light #muneer-theme-switcher label:hover {
      background: var(--muneer-color-transparent);
      color: var(--muneer-color)
    }

    .muneer-theme-light #muneer-theme-switcher label:focus {
      outline: 2px solid var(--muneer-color-accent)
    }

    .muneer-theme-light #muneer-theme-switcher input[type=radio]:checked+label {
      color: var(--muneer-bg);
      background: var(--muneer-color)
    }

    .muneer-theme-light #muneer-theme-switcher input[type=radio]:checked:focus+label {
      outline: 2px solid var(--muneer-color-accent);
      outline-offset: 2px
    }

    .muneer-hidden {
      display: none !important
    }
  </style>
  <style>
    html {
      --muneer-color: #10615f !important;
      --muneer-color-accent: #1faf6f6b !important;
      --muneer-color-transparent: #eaf6ef !important;
      --muneer-text: #414042 !important;
      --muneer-btn-bg: #84BC47 !important;
      --muneer-btn-color-hover: #84BC47 !important;
    }

    body.muneer-readable-font [class^=icon-],
    [class*=" icon-"] font-family: icons,
    sans-serif !important;
    }
  </style>
  <style>
    .VIpgJd-ZVi9od-ORHb-OEVmcd {
      left: 0;
      top: 0;
      height: 39px;
      width: 100%;
      z-index: 10000001;
      position: fixed;
      border: none;
      border-bottom: 1px solid #6B90DA;
      margin: 0;
      box-shadow: 0 0 8px 1px #999
    }

    .VIpgJd-ZVi9od-xl07Ob-OEVmcd {
      z-index: 10000002;
      border: none;
      position: fixed;
      box-shadow: 0 3px 8px 2px #999
    }

    .VIpgJd-ZVi9od-SmfZ-OEVmcd {
      z-index: 10000000;
      border: none;
      margin: 0
    }

    .goog-te-gadget {
      font-family: arial;
      font-size: 11px;
      color: #666;
      white-space: nowrap
    }

    .goog-te-gadget img {
      vertical-align: middle;
      border: none
    }

    .goog-te-gadget-simple {
      background-color: #FFF;
      border-left: 1px solid #D5D5D5;
      border-top: 1px solid #9B9B9B;
      border-bottom: 1px solid #E8E8E8;
      border-right: 1px solid #D5D5D5;
      font-size: 10pt;
      display: inline-block;
      padding-top: 1px;
      padding-bottom: 2px;
      cursor: pointer
    }

    .goog-te-gadget-icon {
      margin-left: 2px;
      margin-right: 2px;
      width: 19px;
      height: 19px;
      border: none;
      vertical-align: middle
    }

    .goog-te-combo {
      margin-left: 4px;
      margin-right: 4px;
      vertical-align: baseline
    }

    .goog-te-gadget .goog-te-combo {
      margin: 4px 0
    }

    .VIpgJd-ZVi9od-l4eHX-hSRGPd,
    .VIpgJd-ZVi9od-l4eHX-hSRGPd:link,
    .VIpgJd-ZVi9od-l4eHX-hSRGPd:visited,
    .VIpgJd-ZVi9od-l4eHX-hSRGPd:hover,
    .VIpgJd-ZVi9od-l4eHX-hSRGPd:active {
      font-size: 12px;
      font-weight: bold;
      color: #444;
      text-decoration: none
    }

    .VIpgJd-ZVi9od-ORHb .VIpgJd-ZVi9od-l4eHX-hSRGPd,
    .VIpgJd-ZVi9od-TvD9Pc-hSRGPd {
      display: block;
      margin: 0 10px
    }

    .VIpgJd-ZVi9od-ORHb .VIpgJd-ZVi9od-l4eHX-hSRGPd {
      padding-top: 2px;
      padding-left: 4px
    }

    .goog-te-combo,
    .VIpgJd-ZVi9od-ORHb *,
    .VIpgJd-ZVi9od-SmfZ *,
    .VIpgJd-ZVi9od-xl07Ob *,
    .VIpgJd-ZVi9od-vH1Gmf *,
    .VIpgJd-ZVi9od-l9xktf * {
      font-family: arial;
      font-size: 10pt
    }

    .VIpgJd-ZVi9od-ORHb {
      margin: 0;
      background-color: #E4EFFB;
      overflow: hidden
    }

    .VIpgJd-ZVi9od-ORHb img {
      border: none
    }

    .VIpgJd-ZVi9od-ORHb-bN97Pc {
      color: #000
    }

    .VIpgJd-ZVi9od-ORHb-bN97Pc img {
      vertical-align: middle
    }

    .VIpgJd-ZVi9od-ORHb-Tswv1b {
      color: #666;
      vertical-align: top;
      margin-top: 0;
      font-size: 7pt
    }

    .VIpgJd-ZVi9od-ORHb-KE6vqe {
      width: 8px
    }

    .VIpgJd-ZVi9od-LgbsSe {
      border-color: #E7E7E7;
      border-style: none solid solid none;
      border-width: 0 1px 1px 0
    }

    .VIpgJd-ZVi9od-LgbsSe div {
      border-color: #CCC #999 #999 #CCC;
      border-right: 1px solid #999;
      border-style: solid;
      border-width: 1px;
      height: 20px
    }

    .VIpgJd-ZVi9od-LgbsSe button {
      background: transparent;
      border: none;
      cursor: pointer;
      height: 20px;
      overflow: hidden;
      margin: 0;
      vertical-align: top;
      white-space: nowrap
    }

    .VIpgJd-ZVi9od-LgbsSe button:active {
      background: none repeat scroll 0 0 #CCC
    }

    .VIpgJd-ZVi9od-SmfZ {
      margin: 0;
      background-color: #FFF;
      white-space: nowrap
    }

    .VIpgJd-ZVi9od-SmfZ-hSRGPd {
      text-decoration: none;
      font-weight: bold;
      font-size: 10pt;
      border: 1px outset #888;
      padding: 6px 10px;
      white-space: nowrap;
      position: absolute;
      left: 0;
      top: 0
    }

    .VIpgJd-ZVi9od-SmfZ-hSRGPd img {
      margin-left: 2px;
      margin-right: 2px;
      width: 19px;
      height: 19px;
      border: none;
      vertical-align: middle
    }

    .VIpgJd-ZVi9od-SmfZ-hSRGPd span {
      text-decoration: underline;
      margin-left: 2px;
      margin-right: 2px;
      vertical-align: middle
    }

    .goog-te-float-top .VIpgJd-ZVi9od-SmfZ-hSRGPd {
      padding: 2px;
      border-top-width: 0
    }

    .goog-te-float-bottom .VIpgJd-ZVi9od-SmfZ-hSRGPd {
      padding: 2px;
      border-bottom-width: 0
    }

    .VIpgJd-ZVi9od-xl07Ob-lTBxed {
      text-decoration: none;
      color: #00C;
      white-space: nowrap;
      margin-left: 4px;
      margin-right: 4px
    }

    .VIpgJd-ZVi9od-xl07Ob-lTBxed span {
      text-decoration: underline
    }

    .VIpgJd-ZVi9od-xl07Ob-lTBxed img {
      margin-left: 2px;
      margin-right: 2px
    }

    .goog-te-gadget-simple .VIpgJd-ZVi9od-xl07Ob-lTBxed {
      color: #000
    }

    .goog-te-gadget-simple .VIpgJd-ZVi9od-xl07Ob-lTBxed span {
      text-decoration: none
    }

    .VIpgJd-ZVi9od-xl07Ob {
      background-color: #FFF;
      text-decoration: none;
      border: 2px solid #C3D9FF;
      overflow-y: scroll;
      overflow-x: hidden;
      position: absolute;
      left: 0;
      top: 0
    }

    .VIpgJd-ZVi9od-xl07Ob-ibnC6b {
      padding: 3px;
      text-decoration: none
    }

    .VIpgJd-ZVi9od-xl07Ob-ibnC6b,
    .VIpgJd-ZVi9od-xl07Ob-ibnC6b:link {
      color: #00C;
      background: #FFF
    }

    .VIpgJd-ZVi9od-xl07Ob-ibnC6b:visited {
      color: #551A8B
    }

    .VIpgJd-ZVi9od-xl07Ob-ibnC6b:hover {
      background: #C3D9FF
    }

    .VIpgJd-ZVi9od-xl07Ob-ibnC6b:active {
      color: #00C
    }

    .VIpgJd-ZVi9od-vH1Gmf {
      background-color: #FFF;
      text-decoration: none;
      border: 1px solid #6B90DA;
      overflow: hidden;
      padding: 4px
    }

    .VIpgJd-ZVi9od-vH1Gmf-KrhPNb {
      width: 16px
    }

    .VIpgJd-ZVi9od-vH1Gmf-hgDUwe {
      margin: 6px 0;
      height: 1px;
      background-color: #aaa;
      overflow: hidden
    }

    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b div,
    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b-gk6SMd div {
      padding: 4px
    }

    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b .uDEFge {
      display: none
    }

    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b-gk6SMd .fmcmS {
      padding-left: 4px;
      padding-right: 4px
    }

    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b,
    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b-gk6SMd {
      text-decoration: none
    }

    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b div,
    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b:link div,
    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b:visited div,
    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b:active div {
      color: #00C;
      background: #FFF
    }

    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b:hover div {
      color: #FFF;
      background: #36C
    }

    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b-gk6SMd div,
    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b-gk6SMd:link div,
    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b-gk6SMd:visited div,
    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b-gk6SMd:hover div,
    .VIpgJd-ZVi9od-vH1Gmf-ibnC6b-gk6SMd:active div {
      color: #000;
      font-weight: bold
    }

    .VIpgJd-ZVi9od-l9xktf {
      background-color: #FFF;
      overflow: hidden;
      padding: 8px;
      border: none;
      border-radius: 10px
    }

    .VIpgJd-ZVi9od-l9xktf-OEVmcd {
      background-color: #FFF;
      border: 1px solid #6B90DA;
      box-shadow: 0 3px 8px 2px #999;
      border-radius: 8px
    }

    .VIpgJd-ZVi9od-l9xktf img {
      border: none
    }

    .VIpgJd-ZVi9od-l9xktf-fmcmS {
      margin-top: 6px
    }

    .VIpgJd-ZVi9od-l9xktf-VgwJlc {
      margin-top: 6px;
      white-space: nowrap
    }

    .VIpgJd-ZVi9od-l9xktf-VgwJlc * {
      vertical-align: middle
    }

    .VIpgJd-ZVi9od-l9xktf-VgwJlc .DUGJie {
      background-image: url("undefined")
    }

    .VIpgJd-ZVi9od-l9xktf-VgwJlc .TdyTDe {
      background-image: url("undefined")
    }

    .VIpgJd-ZVi9od-l9xktf-VgwJlc span {
      color: #00C;
      text-decoration: underline;
      cursor: pointer;
      margin: 0 4px
    }

    .VIpgJd-ZVi9od-l9xktf-I9GLp {
      margin: 6px 0 0
    }

    .VIpgJd-ZVi9od-l9xktf-I9GLp form {
      margin: 0
    }

    .VIpgJd-ZVi9od-l9xktf-I9GLp form textarea {
      margin-bottom: 4px;
      width: 100%
    }

    .VIpgJd-ZVi9od-l9xktf-yePe5c {
      margin: 6px 0 4px
    }

    .VIpgJd-ZVi9od-aZ2wEe-wOHMyf {
      z-index: 1000;
      position: fixed;
      -webkit-transition-delay: .6s;
      transition-delay: .6s;
      left: -1000px;
      top: -1000px
    }

    .VIpgJd-ZVi9od-aZ2wEe-wOHMyf-ti6hGc {
      -webkit-transition-delay: 0s;
      transition-delay: 0s;
      left: -14px;
      top: -14px
    }

    .VIpgJd-ZVi9od-aZ2wEe-OiiCO {
      display: -webkit-box;
      display: -webkit-flex;
      display: flex;
      -webkit-box-align: center;
      -webkit-align-items: center;
      align-items: center;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      width: 104px;
      height: 104px;
      border-radius: 50px;
      background: #FFF url("undefined") 50% 50% no-repeat;
      -webkit-transition: all .6s ease-in-out;
      transition: all .6s ease-in-out;
      -webkit-transform: scale(.4);
      transform: scale(.4);
      opacity: 0
    }

    .VIpgJd-ZVi9od-aZ2wEe-OiiCO-ti6hGc {
      -webkit-transform: scale(.5);
      transform: scale(.5);
      opacity: 1
    }

    .VIpgJd-ZVi9od-aZ2wEe {
      margin: 2px 0 0 2px;
      -webkit-animation: spinner-rotator 1.4s linear infinite;
      animation: spinner-rotator 1.4s linear infinite
    }

    @-webkit-keyframes spinner-rotator {
      0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
      }

      100% {
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg)
      }
    }