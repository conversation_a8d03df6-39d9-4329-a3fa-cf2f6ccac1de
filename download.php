<?php
require_once 'db_connect.php';
require_once 'inc/SecureIdHandler.php';

// التحقق من وجود معرف الشهادة
if (!isset($_GET['id'])) {
    die("معرف الشهادة غير محدد");
}

$encryptedId = $_GET['id'];

// فك تشفير الـ ID
$id = SecureIdHandler::decryptId($encryptedId);

if ($id === false) {
    die("معرف الشهادة غير صالح");
}

// جلب بيانات الشهادة من قاعدة البيانات
try {
    $stmt = $pdo->prepare("SELECT * FROM certificates WHERE id_number = ?");
    $stmt->execute([$id]);
    $certificate = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$certificate) {
        die("الشهادة غير موجودة");
    }

    // تحضير الصورة للعرض
    $photo_base64 = '';
    if (!empty($certificate['photo'])) {
        // تحويل البيانات الثنائية للصورة إلى base64
        $photo_data = $certificate['photo'];
        // التحقق مما إذا كانت البيانات بالفعل بتنسيق base64
        if (base64_decode($photo_data, true) === false) {
            // إذا لم تكن base64، قم بتحويلها
            $photo_base64 = 'data:image/jpeg;base64,' . base64_encode($photo_data);
        } else {
            // إذا كانت بالفعل base64، أضف فقط البادئة
            $photo_base64 = 'data:image/jpeg;base64,' . $photo_data;
        }
    }

    // تحضير رمز QR للعرض
    $qr_base64 = '';
    if (!empty($certificate['qr'])) {
        // تحويل البيانات الثنائية لرمز QR إلى base64
        $qr_data = $certificate['qr'];
        // التحقق مما إذا كانت البيانات بالفعل بتنسيق base64
        if (base64_decode($qr_data, true) === false) {
            // إذا لم تكن base64، قم بتحويلها
            $qr_base64 = 'data:image/png;base64,' . base64_encode($qr_data);
        } else {
            // إذا كانت بالفعل base64، أضف فقط البادئة
            $qr_base64 = 'data:image/png;base64,' . $qr_data;
        }
    }

    // تحضير شعار الموقع للعرض
    $logo_base64 = '';
    if (!empty($certificate['imgLogo'])) {
        // التحقق مما إذا كان imgLogo يحتوي على مسار ملف أو بيانات ثنائية
        if (file_exists($certificate['imgLogo'])) {
            // إذا كان مسار ملف صحيح
            $logo_base64 = $certificate['imgLogo'];
        } else {
            // إذا كان بيانات ثنائية
            if (is_resource($certificate['imgLogo'])) {
                // لو جاي كـ stream من PDO
                $logo_data = stream_get_contents($certificate['imgLogo']);
            } else {
                // لو جاي كـ binary string
                $logo_data = $certificate['imgLogo'];
            }

            if (!empty($logo_data)) {
                $logo_base64 = 'data:image/png;base64,' . base64_encode($logo_data);
            } else {
                $logo_base64 = 'imgs/RYAD1.png'; // fallback
            }
        }
    } else {
        $logo_base64 = 'imgs/RYAD1.png'; // fallback
    }
} catch(PDOException $e) {
    die("خطأ في جلب البيانات: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الشهادة الصحية</title>
    <link rel="stylesheet" href="main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" rel="stylesheet"/>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100..900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Kufi Arabic', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .certificate-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .certificate-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .certificate-title {
            color: #007c79;
            font-size: 28px;
            margin-bottom: 10px;
        }
        .certificate-content {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .certificate-info {
            flex: 2;
        }
        .certificate-images {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 15px;
        }
        .info-label {
            font-weight: bold;
            width: 150px;
            color: #555;
        }
        .info-value {
            flex: 1;
        }
        .image-container {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
        }
        .download-btn {
            display: block;
            width: 200px;
            margin: 30px auto;
            padding: 10px;
            background-color: #007c79;
            color: white;
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
        }
        @media print {
            .download-btn {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        <div class="certificate-header">
            <h1 class="certificate-title">الشهادة الصحية الموحدة</h1>
        </div>

        <div class="certificate-content">
            <div class="certificate-info">
                <div class="info-row">
                    <div class="info-label">الاسم:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['person_name']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">رقم الهوية:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['id_number']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">الجنسية:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['nationality']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">المهنة:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['job']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">الجنس:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['gender']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">الأمانة:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['amanah']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">البلدية:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['baladiah']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">رقم الرخصة:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['license_number']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">اسم المنشأة:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['place_name']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">رقم المنشأة:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['place_number']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">رقم الشهادة:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['cert_number']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">تاريخ الإصدار:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['issue_date_hijri']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">تاريخ الانتهاء:</div>
                    <div class="info-value"><?php echo htmlspecialchars($certificate['expire_date_hijri']); ?></div>
                </div>
                <?php if (!empty($certificate['linkWeb'])): ?>
                <div class="info-row">
                    <div class="info-label">رابط الموقع:</div>
                    <div class="info-value"><a href="<?php echo htmlspecialchars($certificate['linkWeb']); ?>" target="_blank"><?php echo htmlspecialchars($certificate['linkWeb']); ?></a></div>
                </div>
                <?php endif; ?>
            </div>

            <div class="certificate-images">
                <div class="image-container">
                    <h3>الصورة الشخصية</h3>
                    <img src="<?php echo $photo_base64; ?>" alt="الصورة الشخصية">
                </div>
                <div class="image-container">
                    <h3>رمز QR</h3>
                    <img src="<?php echo $qr_base64; ?>" alt="رمز QR">
                </div>
                <div class="image-container">
                    <h3>شعار الموقع</h3>
                    <img src="<?php echo $logo_base64; ?>" alt="شعار الموقع">
                </div>
            </div>
        </div>

        <a href="#" class="download-btn" onclick="window.print()">طباعة الشهادة</a>
    </div>
</body>
</html>

1. **حفظ شهادة جديدة ():**
   - حقل شعار الموقع يجب أن يكون اختياري.
   - إذا لم يقم المستخدم برفع صورة (`$_FILES['logoFile']` فارغ أو `UPLOAD_ERR_NO_FILE`):
       - عمود `imgLogo` في قاعدة البيانات يجب أن يُترك NULL أو سلسلة فارغة حسب تصميم DB.
       - العملية يجب أن تنفذ INSERT بنجاح بدون خطأ SQL (مثل 1048).
   - إذا تم رفع صورة:
       - تحقق من نوع الملف (JPEG, PNG, GIF, WEBP) وحجمه (مثلاً ≤ 2MB).
       - احفظ الملف في مجلد آمن (uploads/logos/) وادخل اسم الملف في عمود `imgLogo`.
   - استخدام prepared statements (PDO) لمنع SQL injection.

2. **تعديل شهادة موجودة (UPDATE):**
   - عند تعديل الشهادة، **إذا لم يتم رفع ملف شعار جديد**:
       - لا تغيّر عمود `imgLogo` في قاعدة البيانات (احتفظ بالقيمة القديمة).
       - لا تمرّر NULL أو '' إلا إذا كنت تريد فعليًا مسح الشعار.
   - إذا تم رفع ملف جديد:
       - احفظ الملف بنفس الطريقة الآمنة (التحقق من النوع والحجم، اسم عشوائي، move_uploaded_file).
       - حدّث عمود `imgLogo` بالاسم الجديد.
       - يمكنك حذف الملف القديم إذا رغبت بذلك.

3. **HTML / form.php:**
   - تأكد أن حقل إدخال الملف ليس مطلوبًا (`required` غير موجود):

   

4

   - هذا يضمن أن عمود `imgLogo` **لا يُفرغ إلا إذا تم رفع ملف جديد**، ويحافظ على القيمة القديمة عند عدم رفع ملف.

5. **قواعد قاعدة البيانات (اختياري لكنها موصى بها):**
   - اجعل عمود imgLogo يقبل NULL:
     ```sql
     ALTER TABLE certificates MODIFY imgLogo VARCHAR(255) NULL DEFAULT NULL;
     ```
   - أو إذا تريد قيمة افتراضية فارغة:  
     ```sql
     ALTER TABLE certificates MODIFY imgLogo VARCHAR(255) NOT NULL DEFAULT '';
     ```

6. **الأمان:**
   - تحقق من MIME type وحجم الملف.
   - استخدم أسماء ملفات عشوائية أو UUID لتجنب استبدال الملفات.
   - خزن فقط اسم أو مسار الملف في قاعدة البيانات، وليس محتوى الصورة.

7. **النتيجة المتوقعة بعد التطبيق:**
   - حفظ شهادة جديدة بدون شعار → يتم الحفظ بدون خطأ.
   - حفظ شهادة جديدة مع شعار → يتم الحفظ مع الصورة.
   - تعديل شهادة بدون رفع شعار جديد → يحتفظ الشعار القديم.
   - تعديل شهادة مع شعار جديد → يتم استبدال الشعار.

---

هذا البرومبت شامل لكل الحالات: INSERT، UPDATE، التحقق من الملف، قاعدة البيانات، وحماية النظام. يمكن تسليمه مباشرة للذكاء الاصطناعي أو المطوّر لتوليد الكود الجاهز.  
