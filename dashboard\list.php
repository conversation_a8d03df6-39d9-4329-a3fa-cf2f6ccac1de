<?php
require_once '../db_connect.php';
require_once 'auth.php';
require_once '../inc/SecureIdHandler.php';

// معالجة حذف شهادة
if (isset($_GET['delete_id'])) {
    try {
        // فك تشفير الـ ID
        $decrypted_id = SecureIdHandler::decryptId($_GET['delete_id']);
        if ($decrypted_id === false) {
            $error = "معرف الشهادة غير صالح";
        } else {
            // تنفيذ الحذف الفعلي باستخدام prepared statement
            $stmt = $pdo->prepare("DELETE FROM certificates WHERE id_number = :id LIMIT 1");
            $stmt->bindParam(':id', $decrypted_id);
            $stmt->execute();

            // التحقق من عدد الصفوف المتأثرة
            $affected_rows = $stmt->rowCount();

            if ($affected_rows === 1) {
                $message = "تم حذف الشهادة بنجاح!";
                // إعادة التوجيه إلى صفحة القائمة بعد الحذف
                header("Location: list.php");
                exit;
            } elseif ($affected_rows === 0) {
                $error = "لم يتم العثور على الشهادة";
            } else {
                $error = "تم حذف عدد غير متوقع من الشهادات: " . $affected_rows;
            }
        }
    } catch(PDOException $e) {
        $error = "خطأ في الحذف: " . $e->getMessage();
    }
}

// إعدادات الترقيم
$records_per_page = 10; // عدد الشهادات في كل صفحة
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1; // الصفحة الحالية
$offset = ($page - 1) * $records_per_page; // حساب الإزاحة

// جلب الشهادات مع الترقيم
try {
    // الحصول على العدد الإجمالي للشهادات
    $count_stmt = $pdo->query("SELECT COUNT(*) FROM certificates");
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $records_per_page);

    // جلب الشهادات للصفحة الحالية
    $stmt = $pdo->prepare("SELECT * FROM certificates ORDER BY created_at DESC LIMIT :offset, :limit");
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $records_per_page, PDO::PARAM_INT);
    $stmt->execute();
    $certificates = $stmt->fetchAll();
} catch(PDOException $e) {
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
    $certificates = [];
    $total_pages = 0;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>قائمة الشهادات الصحية</title>
    <style>
        body {
            font-family: sans-serif;
            padding: 20px;
            background-color: #f9f9f9;
            direction: rtl;
        }
        h2 { color: #007c79; }
        .card {
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #fff;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: relative;
        }
        .card strong { color: #555; }
        .card .btn-group {
            margin-top: 10px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .card button {
            background: #007c79;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
        }
        .card button.delete {
            background: #c62828;
        }
        .card button:hover {
            filter: brightness(0.9);
        }
        .search-container {
            margin-bottom: 20px;
            position: relative;
        }
        .search-input {
            width: 95%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .search-input:focus {
            outline: none;
            border-color: #007c79;
            box-shadow: 0 0 5px rgba(0,124,121,0.3);
        }
        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 10px 0;
            color: #007c79;
        }
        .no-results {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
            background-color: #f5f5f5;
            border-radius: 5px;
            margin: 10px 0;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            list-style: none;
            padding: 0;
        }
        .pagination li {
            margin: 0 5px;
        }
        .pagination a, .pagination span {
            display: block;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #007c79;
            background-color: #fff;
        }
        .pagination a:hover {
            background-color: #f5f5f5;
        }
        .pagination .active a, .pagination .active span {
            background-color: #007c79;
            color: white;
            border-color: #007c79;
        }
        .pagination .disabled span {
            color: #999;
            cursor: not-allowed;
        }
        .buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 30px;
        }
        .buttons button {
            flex: 1 1 150px;
            padding: 12px;
            font-size: 16px;
            background: #444;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        .buttons button:hover { background: #222; }
        .backup-controls {
            margin: 30px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .backup-controls button {
            flex: 1 1 200px;
            padding: 12px;
            font-size: 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            background-color: #007c79;
            color: white;
        }
        .backup-controls button:hover { background-color: #005f5c; }
        .certificatesList {
            /* cursor: pointer; */
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #c62828;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
        }
        .logout-btn:hover {
            background: #a02622;
        }
    </style>
</head>
<body>
<a href="logout.php" class="logout-btn">تسجيل الخروج</a>


<div style="margin-bottom: 15px;">
    <button id="toggleSortBtn" style="padding: 8px 16px; border:none; border-radius:6px; background:#007c79; color:white; cursor:pointer;">
        🔽 ترتيب: الأحدث أولاً
    </button>
</div>

    <h2>📋 قائمة الشهادات الصحية المحفوظة</h2>

    <?php if (isset($message)): ?>
        <div class="message success"><?php echo $message; ?></div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="message error"><?php echo $error; ?></div>
    <?php endif; ?>

    <div class="search-container">
        <input type="text" id="searchInput" class="search-input" placeholder="ابحث بالاسم أو رقم الهوية...">
        <span class="search-icon">🔍</span>
    </div>

    <div id="loading" class="loading">جاري البحث...</div>
    <div id="noResults" class="no-results">لم يتم العثور على نتائج</div>



    <div id="certificatesList">
        <?php if (empty($certificates)): ?>
            <p>لا توجد شهادات محفوظة.</p>
        <?php else: ?>
            <?php foreach ($certificates as $cert): ?>
                <div class="card">
                    <strong>الاسم:</strong> <?php echo htmlspecialchars($cert['person_name']); ?><br>
                    <strong>رقم الهوية:</strong> <?php echo htmlspecialchars($cert['id_number']); ?><br>
                    <strong>رقم الشهادة:</strong> <?php echo htmlspecialchars($cert['cert_number']); ?><br>
                    <div class="btn-group">
                        <button onclick="openCertificate('<?php echo SecureIdHandler::encryptId($cert['id_number']); ?>')">🖨️ الشهاده  الصحية  الموحدة  </button>
                        <button onclick="HealthCertificate('<?php echo SecureIdHandler::encryptId($cert['id_number']); ?>')">الشهاده الصحية    </button>
                        <button onclick="AnnualHealth('<?php echo SecureIdHandler::encryptId($cert['id_number']); ?>')">شهادة صحية سنوية    </button>
                                                <button onclick="ylou('<?php echo SecureIdHandler::encryptId($cert['id_number']); ?>')">الازرق الجديد      </button>

                        <button onclick="openIndexCopy('<?php echo SecureIdHandler::encryptId($cert['id_number']); ?>')">📄 النسخة القابلة للنسخ</button>
                        <button onclick="bluYrleay('<?php echo SecureIdHandler::encryptId($cert['id_number']); ?>')">📄   السنوية  الازرق </button>
                        <a href="form.php?id=<?php echo SecureIdHandler::encryptId($cert['id_number']); ?>" target="_blank">
                            <button style="background-color: orange;">✏️ تعديل</button>
                        </a>
                        <button onclick="downloadFilledPage('<?php echo SecureIdHandler::encryptId($cert['id_number']); ?>')" style="background-color: #008a2e;">📥 تحميل</button>
                        <button class="delete" onclick="deleteCertificate('<?php echo SecureIdHandler::encryptId($cert['id_number']); ?>')">🗑️ حذف</button>
                        
                        
    <?php if ($cert['is_active'] == 1): ?>
    <button class="toggle-btn" style="background:red;color:white"
            onclick="toggleStatus('<?= SecureIdHandler::encryptId($cert['id_number']) ?>', this)">
        تعطيل الشهادة
    </button>
<?php else: ?>
    <button class="toggle-btn" style="background:green;color:white"
            onclick="toggleStatus('<?= SecureIdHandler::encryptId($cert['id_number']) ?>', this)">
        تفعيل الشهادة
    </button>
<?php endif; ?>



                        
                        
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- أزرار الترقيم -->
    <?php if ($total_pages > 1): ?>
    <div class="pagination-container">
        <ul class="pagination">
            <?php if ($page > 1): ?>
                <li><a href="?page=1">الأولى</a></li>
                <li><a href="?page=<?php echo $page - 1; ?>">السابق</a></li>
            <?php else: ?>
                <li class="disabled"><span>الأولى</span></li>
                <li class="disabled"><span>السابق</span></li>
            <?php endif; ?>

            <?php 
            // تحديد نطاق أرقام الصفحات التي سيتم عرضها
            $start_page = max(1, $page - 2);
            $end_page = min($total_pages, $page + 2);

            // عرض رابط للصفحة الأولى إذا لم تكن في النطاق
            if ($start_page > 1): ?>
                <li><a href="?page=1">1</a></li>
                <?php if ($start_page > 2): ?>
                <li class="disabled"><span>...</span></li>
                <?php endif; ?>
            <?php endif; ?>

            <?php // عرض أرقام الصفحات في النطاق
            for ($i = $start_page; $i <= $end_page; $i++): ?>
                <?php if ($i == $page): ?>
                    <li class="active"><span><?php echo $i; ?></span></li>
                <?php else: ?>
                    <li><a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a></li>
                <?php endif; ?>
            <?php endfor; ?>

            <?php // عرض رابط للصفحة الأخيرة إذا لم تكن في النطاق
            if ($end_page < $total_pages): ?>
                <?php if ($end_page < $total_pages - 1): ?>
                <li class="disabled"><span>...</span></li>
                <?php endif; ?>
                <li><a href="?page=<?php echo $total_pages; ?>"><?php echo $total_pages; ?></a></li>
            <?php endif; ?>

            <?php if ($page < $total_pages): ?>
                <li><a href="?page=<?php echo $page + 1; ?>">التالي</a></li>
                <li><a href="?page=<?php echo $total_pages; ?>">الأخيرة</a></li>
            <?php else: ?>
                <li class="disabled"><span>التالي</span></li>
                <li class="disabled"><span>الأخيرة</span></li>
            <?php endif; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="backup-controls">
        <button onclick="backupCertificates()">💾 حفظ نسخة احتياطية</button>
        <button onclick="document.getElementById('backupFileInput').click()">🔄 استعادة نسخة احتياطية</button>
        <input type="file" id="backupFileInput" style="display: none" accept=".json" onchange="restoreCertificates(this)" />
    </div>
    <div class="buttons">
        <button onclick="window.location.href='form.php'">📝 صفحة نموذج الإدخال</button>
        <button onclick="window.location.href='certificate.php'">🔍 عرض شهادة افتراضية</button>
        <button onclick="window.location.href='../copy_link.php'" target="_blank">تعديل الرابط</button>
    </div>

<script>
    function openCertificate(id) {
        window.open("certificate.php?id=" + id, "_blank");
    }


 function ylou(id) {
        window.open("ylou.php?id=" + id, "_blank");
    }



     function bluYrleay(id) {
        window.open("bluYrleay.php?id=" + id, "_blank");
    }


 function HealthCertificate(id) {
        window.open("Health-Certificate.php?id=" + id, "_blank");
    }


     function AnnualHealth(id) {
        window.open("annual-Health.php?id=" + id, "_blank");
    }


    function openIndexCopy(id) {
        window.open("../int/int-us.php?id=" + id, "_blank");
    }


    

    function deleteCertificate(id) {
        if (!confirm("هل أنت متأكد من حذف هذه الشهادة؟")) return;
        // نحتاج إلى فك تشفير الـ ID في الخادم
        window.location.href = "list.php?delete_id=" + id;
    }

    function backupCertificates() {
        // إنشاء طلب AJAX لجلب البيانات
        fetch('../backup.php')
            .then(response => response.json())
            .then(data => {
                // تحويل البيانات إلى نص JSON
                const dataStr = JSON.stringify(data, null, 2);
                // إنشاء ملف للتحميل
                const blob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'certificates_backup.json';
                a.click();
                URL.revokeObjectURL(url);
            })
            .catch(error => {
                alert('حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' + error);
            });
    }

    function restoreCertificates(input) {
        const file = input.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);

                // إرسال البيانات إلى الخادم لاستعادتها
                fetch('../restore.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        alert('✅ تمت الاستعادة بنجاح');
                        location.reload();
                    } else {
                        alert('❌ ' + result.message);
                    }
                })
                .catch(error => {
                    alert('❌ خطأ في الاتصال بالخادم: ' + error);
                });
            } catch (err) {
                alert('❌ الملف غير صالح');
            }
        };
        reader.readAsText(file);
    }

    function downloadFilledPage(id) {
        window.open("../download.php?id=" + id, "_blank");
    }

    // البحث الفوري
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const loading = document.getElementById('loading');
        const noResults = document.getElementById('noResults');
        const certificatesList = document.getElementById('certificatesList');

        // متغير لتخزين مؤقت للبحث (debounce)
        let searchTimeout;

        // مستمع حدث للبحث أثناء الكتابة
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim();

            // إلغاء البحث السابق إذا كان قيد التنفيذ
            clearTimeout(searchTimeout);

            // إخفاء رسالة عدم وجود نتائج
            noResults.style.display = 'none';

            // إذا كان حقل البحث فارغًا، عرض جميع الشهادات
            if (searchTerm === '') {
                loading.style.display = 'none';
                // إعادة تحميل الصفحة لعرض جميع الشهادات
                location.reload();
                return;
            }

            // عرض رسالة التحميل
            loading.style.display = 'block';

            // استخدام debounce لتقليل عدد الطلبات
            searchTimeout = setTimeout(function() {
                performSearch(searchTerm);
            }, 300); // انتظار 300 مللي ثانية بعد التوقف عن الكتابة
        });

        // دالة تنفيذ البحث
        function performSearch(searchTerm) {
            // إرسال طلب AJAX لملف البحث
            fetch('search.php?q=' + encodeURIComponent(searchTerm))
                .then(response => response.json())
                .then(data => {
                    // إخفاء رسالة التحميل
                    loading.style.display = 'none';

                    if (data.error) {
                        // عرض رسالة الخطأ
                        certificatesList.innerHTML = '<div class="message error">' + data.error + '</div>';
                        return;
                    }

                    if (data.data && data.data.length > 0) {
                        // عرض النتائج
                        displayResults(data.data);

                        // عرض أزرار الترقيم إذا لزم الأمر
                        if (data.pagination && data.pagination.total_pages > 1) {
                            displayPagination(data.pagination, searchTerm);
                        } else {
                            // إخفاء أزرار الترقيم الحالية إذا كانت موجودة
                            const existingPagination = document.querySelector('.pagination-container');
                            if (existingPagination) {
                                existingPagination.remove();
                            }
                        }
                    } else {
                        // عرض رسالة عدم وجود نتائج
                        noResults.style.display = 'block';
                        certificatesList.innerHTML = '';

                        // إخفاء أزرار الترقيم الحالية إذا كانت موجودة
                        const existingPagination = document.querySelector('.pagination-container');
                        if (existingPagination) {
                            existingPagination.remove();
                        }
                    }
                })
                .catch(error => {
                    // إخفاء رسالة التحميل
                    loading.style.display = 'none';
                    // عرض رسالة الخطأ
                    certificatesList.innerHTML = '<div class="message error">حدث خطأ في البحث: ' + error.message + '</div>';
                });
        }

        // دالة عرض النتائج
       function displayResults(results) {
            let html = '';

            // إنشاء HTML لكل نتيجة
            results.forEach(cert => {
                html += '<div class="card">';
                html += '<strong>' + (cert.person_name || 'غير محدد') + '</strong>';
                html += '<div>رقم الهوية: ' + (cert.id_number || 'غير محدد') + '</div>';
                html += '<div>الجنسية: ' + (cert.nationality || 'غير محدد') + '</div>';
                html += '<div>المهنة: ' + (cert.job || 'غير محدد') + '</div>';
                html += '<div>رقم الشهادة: ' + (cert.cert_number || 'غير محدد') + '</div>';
                html += '<div>تاريخ الإصدار: ' + (cert.issue_date_hijri || 'غير محدد') + '</div>';
                html += '<div>تاريخ الانتهاء: ' + (cert.expire_date_hijri || 'غير محدد') + '</div>';

                html += '<div class="btn-group">';
                html += '<button onclick="window.open(\'certificate.php?id=' + cert.encrypted_id + '\', \'_blank\')">📄 عرض     الشهادة</button>';
                html += '<button onclick="window.open(\'../int/int-us.php?id=' + cert.encrypted_id + '\', \'_blank\')">🌐 عرض النسخة الإنجليزية</button>';
                html += '<button onclick="downloadFilledPage(\'' + cert.encrypted_id + '\')">⬇️ تحميل الصفحة المعبأة</button>';
                
                // إضافة الزر الجديد هنا
                html += '<button onclick="bluYrleay(\'' + cert.id_number + '\')">📄   السنوية  الازرق </button>';

                html += '<button class="delete" onclick="confirmDelete(\'' + cert.encrypted_id + '\')">🗑️ حذف</button>';
                
                html += '</div>';
                html += '</div>';
            });

            // تحديث محتوى قائمة الشهادات
            certificatesList.innerHTML = html;
        }
        // دالة عرض أزرار الترقيم لنتائج البحث
        function displayPagination(pagination, searchTerm) {
            // إزالة أزرار الترقيم الحالية إذا كانت موجودة
            const existingPagination = document.querySelector('.pagination-container');
            if (existingPagination) {
                existingPagination.remove();
            }

            // إنشاء حاوية أزرار الترقيم
            const paginationContainer = document.createElement('div');
            paginationContainer.className = 'pagination-container';

            // إنشاء قائمة أزرار الترقيم
            const paginationList = document.createElement('ul');
            paginationList.className = 'pagination';

            const currentPage = pagination.current_page;
            const totalPages = pagination.total_pages;

            // إضافة زر "الأولى" و "السابق"
            if (currentPage > 1) {
                paginationList.appendChild(createPaginationItem(1, 'الأولى', searchTerm));
                paginationList.appendChild(createPaginationItem(currentPage - 1, 'السابق', searchTerm));
            } else {
                paginationList.appendChild(createPaginationDisabledItem('الأولى'));
                paginationList.appendChild(createPaginationDisabledItem('السابق'));
            }

            // تحديد نطاق أرقام الصفحات التي سيتم عرضها
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            // عرض رابط للصفحة الأولى إذا لم تكن في النطاق
            if (startPage > 1) {
                paginationList.appendChild(createPaginationItem(1, '1', searchTerm));
                if (startPage > 2) {
                    paginationList.appendChild(createPaginationDisabledItem('...'));
                }
            }

            // عرض أرقام الصفحات في النطاق
            for (let i = startPage; i <= endPage; i++) {
                if (i === currentPage) {
                    paginationList.appendChild(createPaginationActiveItem(i));
                } else {
                    paginationList.appendChild(createPaginationItem(i, i, searchTerm));
                }
            }

            // عرض رابط للصفحة الأخيرة إذا لم تكن في النطاق
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationList.appendChild(createPaginationDisabledItem('...'));
                }
                paginationList.appendChild(createPaginationItem(totalPages, totalPages, searchTerm));
            }

            // إضافة زر "التالي" و "الأخيرة"
            if (currentPage < totalPages) {
                paginationList.appendChild(createPaginationItem(currentPage + 1, 'التالي', searchTerm));
                paginationList.appendChild(createPaginationItem(totalPages, 'الأخيرة', searchTerm));
            } else {
                paginationList.appendChild(createPaginationDisabledItem('التالي'));
                paginationList.appendChild(createPaginationDisabledItem('الأخيرة'));
            }

            // إضافة القائمة إلى الحاوية
            paginationContainer.appendChild(paginationList);

            // إضافة الحاوية بعد قائمة الشهادات
            certificatesList.insertAdjacentElement('afterend', paginationContainer);
        }

        // دالة إنشاء عنصر ترقيم عادي
        function createPaginationItem(page, text, searchTerm) {
            const li = document.createElement('li');
            const a = document.createElement('a');
            a.href = '#';
            a.textContent = text;
            a.addEventListener('click', function(e) {
                e.preventDefault();
                performSearchWithPage(searchTerm, page);
            });
            li.appendChild(a);
            return li;
        }

        // دالة إنشاء عنصر ترقيم نشط
        function createPaginationActiveItem(page) {
            const li = document.createElement('li');
            li.className = 'active';
            const span = document.createElement('span');
            span.textContent = page;
            li.appendChild(span);
            return li;
        }

        // دالة إنشاء عنصر ترقيم معطل
        function createPaginationDisabledItem(text) {
            const li = document.createElement('li');
            li.className = 'disabled';
            const span = document.createElement('span');
            span.textContent = text;
            li.appendChild(span);
            return li;
        }

        // دالة تنفيذ البحث مع تحديد الصفحة
        function performSearchWithPage(searchTerm, page) {
            // عرض رسالة التحميل
            loading.style.display = 'block';

            // إخفاء رسالة عدم وجود نتائج
            noResults.style.display = 'none';

            // إرسال طلب AJAX لملف البحث مع تحديد الصفحة
            fetch('search.php?q=' + encodeURIComponent(searchTerm) + '&page=' + page)
                .then(response => response.json())
                .then(data => {
                    // إخفاء رسالة التحميل
                    loading.style.display = 'none';

                    if (data.error) {
                        // عرض رسالة الخطأ
                        certificatesList.innerHTML = '<div class="message error">' + data.error + '</div>';
                        return;
                    }

                    if (data.data && data.data.length > 0) {
                        // عرض النتائج
                        displayResults(data.data);

                        // عرض أزرار الترقيم إذا لزم الأمر
                        if (data.pagination && data.pagination.total_pages > 1) {
                            displayPagination(data.pagination, searchTerm);
                        }
                    } else {
                        // عرض رسالة عدم وجود نتائج
                        noResults.style.display = 'block';
                        certificatesList.innerHTML = '';
                    }
                })
                .catch(error => {
                    // إخفاء رسالة التحميل
                    loading.style.display = 'none';
                    // عرض رسالة الخطأ
                    certificatesList.innerHTML = '<div class="message error">حدث خطأ في البحث: ' + error.message + '</div>';
                });
        }
    });
    
    
    
    
    
    
    
    
    
    
    
    
    
    
   


function toggleStatus(encryptedId, btn) {
    if (!encryptedId) {
        alert('لم يتم إرسال الايدي!');
        return;
    }

    fetch('../toggle_status.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=utf-8' },
        body: JSON.stringify({ id: encryptedId })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('الخادم أعاد حالة خطأ: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            if (data.newStatus == 1) {
                btn.innerText = 'تعطيل الشهادة';
                btn.style.background = 'red';
            } else {
                btn.innerText = 'تفعيل الشهادة';
                btn.style.background = 'green';
            }
        } else {
            alert('فشل العملية: ' + data.message);
        }
    })
    .catch(err => {
        console.error('خطأ:', err);
        alert('خطأ في الاتصال: ' + err.message);
    });
} 
    
    
    
    
    
    
    
    
</script>
















</body>
</html>