<?php
// 1. حدد كلمة المرور النصية التي تريد تشفيرها
$plaintext_password = 'najeeb777';

// 2. استخدم الدالة الآمنة password_hash لتوليد التشفير
// (PASSWORD_DEFAULT يستخدم خوارزمية bcrypt حالياً وهي الخيار الأفضل)
$hashed_password = password_hash($plaintext_password, PASSWORD_DEFAULT);

// 3. طباعة الناتج
echo "" . $hashed_password;
?>


<!-- 

INSERT INTO `users` (`id`, `username`, `password`, `created_at`) VALUES 
(NULL, 'admin', '$2y$10$s88RMzkYJBKZSWjXRkY7z.KpZkyeYbm6en1M9ViGhkvoHFUveQ6na', CURRENT_TIMESTAMP());

-->