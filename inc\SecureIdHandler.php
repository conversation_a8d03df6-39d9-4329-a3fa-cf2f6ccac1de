<?php
class SecureIdHandler {
    private static $encryption_key;
    private static $hmac_key;
    public static function init() {
        // استخدام متغيرات البيئة مع قيم افتراضية آمنة للتطوير فقط
        self::$encryption_key = getenv('ENCRYPTION_KEY') ?: 'default_encryption_key_change_in_production_32bytes!';
        self::$hmac_key = getenv('HMAC_KEY') ?: 'default_hmac_key_change_in_production_32bytes!';

        // التأكد من أن المفاتيح بطول 32 بايت (256 بت)
        if (strlen(self::$encryption_key) < 32) {
            self::$encryption_key = hash('sha256', self::$encryption_key, true);
        }

        if (strlen(self::$hmac_key) < 32) {
            self::$hmac_key = hash('sha256', self::$hmac_key, true);
        }
    }

    /**
     * تشفير الـ ID إلى سلسلة طويلة وآمنة
     * 
     * @param int $id الـ ID الأصلي
     * @return string السلسلة المشفرة الطويلة
     */
    public static function encryptId($id) {
        if (!self::$encryption_key) {
            self::init();
        }

        // توليد IV عشوائي
        $iv = random_bytes(16);

        // تشفير الـ ID
        $encrypted = openssl_encrypt(
            $id, 
            'aes-256-cbc', 
            self::$encryption_key, 
            OPENSSL_RAW_DATA, 
            $iv
        );

        // إنشاء HMAC للتحقق من التكامل
        $hmac = hash_hmac('sha256', $iv . $encrypted, self::$hmac_key, true);

        // دمج IV + البيانات المشفرة + HMAC
        $combined = $iv . $encrypted . $hmac;

        // تحويل إلى base64url (آمن للروابط)
        return rtrim(strtr(base64_encode($combined), '+/', '-_'), '=');
    }

    /**
     * فك تشفير السلسلة الطويلة لاسترداد الـ ID الأصلي
     * 
     * @param string $encryptedId السلسلة المشفرة الطويلة
     * @return int|false الـ ID الأصلي أو false في حال الفشل
     */
    public static function decryptId($encryptedId) {
        if (!self::$encryption_key) {
            self::init();
        }

        // تحويل من base64url إلى base64 عادي
        $base64 = strtr($encryptedId, '-_', '+/');

        // إضافة المساواة إذا لزم الأمر
        if (strlen($base64) % 4) {
            $base64 .= str_repeat('=', 4 - (strlen($base64) % 4));
        }

        // فك تشفير base64
        $combined = base64_decode($base64);

        // التحقق من الطول (يجب أن يكون على الأقل 16 بايت للـ IV + 1 بايت للبيانات + 32 بايت للـ HMAC)
        if (strlen($combined) < 49) {
            return false;
        }

        // استخراج المكونات
        $iv = substr($combined, 0, 16);
        $encrypted = substr($combined, 16, -32);
        $hmac = substr($combined, -32);

        // التحقق من HMAC
        $calculatedHmac = hash_hmac('sha256', $iv . $encrypted, self::$hmac_key, true);
        if (!hash_equals($hmac, $calculatedHmac)) {
            return false; // HMAC غير صالح، البيانات قد تم العبث بها
        }

        // فك تشفير البيانات
        $decrypted = openssl_decrypt(
            $encrypted, 
            'aes-256-cbc', 
            self::$encryption_key, 
            OPENSSL_RAW_DATA, 
            $iv
        );

        if ($decrypted === false) {
            return false;
        }

        // تحويل إلى عدد صحيح
        return (string)$decrypted;
    }

    /**
     * إنشاء رابط آمن لصفحة int-us.php
     * 
     * @param int $id الـ ID الأصلي
     * @return string الرابط الآمن
     */
    public static function createIntUsLink($id) {
        $encryptedId = self::encryptId($id);
        return "int/int-us.php?id=" . urlencode($encryptedId);
    }
}
?>