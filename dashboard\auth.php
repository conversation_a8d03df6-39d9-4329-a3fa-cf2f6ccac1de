<?php
// بدء الجلسة
session_start();

// إعادة إنشاء معرف الجلسة لمنع هجمات تثبيت الجلسة
if (!isset($_SESSION['initiated'])) {
    session_regenerate_id();
    $_SESSION['initiated'] = true;
}

// التحقق من وجود جلسة مسجلة للمدير
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    // تسجيل محاولة الوصول غير المصرح به
    logUnauthorizedAttempt();

    // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
    header('Location: admin_login.php');
    exit;
}

// التحقق من انتهاء صلاحية الجلسة (30 دقيقة)
$max_lifetime = 1800; // 30 دقيقة بالثواني
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $max_lifetime)) {
    // انتهت صلاحية الجلسة
    session_unset();
    session_destroy();
    header('Location: admin_login.php?timeout=1');
    exit;
}

// تحديث وقت آخر نشاط
$_SESSION['last_activity'] = time();

// دالة لتسجيل محاولات الوصول غير المصرح بها
function logUnauthorizedAttempt() {
    $log_file = '../logs/access_attempts.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $requested_page = $_SERVER['REQUEST_URI'] ?? 'Unknown';

    $log_entry = "[$timestamp] IP: $ip | Page: $requested_page | User-Agent: $user_agent
";

    // إنشاء مجلد السجلات إذا لم يكن موجودًا
    if (!file_exists(dirname($log_file))) {
        mkdir(dirname($log_file), 0755, true);
    }

    // كتابة السجل
    file_put_contents($log_file, $log_entry, FILE_APPEND);

    // حظر IP بعد 5 محاولات فاشلة في 5 دقائق
    checkAndBlockIP($ip);
}

// دالة للتحقق من حظر IP
function checkAndBlockIP($ip) {
    $log_file = '../logs/access_attempts.log';
    $current_time = time();
    $attempts = 0;

    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        $lines = explode("
", trim($log_content));

        // عد المحاولات في آخر 5 دقائق
        foreach ($lines as $line) {
            if (strpos($line, "IP: $ip") !== false) {
                preg_match('/\[(.*?)\]/', $line, $matches);
                if (isset($matches[1])) {
                    $log_time = strtotime($matches[1]);
                    if (($current_time - $log_time) <= 300) { // 5 دقائق
                        $attempts++;
                    }
                }
            }
        }

        // إذا كان هناك أكثر من 5 محاولات، قم بحظر الـ IP
        if ($attempts > 5) {
            $htaccess_file = '../.htaccess';
            $rule = "Deny from $ip
";

            // تحقق مما إذا كان الـ IP محظورًا بالفعل
            if (file_exists($htaccess_file)) {
                $htaccess_content = file_get_contents($htaccess_file);
                if (strpos($htaccess_content, $rule) === false) {
                    // إضافة قاعدة الحظر
                    file_put_contents($htaccess_file, $rule, FILE_APPEND);
                }
            } else {
                // إنشاء ملف .htaccess جديد مع قاعدة الحظر
                file_put_contents($htaccess_file, $rule);
            }
        }
    }
}

// توليد CSRF token إذا لم يكن موجودًا
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// دالة للتحقق من CSRF token
function verifyCSRFToken($token) {
    if (isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token)) {
        return true;
    }
    return false;
}
?>