<?php
require_once 'db_connect.php';
require_once 'inc/SecureIdHandler.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(["success" => false, "message" => "طريقة الطلب غير مسموحة"]);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['id']) || empty($input['id'])) {
    echo json_encode(["success" => false, "message" => "لم يتم إرسال المعرف"]);
    exit;
}

try {
    $encryptedId = $input['id'];
    $decrypted_id = SecureIdHandler::decryptId($encryptedId);

    if ($decrypted_id === false) {
        echo json_encode(["success" => false, "message" => "معرف مشفر غير صالح"]);
        exit;
    }

    // 1. جلب الحالة الحالية من قاعدة البيانات (استخدم is_active وليس status)
    $stmt = $pdo->prepare("SELECT is_active FROM certificates WHERE id_number = ? LIMIT 1");
    $stmt->execute([$decrypted_id]);
    $cert = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$cert) {
        echo json_encode(["success" => false, "message" => "الشهادة غير موجودة"]);
        exit;
    }

    // 2. عكس الحالة
    $currentStatus = (int)$cert['is_active'];
    $newStatus = $currentStatus === 1 ? 0 : 1;

    // 3. التحديث
    $update = $pdo->prepare("UPDATE certificates SET is_active = ? WHERE id_number = ?");
    $result = $update->execute([$newStatus, $decrypted_id]);

    if ($result) {
        echo json_encode([
            "success" => true,
            "newStatus" => $newStatus,
            "message" => $newStatus === 1 ? "تم التفعيل" : "تم التعطيل"
        ]);
    } else {
        echo json_encode(["success" => false, "message" => "فشل تحديث الحالة"]);
    }

} catch (Exception $e) {
    error_log("Toggle Status Error: " . $e->getMessage());
    echo json_encode(["success" => false, "message" => "حدث خطأ داخلي"]);
}
?>