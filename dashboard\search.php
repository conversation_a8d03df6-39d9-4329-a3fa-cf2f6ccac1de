<?php
require_once '../db_connect.php';
require_once '../inc/SecureIdHandler.php';

// تعيين رأس الاستجابة كـ JSON
header('Content-Type: application/json; charset=UTF-8');

// التحقق من وجود مصطلح البحث
if (!isset($_GET['q']) || empty(trim($_GET['q']))) {
    echo json_encode(['error' => 'يرجى إدخال مصطلح بحث']);
    exit;
}

// إعدادات الترقيم
$records_per_page = 10;
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

$searchTerm = trim($_GET['q']);
$results = [];

try {
    // بناء استعلام البحث
    $sql = "SELECT id, person_name, id_number, nationality, job, cert_number, 
                   issue_date_hijri, expire_date_hijri, is_active 
            FROM certificates WHERE ";
    $countSql = "SELECT COUNT(*) FROM certificates WHERE ";
    $params = [];

    // التحقق من نوع البحث
    if (mb_strlen($searchTerm) === 1) {
        // إذا كان حرفًا واحدًا، ابحث في بداية الاسم
        $sql .= "person_name LIKE :search_term ";
        $countSql .= "person_name LIKE :search_term ";
        $params[':search_term'] = $searchTerm . '%';
    } else {
        // إذا كان أكثر من حرف، ابحث في أي مكان في الاسم أو رقم الهوية
        $sql .= "(person_name LIKE :search_term1 OR id_number LIKE :search_term2) ";
        $countSql .= "(person_name LIKE :search_term1 OR id_number LIKE :search_term2) ";
        $params[':search_term1'] = '%' . $searchTerm . '%';
        $params[':search_term2'] = '%' . $searchTerm . '%';
    }

    // إضافة الترتيب والحدود
    $sql .= "ORDER BY person_name ASC LIMIT :offset, :limit";

    // تنفيذ استعلام العدّ
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total_records = (int)$countStmt->fetchColumn();
    $total_pages = ceil($total_records / $records_per_page);

    // تنفيذ استعلام البيانات
    $stmt = $pdo->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $records_per_page, PDO::PARAM_INT);
    $stmt->execute();

    $certificates = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // تجهيز النتائج
    foreach ($certificates as $cert) {
        $encryptedId = SecureIdHandler::encryptId($cert['id_number']);
        $results[] = [
            'id' => $cert['id'],
            'encrypted_id' => $encryptedId,
            'person_name' => $cert['person_name'],
            'id_number' => $cert['id_number'],
            'nationality' => $cert['nationality'],
            'job' => $cert['job'],
            'cert_number' => $cert['cert_number'],
            'issue_date_hijri' => $cert['issue_date_hijri'],
            'expire_date_hijri' => $cert['expire_date_hijri'],
            'is_active' => (int)$cert['is_active'] // ✅ تمت إضافته هنا
        ];
    }

    echo json_encode([
        'success' => true,
        'data' => $results,
        'pagination' => [
            'total_records' => $total_records,
            'total_pages' => $total_pages,
            'current_page' => $page,
            'records_per_page' => $records_per_page
        ]
    ]);

} catch (PDOException $e) {
    echo json_encode(['error' => 'خطأ في البحث: ' . $e->getMessage()]);
}
