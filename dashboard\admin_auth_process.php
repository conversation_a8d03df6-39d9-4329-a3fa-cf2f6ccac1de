<?php
// بدء الجلسة
session_start();

// التحقق من طلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: admin_login.php');
    exit;
}

// التحقق من CSRF token
if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    die('طلب غير صالح');
}

// الحصول على بيانات النموذج وتنقيتها
$username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_STRING);
$password = $_POST['password'];

// بيانات الاعتماد الافتراضية (يجب تغييرها في بيئة الإنتاج)
// في بيئة الإنتاج، يجب تخزين كلمات المرور المشفرة في قاعدة البيانات
$valid_username = 'admin'; // غير هذا الاسم
$valid_password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'; // password

// التحقق من بيانات الاعتماد
if ($username === $valid_username && password_verify($password, $valid_password_hash)) {
    // تسجيل الدخول بنجاح
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_username'] = $username;
    $_SESSION['last_activity'] = time();

    // إعادة إنشاء معرف الجلسة لمنع هجمات تثبيت الجلسة
    session_regenerate_id(true);

    // تسجيل الدخول بنجاح - التوجيه للوحة التحكم
    header('Location: list.php');
    exit;
} else {
    // فشل تسجيل الدخول
    $error = "اسم المستخدم أو كلمة المرور غير صحيحة";

    // تسجيل محاولة الدخول الفاشلة
    logFailedAttempt($username);

    // إعادة التوجيه لصفحة تسجيل الدخول مع رسالة الخطأ
    header('Location: admin_login.php?error=' . urlencode($error));
    exit;
}

// دالة لتسجيل محاولات الدخول الفاشلة
function logFailedAttempt($username) {
    $log_file = '../logs/login_attempts.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'];

    $log_entry = "[$timestamp] Failed login attempt | Username: $username | IP: $ip
";

    // إنشاء مجلد السجلات إذا لم يكن موجودًا
    if (!file_exists(dirname($log_file))) {
        mkdir(dirname($log_file), 0755, true);
    }

    // كتابة السجل
    file_put_contents($log_file, $log_entry, FILE_APPEND);
}
?>