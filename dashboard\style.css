:root {
  --main-color: #0d6f6d;
  --second-color: #83bd48;
  --text-color: #787676;
  --text-footer: #115a68;
  --light-border: #78767621;
  --details-text-color: #333333;

  --font-weight-value: 600;
  --font-size-value: 0.813rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

@font-face {
  font-family: 'NotoKufiArabic';
  src: url('fonts/NotoKufiArabic-Regular.woff2') format('woff2'),
       url('fonts/NotoKufiArabic-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap; /* يضمن ظهور النص حتى لو لم يتم تحميل الخط بعد */
}



@font-face {
  font-family: 'tanseekarabic';
  src: url('tanseek-modern-pro-arabic-light.ttf') format('truetype');
  font-weight: normal;
 /* يضمن ظهور النص حتى لو لم يتم تحميل الخط بعد */
}



body {
  font-family: 'NotoKufiArabic', sans-serif;
  background-color: #282828;
  font-size: 0.875rem;
}

h6 {
  font-size: 11px;
  font-weight: 600;
}

.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.h-72 {
  height: 72%;
}
.grid {
  display: grid;
}
.bg-gray {
  background-color: #e0e3e2;
}
.space-between {
  justify-content: space-between;
}
.space-around {
  justify-content: space-around;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
@font-face {
  font-family: 'Amiri'; /* اسم الخط الذي ستستخدمه في CSS */
  src: url('../fonts/Amiri-Regular.woff2') format('woff2'),
       url('../fonts/Amiri-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');


@font-face {
  font-family: 'Scheherazade';
  src: url('../fonts/Scheherazade-Regular.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'extralight';
  src: url('fonts/cairo-extralight.ttf') format('truetype');
  font-weight: normal;

}



 
.name {
  font-size: 24px;
  font-weight: 900;
font-family: 'extralight', serif;  
color: #0e7270;
}

.max-w-full {
  max-width: 100%;
}
.max-h-full {
  max-height: 100%;
}
.w-285 {
  width: 290px;
}
.w-245 {
  width: 245px;
}

.text-center {
  text-align: center;
}
.text-30 {
  font-size: 25px;
}
.p-8 {
  padding: 8px;
}
.p-10 {
  padding: 10px;
}

.p-6 {
  padding: 6px;
}
.p-4 {
  padding: 4px;
}
.pb-60 {
  padding-bottom: 60px;
}
.px-8 {
  padding-right: 8px;
  padding-left: 8px;
}
.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}
.rounded-6 {
  border-radius: 6px;
}
.rounded-8 {
  border-radius: 8px;
}
.text-decoration {
  text-decoration: none;
}
.bg-white {
  background-color: white;
}

.text-red {
  color: red !important;
}

.text-white {
  color: white !important;
}

.text-right {
  text-align: right !important;
}
.dir-rtl {
  direction: rtl !important;
}
.grid-re-1fr {
  grid-template-columns: repeat(2, 1fr);
}

.flex-1 {
  flex: 1;
}

.mt-30 {
  margin-top: 30px;
}
.mt-40 {
  margin-top: 40px;
}
.mb-30 {
  margin-bottom: 30px;
}
.mb-60 {
  margin-bottom: 60px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mb-10 {
  margin-bottom: 10px;
}
.mt-10 {
  margin-top: 10px;
}
.-mt-10 {
  margin-top: -10px;
}
.mb-30 {
  margin-bottom: 30px;
}
.mr-30 {
  margin-right: 30px;
}
.mr-15 {
  margin-right: 15px;
}
.mr-12 {
  margin-right: 24px;
}
.ml-15 {
  margin-left: 15px;
}
.ml-17 {
  margin-left: 17px;
}
.gap-30 {
  gap: 30px;
}
.gap-26 {
  gap: 26px;
}
.gap-35 {
  gap: 35px;
}
.gap-20 {
  gap: 20px;
}
.gap-15 {
  gap: 15px;
}
.gap-10 {
  gap: 10px;
}
.gap-8 {
  gap: 8px;
}
.gap-5 {
  gap: 5px;
}
.gap-2 {
  gap: 2px;
}
.gap-3 {
  gap: 3px;
}
.text-details-color {
  color: var(--details-text-color);
}
.flex-column {
  flex-direction: column;
}

.btn-div {
  text-align: center;
}

.download-btn {
  display: inline-block;
  margin: 0 0 1rem 0;
  padding: 0.5rem 1rem;
  background: #0e7270;
  color: #fff;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 600;
}
.download-btn:hover {
  opacity: 0.92;
}

#certificate {
  margin: 0;
  padding: 0;
  background-color: transparent;
  box-shadow: none;
  overflow: hidden; /* للتأكد من عدم ظهور أي محتوى زائد */
}


.container {
  position: relative;
  max-width: 51.125rem;
  margin: 0 auto;
  background-color: #f0f0f0;
  overflow: hidden;
}

.container.top::after {
  content: "";
  position: absolute;
  top: -25px;
  right: -49.5%;
  bottom: 0;
  width: 100%;
  background: url(./Imgs/nakhla.png) no-repeat center center;
  opacity: 0.1;
  background-size: auto 100%;
  pointer-events: none;
  height: 146%;
}

.container.bottom::before {
  content: "";
  position: absolute;
  top: -34px;
  left: -49.5%;
  bottom: 0;
  width: 100%;
  background: url(./Imgs/nakhla.png) no-repeat center center;
  opacity: 0.2;
  background-size: auto 100%;
  pointer-events: none;
  height: 146%;
}

header {
  gap: 140px;
}

.div-img {
  width: 80px;
  position: relative;
}
.div-img img {
  z-index: 100;
}
.sprtot {
    border-right: 1px solid #80808059;
    border-left: 1px solid #80808059;
    /* margin: 0 5px; */
    height: 74px;
}

.details {
  padding: 0 17px;
}
.details h1 {
  color: #0e7270;
}
.text-55 {
  font-size: 42px;
  font-weight: 500;
  width: 360px;
  text-align: right;
  line-height: 1.6em;
}
.border-green {
  border: 1px solid var(--second-color);
}
.text-contact {
  color: #6e6e6e;
}

.left-photos {
  width: 166px;
}
.left-photos div {
  height: 176px;
}
.shapeInput {
  padding: 5px 5px;
    transform: scaleY(1.0);

}

.contact-row {
  position: relative;
  flex-wrap: nowrap;
  border-right: 1.5px solid var(--main-color);
  border-bottom: 1.5px solid var(--main-color);
  border-radius: 0 0 30px;
  margin: 10px 16px 0;
  padding-bottom: 5px;
  margin-bottom: 2.5px;
  gap: 45px;
}

/* أيقونة الهاتف (زي ما هي) */
.contact-row .div-has-iphone {
  width: 33px;
  height: 33px;
  border: 2px solid var(--second-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.contact-row .div-has-iphone i {
  font-size: 18px;
  color: var(--second-color);
}

.care-span {
  font-size: 10px;
  font-weight: 700;
}

/* باقي الأيقونات */
.parent-icon {
  width: 23px;
  height: 23px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--second-color);
  border-radius: 50%;
}
.parent-icon i {
  font-size: 11.5px;
  color: var(--second-color);
}

/* النصوص بجانب الأيقونات */
.text-footer {
  color: var(--text-footer);
  font-weight: 600;
  font-size: 15px;
  white-space: nowrap;
}

/* فاصل الهاتف */
.details-contact {
  padding-right: 6px;
  color: #6e6e6e;
  border-right: 1px dashed var(--text-footer);
}

.container.bottom {
  background-color: #4a8484;
  padding: 5px 18px;
}

.content-text {
  padding: 0 32px 45px 20px;
}

.last-div {
  width: 100%;
  height: 25px;
}

.footer-text {
  font-size: 23px;
  line-height: 1.6em;
  direction: rtl;
  font-weight: 400;
}

.container.top::before {
  content: "";
  position: absolute;
  inset: 0% 0% 0% 50%;
  pointer-events: none;
  z-index: 0;
  background: repeating-radial-gradient(
    circle at -62% 46%,
    /* مركز الأقواس: أقصى يمين فوق */ rgb(255 255 255 / 25%) 0 3px,
    /* الخط أسمك (3px) وأوضح (45%) */ transparent 5px 25px
      /* المسافة بين الأقواس أوسع */
  );
  transform: rotate(52deg);
  width: 100%;
  height: 123%;
}







@font-face {
  font-family: 'myTitle';
  src: url('fonts/tanseek-modern-pro-arabic-medium.ttf') format('truetype');
  font-weight: normal;
 /* يضمن ظهور النص حتى لو لم يتم تحميل الخط بعد */
}
/* 
@font-face {
  font-family: 'Somar';
  src: url('fonts/cairo-bold.ttf') format('truetype'); 
  font-style: normal;
} */



/* @font-face {
  font-family: 'cairo';
  src: url('fonts/cairo-bold.ttf') format('truetype'); 
  font-style: normal;
} */


/* @font-face {
  font-family: 'semibold';
  src: url('fonts/cairo-semibold.ttf') format('truetype'); 
  font-style: normal;
} */

/* @font-face {
  font-family: 'modern';
  src: url('fonts/tanseek-modern-pro-arabic-light.ttf') format('truetype'); 
 
  font-style: normal;
} */

header .div-tilte {
    background-color: #0e7270;
    padding: 9px 43px;
    margin-top: -28px;
    transform: scaleY(1.2);
    transform: scaleX(1.6);
    margin-bottom: -28px;
    margin-left: 32px;
}
 
.div-tilte h1 {
      font-family: 'myTitle' !important;
    transform: scaleY(1.5);
    font-size: 42px;
    font-weight: 100;
    margin-bottom: 9px;
    margin-left: 50px;
}


.shapeInput h4 {
      font-family: 'extralight' !important;

  /* color: var(--details-text-color); */
  font-size: 15px;
  font-weight: 600;
  line-height: 26px;
  color: #000;
}
.shapeLabel {
      font-family: 'cairo' !important;

  margin-right: 4px;
  margin-bottom: 4px;
  font-size: 15px;
  font-weight: 600;
  /* transform: scaleY(1.2);
     transform: scaleX(1.2);
     margin-right: 36px; */

     
}
.w-245 {
    width: 254px;
    height: 93px;
}

.max-w-full {
    max-width: 100%;
    padding: 3px;
}

.left-photos div {
    height: 170px;
}

header .max-w-full {
    max-width: 119%;
    padding: 3px;
    margin-top: 12px;
}